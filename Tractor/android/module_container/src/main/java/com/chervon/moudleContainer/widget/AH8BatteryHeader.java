package com.chervon.moudleContainer.widget;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.LinearGradient;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.RadialGradient;
import android.graphics.Rect;
import android.graphics.RectF;
import android.graphics.Shader;
import android.graphics.SweepGradient;
import android.graphics.Typeface;
import android.text.Layout;
import android.text.StaticLayout;
import android.text.TextPaint;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.MotionEvent;
import android.view.View;

import androidx.annotation.Nullable;

import com.blankj.utilcode.util.ScreenUtils;
import com.chervon.moudleContainer.R;
import com.chervon.moudleContainer.panel.data.entity.CapacityModel;
import java.util.ArrayList;
import java.util.List;


/**
 * @Author: hyman
 * @desc 8AH自定义头部View
 * @CreateDate: 2023/9/13
 * @UpdateDate: 2023/9/13
 */
public class AH8BatteryHeader extends View {

  protected static final String TAG = "AH8BatteryHeader";
  //最外圆半径
  private final int outR = dpToPx(283);
  //中间圆半径
  private final int middleR = dpToPx(248);
  //中间圆外环圈
  private final int middle_OutR = dpToPx(18);
  private final int inR = dpToPx(180);
  //充电左上角图标
  protected Rect statusImageRect;
  protected boolean showTips = false;
  private List<CapacityModel>  capacityModelArrayList = new ArrayList<>();


  public AH8BatteryHeader(Context context) {
    super(context);
  }


  public AH8BatteryHeader(Context context, @Nullable AttributeSet attrs) {
    super(context, attrs);
  }

  public AH8BatteryHeader(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
    super(context, attrs, defStyleAttr);
  }

  @Override
  protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
    super.onMeasure(widthMeasureSpec, heightMeasureSpec);
  }

  @SuppressLint("DrawAllocation")
  @Override
  protected void onDraw(Canvas canvas) {
    super.onDraw(canvas);
    int width = getWidth();
    int height = getHeight();
    canvas.rotate(-90, width / 2, height / 2);

    if (TextUtils.isEmpty(status)) {
      return;
    }

    switch (status) {
      case STANDBY:
        drawStandby(canvas, width, height);
        break;
      case FULLYCHARGED:
        drawFullyCharged(canvas, width, height);
        break;
      case DISCHARGING:
        drawDischargingCharged(canvas, width, height);
        break;
      case CHARGINGFAILED:

        if (value == -1) {
          drawChargeErrorNoData(canvas, width, height);
        } else {
          drawChargeErrorWithData(canvas, width, height);
        }

        break;
      case CHARGING:
        //充电中状态
        drawCharging(canvas, width, height);
        break;
      case OVERHEAT:
        //过温
        drawChargeOverTemp(canvas, width, height);
        break;
      case ERROR:
        if (value == -1) {
          drawChargeErrorNoData(canvas, width, height);
        } else {
          drawChargeErrorWithData(canvas, width, height);
        }

        break;
      case EMPTY:
        //未充电状态
       drawChargeNormal(canvas, width, height);
        break;
      default:
        //未充电状态
        drawChargeNormal(canvas, width, height);
        break;
    }

  }


  /**
   * 充电中状态
   *
   * @param canvas
   * @param parentWidth
   * @param parentHeight
   */
  private void drawStandby(Canvas canvas, int parentWidth, int parentHeight) {

    if (value == -1) {
      return;
    }
    float endAngle = 3.6f * value;

    float[] position = new float[2];
    position[0] = 0f;
    position[1] = ((endAngle) * 1.0f) / 360f;

    Paint paint = new Paint();
    paint.setAntiAlias(true);
    paint.setAlpha(51);
    RadialGradient mRadialGradient = new RadialGradient(parentWidth / 2,
      parentHeight / 2,
      outR,
      getResources().getColor(R.color.middle_color_bord_sweepgradient_end),
      getResources().getColor(R.color.out_middle_color_sweepgradient_charging_start),
      Shader.TileMode.MIRROR);
    paint.setShader(mRadialGradient);
    canvas.drawCircle(parentWidth / 2, parentHeight / 2, outR, paint);


    Paint paint2 = new Paint();
    paint2.setAntiAlias(true);
    LinearGradient linearGradient = new LinearGradient(
      0,
      parentHeight / 2,
      parentWidth,
      parentHeight,
      getResources().getColor(R.color.out_middle_color_sweepgradient_charging_middle_start),
      getResources().getColor(R.color.out_middle_color_sweepgradient_charging_middle_end),
      Shader.TileMode.CLAMP);
    paint2.setShader(linearGradient);
    canvas.drawCircle(parentWidth / 2, parentHeight / 2, middleR, paint2);

    //画充电保护圆环
    drawChargeIsOptimized(canvas, parentWidth, parentHeight);

    Paint paint3 = new Paint();
    paint3.setAntiAlias(true);
    int[] SECTION_COLORS = {
      getResources().getColor(R.color.out_middle_color_sweepgradient_charging_number_middle_end),
      getResources().getColor(R.color.out_middle_color_sweepgradient_charging_number_middle_start)

    };
    SweepGradient mSweepGradient = new SweepGradient(
      parentWidth / 2,
      parentHeight / 2,
      SECTION_COLORS,
      position);
    paint3.setShader(mSweepGradient);
    RectF oval = new RectF(
      parentWidth / 2 - middleR,
      parentHeight / 2 - middleR,
      parentWidth / 2 + middleR,
      parentHeight / 2 + middleR);
    canvas.drawArc(oval, 0, endAngle, true, paint3);
    //   画内圆
    Paint paint4 = new Paint();
    paint4.setColor(getResources().getColor(R.color.white));
    paint4.setAntiAlias(true);
    canvas.drawCircle(parentWidth / 2, parentHeight / 2, inR, paint4);


    Paint paint5 = new Paint();
    paint5.setAntiAlias(true);
    paint5.setStyle(Paint.Style.STROKE);
    paint5.setStrokeWidth(middle_OutR);
    int[] SECTION_COLORS5 = {
      getResources().getColor(R.color.out_middle_color_sweepgradient_charging_number_middle_out_end),
      getResources().getColor(R.color.out_middle_color_sweepgradient_charging_number_middle_out_start)
    };
    SweepGradient mSweepGradient5 = new SweepGradient(
      parentWidth / 2,
      parentHeight / 2,
      SECTION_COLORS5,
      position);
    paint5.setShader(mSweepGradient5);
    RectF oval5 = new RectF(
      parentWidth / 2 - middleR + middle_OutR - middle_OutR / 2,
      parentHeight / 2 - middleR + middle_OutR - middle_OutR / 2,
      parentWidth / 2 + middleR - middle_OutR + middle_OutR / 2,
      parentHeight / 2 + middleR - middle_OutR + middle_OutR / 2);
    canvas.drawArc(oval5, 0, endAngle, false, paint5);

    drawStarLine(canvas, parentWidth, parentHeight, getResources().getColor(R.color.out_middle_color_sweepgradient_charging_number_middle_out_start));

    drawChargeNumber(canvas, parentWidth, parentHeight);
    //开启充电保护

    drawWarningLine(canvas, parentWidth, parentHeight, 234);

    drawChargeWornImage(canvas, parentWidth, parentHeight);

    drawLeftTopImage(canvas, parentWidth, parentHeight);


  }

  /**
   * 充电中状态
   *
   * @param canvas
   * @param parentWidth
   * @param parentHeight
   */
  private void drawCharging(Canvas canvas, int parentWidth, int parentHeight) {
    if (value == -1) {
      return;
    }
    float endAngle = 3.6f * value;

    float[] position = new float[2];
    position[0] = 0f;
    position[1] = ((endAngle) * 1.0f) / 360f;

    Paint paint = new Paint();
    paint.setAntiAlias(true);
    paint.setAlpha(51);
    RadialGradient mRadialGradient = new RadialGradient(parentWidth / 2,
      parentHeight / 2,
      outR,
      getResources().getColor(R.color.middle_color_bord_sweepgradient_end),
      getResources().getColor(R.color.out_middle_color_sweepgradient_charging_start),
      Shader.TileMode.MIRROR);
    paint.setShader(mRadialGradient);
    canvas.drawCircle(parentWidth / 2, parentHeight / 2, outR, paint);


    Paint paint2 = new Paint();
    paint2.setAntiAlias(true);
    LinearGradient linearGradient = new LinearGradient(
      0,
      parentHeight / 2,
      parentWidth,
      parentHeight,
      getResources().getColor(R.color.out_middle_color_sweepgradient_charging_middle_start),
      getResources().getColor(R.color.out_middle_color_sweepgradient_charging_middle_end),
      Shader.TileMode.CLAMP);
    paint2.setShader(linearGradient);
    canvas.drawCircle(parentWidth / 2, parentHeight / 2, middleR, paint2);

    //画充电保护圆环
    drawChargeIsOptimized(canvas, parentWidth, parentHeight);

    Paint paint3 = new Paint();
    paint3.setAntiAlias(true);
    int[] SECTION_COLORS = {
      getResources().getColor(R.color.out_middle_color_sweepgradient_charging_number_middle_end),
      getResources().getColor(R.color.out_middle_color_sweepgradient_charging_number_middle_start)

    };
    SweepGradient mSweepGradient = new SweepGradient(
      parentWidth / 2,
      parentHeight / 2,
      SECTION_COLORS,
      position);
    paint3.setShader(mSweepGradient);
    RectF oval = new RectF(
      parentWidth / 2 - middleR,
      parentHeight / 2 - middleR,
      parentWidth / 2 + middleR,
      parentHeight / 2 + middleR);
    canvas.drawArc(oval, 0, endAngle, true, paint3);



    //   画内圆
    Paint paint4 = new Paint();
    paint4.setColor(getResources().getColor(R.color.white));
    paint4.setAntiAlias(true);
    canvas.drawCircle(parentWidth / 2, parentHeight / 2, inR, paint4);


    Paint paint5 = new Paint();
    paint5.setAntiAlias(true);
    paint5.setStyle(Paint.Style.STROKE);
    paint5.setStrokeWidth(middle_OutR);
    int[] SECTION_COLORS5 = {
      getResources().getColor(R.color.out_middle_color_sweepgradient_charging_number_middle_out_end),
      getResources().getColor(R.color.out_middle_color_sweepgradient_charging_number_middle_out_start)

    };
    SweepGradient mSweepGradient5 = new SweepGradient(
      parentWidth / 2,
      parentHeight / 2,
      SECTION_COLORS5,
      position);
    paint5.setShader(mSweepGradient5);
    RectF oval5 = new RectF(
      parentWidth / 2 - middleR + middle_OutR - middle_OutR / 2,
      parentHeight / 2 - middleR + middle_OutR - middle_OutR / 2,
      parentWidth / 2 + middleR - middle_OutR + middle_OutR / 2,
      parentHeight / 2 + middleR - middle_OutR + middle_OutR / 2);
    canvas.drawArc(oval5, 0, endAngle, false, paint5);
    drawStarLine(canvas, parentWidth, parentHeight, getResources().getColor(R.color.out_middle_color_sweepgradient_charging_number_middle_out_start));
    drawChargeStatusInCircle(canvas, parentWidth, parentHeight, false);
    drawChargeNumber(canvas, parentWidth, parentHeight);
    //开启充电保护

    drawWarningLine(canvas, parentWidth, parentHeight, 234);

    drawChargeWornImage(canvas, parentWidth, parentHeight);

    drawLeftTopImage(canvas, parentWidth, parentHeight);


  }


  private void drawFullyCharged(Canvas canvas, int parentWidth, int parentHeight) {

    if (value == -1) {
      return;
    }


    if (value == 100) {
      drawFullyCharged2(canvas, parentWidth, parentHeight);
      return;
    }

    float endAngle = 3.6f * value;

    float[] position = new float[2];
    position[0] = 0f;
    position[1] = ((endAngle) * 1.0f) / 360f;

    Paint paint = new Paint();
    paint.setAntiAlias(true);
    paint.setAlpha(51);
    RadialGradient mRadialGradient = new RadialGradient(parentWidth / 2,
      parentHeight / 2,
      outR,
      getResources().getColor(R.color.middle_color_bord_sweepgradient_end),
      getResources().getColor(R.color.out_middle_color_sweepgradient_charging_start),
      Shader.TileMode.MIRROR);
    paint.setShader(mRadialGradient);
    canvas.drawCircle(parentWidth / 2, parentHeight / 2, outR, paint);


    Paint paint2 = new Paint();
    paint2.setAntiAlias(true);
    LinearGradient linearGradient = new LinearGradient(
      0,
      parentHeight / 2,
      parentWidth,
      parentHeight,
      getResources().getColor(R.color.out_middle_color_sweepgradient_charging_middle_start),
      getResources().getColor(R.color.out_middle_color_sweepgradient_charging_middle_end),
      Shader.TileMode.CLAMP);
    paint2.setShader(linearGradient);
    canvas.drawCircle(parentWidth / 2, parentHeight / 2, middleR, paint2);


    Paint paint3 = new Paint();
    paint3.setAntiAlias(true);
    int[] SECTION_COLORS = {
      getResources().getColor(R.color.out_middle_color_sweepgradient_charging_number_middle_end),
      getResources().getColor(R.color.out_middle_color_sweepgradient_charging_number_middle_start)

    };
    SweepGradient mSweepGradient = new SweepGradient(
      parentWidth / 2,
      parentHeight / 2,
      SECTION_COLORS,
      position);
    paint3.setShader(mSweepGradient);
    RectF oval = new RectF(
      parentWidth / 2 - middleR,
      parentHeight / 2 - middleR,
      parentWidth / 2 + middleR,
      parentHeight / 2 + middleR);
    canvas.drawArc(oval, 0, endAngle, true, paint3);
    //   画内圆
    Paint paint4 = new Paint();
    paint4.setColor(getResources().getColor(R.color.white));
    paint4.setAntiAlias(true);
    canvas.drawCircle(parentWidth / 2, parentHeight / 2, inR, paint4);


    Paint paint5 = new Paint();
    paint5.setAntiAlias(true);
    paint5.setStyle(Paint.Style.STROKE);
    paint5.setStrokeWidth(middle_OutR);
    int[] SECTION_COLORS5 = {
      getResources().getColor(R.color.out_middle_color_sweepgradient_charging_number_middle_out_end),
      getResources().getColor(R.color.out_middle_color_sweepgradient_charging_number_middle_out_start)

    };
    SweepGradient mSweepGradient5 = new SweepGradient(
      parentWidth / 2,
      parentHeight / 2,
      SECTION_COLORS5,
      position);
    paint5.setShader(mSweepGradient5);
    RectF oval5 = new RectF(
      parentWidth / 2 - middleR + middle_OutR - middle_OutR / 2,
      parentHeight / 2 - middleR + middle_OutR - middle_OutR / 2,
      parentWidth / 2 + middleR - middle_OutR + middle_OutR / 2,
      parentHeight / 2 + middleR - middle_OutR + middle_OutR / 2);
    canvas.drawArc(oval5, 0, endAngle, false, paint5);
    drawStarLine(canvas, parentWidth, parentHeight, getResources().getColor(R.color.out_middle_color_sweepgradient_charging_number_middle_out_start));
//    drawChargeStatusInCircle(canvas, parentWidth, parentHeight, false);
    drawChargeNumber(canvas, parentWidth, parentHeight);
    //开启充电保护

    drawWarningLine(canvas, parentWidth, parentHeight, 234);

    drawChargeWornImage(canvas, parentWidth, parentHeight);

    drawLeftTopImage(canvas, parentWidth, parentHeight);


  }

  private void drawDischargingCharged(Canvas canvas, int parentWidth, int parentHeight) {

    if (value == -1) {
      return;
    }
    float endAngle = 3.6f * value;

    float[] position = new float[2];
    position[0] = 0f;
    position[1] = ((endAngle) * 1.0f) / 360f;

    Paint paint = new Paint();
    paint.setAntiAlias(true);
    paint.setAlpha(51);
    RadialGradient mRadialGradient = new RadialGradient(parentWidth / 2,
            parentHeight / 2,
            outR,
            getResources().getColor(R.color.middle_color_bord_sweepgradient_end),
            getResources().getColor(R.color.out_middle_color_sweepgradient_charging_start),
            Shader.TileMode.MIRROR);
    paint.setShader(mRadialGradient);
    canvas.drawCircle(parentWidth / 2, parentHeight / 2, outR, paint);


    Paint paint2 = new Paint();
    paint2.setAntiAlias(true);
    LinearGradient linearGradient = new LinearGradient(
            0,
            parentHeight / 2,
            parentWidth,
            parentHeight,
            getResources().getColor(R.color.out_middle_color_sweepgradient_charging_middle_start),
            getResources().getColor(R.color.out_middle_color_sweepgradient_charging_middle_end),
            Shader.TileMode.CLAMP);
    paint2.setShader(linearGradient);
    canvas.drawCircle(parentWidth / 2, parentHeight / 2, middleR, paint2);


    //画充电保护圆环
    drawChargeIsOptimized(canvas, parentWidth, parentHeight);

    Paint paint3 = new Paint();
    paint3.setAntiAlias(true);
    int[] SECTION_COLORS = {
            getResources().getColor(R.color.out_middle_color_sweepgradient_charging_number_middle_end),
            getResources().getColor(R.color.out_middle_color_sweepgradient_charging_number_middle_start)

    };
    SweepGradient mSweepGradient = new SweepGradient(
            parentWidth / 2,
            parentHeight / 2,
            SECTION_COLORS,
            position);
    paint3.setShader(mSweepGradient);
    RectF oval = new RectF(
            parentWidth / 2 - middleR,
            parentHeight / 2 - middleR,
            parentWidth / 2 + middleR,
            parentHeight / 2 + middleR);
    canvas.drawArc(oval, 0, endAngle, true, paint3);
    //   画内圆
    Paint paint4 = new Paint();
    paint4.setColor(getResources().getColor(R.color.white));
    paint4.setAntiAlias(true);
    canvas.drawCircle(parentWidth / 2, parentHeight / 2, inR, paint4);


    Paint paint5 = new Paint();
    paint5.setAntiAlias(true);
    paint5.setStyle(Paint.Style.STROKE);
    paint5.setStrokeWidth(middle_OutR);
    int[] SECTION_COLORS5 = {
            getResources().getColor(R.color.out_middle_color_sweepgradient_charging_number_middle_out_end),
            getResources().getColor(R.color.out_middle_color_sweepgradient_charging_number_middle_out_start)
    };
    SweepGradient mSweepGradient5 = new SweepGradient(
            parentWidth / 2,
            parentHeight / 2,
            SECTION_COLORS5,
            position);
    paint5.setShader(mSweepGradient5);
    RectF oval5 = new RectF(
            parentWidth / 2 - middleR + middle_OutR - middle_OutR / 2,
            parentHeight / 2 - middleR + middle_OutR - middle_OutR / 2,
            parentWidth / 2 + middleR - middle_OutR + middle_OutR / 2,
            parentHeight / 2 + middleR - middle_OutR + middle_OutR / 2);
    canvas.drawArc(oval5, 0, endAngle, false, paint5);

    drawStarLine(canvas, parentWidth, parentHeight, getResources().getColor(R.color.out_middle_color_sweepgradient_charging_number_middle_out_start));

    drawChargeNumber(canvas, parentWidth, parentHeight);
    //开启充电保护

    drawWarningLine(canvas, parentWidth, parentHeight, 234);

    drawChargeWornImage(canvas, parentWidth, parentHeight);

    drawLeftTopImage(canvas, parentWidth, parentHeight);


  }


  private void drawFullyCharged2(Canvas canvas, int parentWidth, int parentHeight) {

    if (value == -1) {
      return;
    }


    float endAngle = 3.6f * value;

    float[] position = new float[2];
    position[0] = 0f;
    position[1] = ((endAngle) * 1.0f) / 360f;

    Paint paint = new Paint();
    paint.setAntiAlias(true);
    paint.setAlpha(51);
    RadialGradient mRadialGradient = new RadialGradient(parentWidth / 2,
      parentHeight / 2,
      outR,
      getResources().getColor(R.color.middle_color_bord_sweepgradient_end),
      getResources().getColor(R.color.out_middle_color_sweepgradient_charging_start),
      Shader.TileMode.MIRROR);
    paint.setShader(mRadialGradient);
    canvas.drawCircle(parentWidth / 2, parentHeight / 2, outR, paint);


    Paint paint2 = new Paint();
    paint2.setAntiAlias(true);
    paint2.setColor(getResources().getColor(R.color.fully_charge_100_out_middle));
    canvas.drawCircle(parentWidth / 2, parentHeight / 2, middleR, paint2);


    Paint paint3 = new Paint();
    paint2.setAntiAlias(true);
    paint3.setColor(getResources().getColor(R.color.fully_charge_100_in_middle));
    canvas.drawCircle(parentWidth / 2, parentHeight / 2, middleR - middle_OutR, paint3);

    //   画内圆
    Paint paint4 = new Paint();
    paint4.setColor(getResources().getColor(R.color.white));
    paint4.setAntiAlias(true);
    canvas.drawCircle(parentWidth / 2, parentHeight / 2, inR, paint4);

//    drawChargeStatusInCircle(canvas, parentWidth, parentHeight, false);
    drawChargeNumber(canvas, parentWidth, parentHeight);
    //开启充电保护

    drawWarningLine(canvas, parentWidth, parentHeight, 234);

    drawChargeWornImage(canvas, parentWidth, parentHeight);

    drawLeftTopImage(canvas, parentWidth, parentHeight);


  }

  /**
   * 电池包挂载未充电
   *
   * @param canvas
   * @param parentWidth
   * @param parentHeight
   */
  private void drawChargeNormal(Canvas canvas, int parentWidth, int parentHeight) {
    //画最外面圆环
    Paint paint1 = new Paint();
    paint1.setColor(getResources().getColor(R.color.out_out_color_uncharge_start));
    //设置空心半径

    //消除锯齿
    paint1.setAntiAlias(true);
    //设置透明度
    paint1.setAlpha(20);
    canvas.drawCircle(parentWidth / 2, parentHeight / 2, outR, paint1);
    //画中间圆形
    Paint paint2 = new Paint();
    //定义画笔颜色
    paint2.setColor(getResources().getColor(R.color.out_middle_bg_color_uncharge));
    //消除锯齿
    paint2.setAntiAlias(true);
    canvas.drawCircle(parentWidth / 2, parentHeight / 2, middleR, paint2);
    //画中间-外圆环
    Paint paint3 = new Paint();
    paint3.setColor(getResources().getColor(R.color.out_middle_bg_out_color_uncharge));
    paint3.setStyle(Paint.Style.STROKE);
    paint3.setStrokeWidth(middle_OutR);
    paint3.setAntiAlias(true);
    canvas.drawCircle(parentWidth / 2, parentHeight / 2, middleR, paint3);
    //   画内圆
    Paint paint4 = new Paint();
    paint4.setColor(getResources().getColor(R.color.white));
    paint4.setAntiAlias(true);
    canvas.drawCircle(parentWidth / 2, parentHeight / 2, inR, paint4);
    //画电量
    Paint text = new Paint();
    text.setColor(Color.BLACK);
    text.setTextAlign(Paint.Align.CENTER);
    text.setTypeface(Typeface.DEFAULT_BOLD);
    text.setTextSize(dpToPx(90));
    Matrix matrix = new Matrix();
    canvas.setMatrix(matrix);
    canvas.drawText("--", parentWidth / 2, parentHeight / 2 + inR / 5, text);

  }

  /**
   * 充电过温
   *
   * @param canvas
   * @param parentWidth
   * @param parentHeight
   */
  private void drawChargeOverTemp(Canvas canvas, int parentWidth, int parentHeight) {
    if (value == -1) {
      return;
    }

    if (value == 100) {
      drawChargeOverTempWithValue100(canvas, parentWidth, parentHeight);
      return;
    }

    float endAngle = 3.6f * value;
    float[] position = new float[2];
    position[0] = 0f;
    position[1] = ((endAngle) * 1.0f) / 360f;

    Paint paint = new Paint();
    paint.setAntiAlias(true);
    paint.setAlpha(15);
    RadialGradient mRadialGradient = new RadialGradient(parentWidth / 2,
      parentHeight / 2,
      outR,
      getResources().getColor(R.color.out_color_sweepgradient_over_temp_end),
      getResources().getColor(R.color.out_color_sweepgradient_over_temp_start),

      Shader.TileMode.MIRROR);
    paint.setShader(mRadialGradient);
    canvas.drawCircle(parentWidth / 2, parentHeight / 2, outR, paint);


    Paint paint2 = new Paint();
    paint2.setAntiAlias(true);
    LinearGradient linearGradient = new LinearGradient(
      parentWidth / 2 - outR / 2,
      parentHeight / 2 + outR / 2,
      parentWidth / 2 + outR,
      parentWidth / 2 - outR,
      getResources().getColor(R.color.out_color_sweepgradient_over_temp_bg_end),
      getResources().getColor(R.color.out_color_sweepgradient_over_temp_bg_start),
      Shader.TileMode.CLAMP);
    paint2.setShader(linearGradient);
    canvas.drawCircle(parentWidth / 2, parentHeight / 2, middleR, paint2);


    Paint paint3 = new Paint();
    paint3.setAntiAlias(true);
    int[] SECTION_COLORS = {getResources().getColor(R.color.out_color_sweepgradient_over_temp_ch_num_end),
      getResources().getColor(R.color.out_color_sweepgradient_over_temp_ch_num_start)

    };
    SweepGradient mSweepGradient = new SweepGradient(
      parentWidth / 2,
      parentHeight / 2,
      SECTION_COLORS,
      position);
    paint3.setShader(mSweepGradient);
    RectF oval = new RectF(
      parentWidth / 2 - middleR,
      parentHeight / 2 - middleR,
      parentWidth / 2 + middleR,
      parentHeight / 2 + middleR);

    canvas.drawArc(oval, 0, endAngle, true, paint3);

    //   画内圆
    Paint paint4 = new Paint();
    paint4.setColor(getResources().getColor(R.color.white));
    paint4.setAntiAlias(true);
    canvas.drawCircle(parentWidth / 2, parentHeight / 2, inR, paint4);

    Paint paint5 = new Paint();
    paint5.setAntiAlias(true);
    paint5.setStyle(Paint.Style.STROKE);
    paint5.setStrokeWidth(middle_OutR);
    int[] SECTION_COLORS5 = {
      getResources().getColor(R.color.out_middle_color_sweepgradient_over_temp_middle_out_start),
      getResources().getColor(R.color.out_middle_color_sweepgradient_over_temp_middle_out_end)
    };
    SweepGradient mSweepGradient5 = new SweepGradient(
      parentWidth / 2,
      parentHeight / 2,
      SECTION_COLORS5,
      position);
    paint5.setShader(mSweepGradient5);
    RectF oval5 = new RectF(
      parentWidth / 2 - middleR + middle_OutR - middle_OutR / 2,
      parentHeight / 2 - middleR + middle_OutR - middle_OutR / 2,
      parentWidth / 2 + middleR - middle_OutR + middle_OutR / 2,
      parentHeight / 2 + middleR - middle_OutR + middle_OutR / 2);
    canvas.drawArc(oval5, 0, endAngle, false, paint5);


    drawStarLine(canvas, parentWidth, parentHeight, getResources().getColor(R.color.out_color_sweepgradient_over_temp_start));
    drawChargeStatusInCircle(canvas, parentWidth, parentHeight, true);
    drawChargeNumber(canvas, parentWidth, parentHeight);
    drawLeftTopImage(canvas, parentWidth, parentHeight);
  }

  /**
   * 满电-充电过温
   *
   * @param canvas
   * @param parentWidth
   * @param parentHeight
   */
  private void drawChargeOverTempWithValue100(Canvas canvas, int parentWidth, int parentHeight) {
    if (value == -1) {
      return;
    }

    float endAngle = 3.6f * value;
    float[] position = new float[2];
    position[0] = 0f;
    position[1] = ((endAngle) * 1.0f) / 360f;

    Paint paint = new Paint();
    paint.setAntiAlias(true);
    paint.setAlpha(15);
    RadialGradient mRadialGradient = new RadialGradient(parentWidth / 2,
      parentHeight / 2,
      outR,
      getResources().getColor(R.color.out_color_sweepgradient_over_temp_end),
      getResources().getColor(R.color.out_color_sweepgradient_over_temp_start),

      Shader.TileMode.MIRROR);
    paint.setShader(mRadialGradient);
    canvas.drawCircle(parentWidth / 2, parentHeight / 2, outR, paint);


    Paint paint2 = new Paint();
    paint2.setAntiAlias(true);
    paint2.setColor(getResources().getColor(R.color.out_middle_color_over_temp_middle_out_bg));
    canvas.drawCircle(parentWidth / 2, parentHeight / 2, middleR, paint2);
    //   画内圆
    Paint paint4 = new Paint();
    paint4.setColor(getResources().getColor(R.color.white));
    paint4.setAntiAlias(true);
    canvas.drawCircle(parentWidth / 2, parentHeight / 2, inR, paint4);

    Paint paint5 = new Paint();
    paint5.setAntiAlias(true);
    paint5.setStyle(Paint.Style.STROKE);
    paint5.setStrokeWidth(middle_OutR);
    paint5.setColor(getResources().getColor(R.color.out_color_sweepgradient_over_temp_ch_num_start));
    RectF oval5 = new RectF(
      parentWidth / 2 - middleR + middle_OutR - middle_OutR / 2,
      parentHeight / 2 - middleR + middle_OutR - middle_OutR / 2,
      parentWidth / 2 + middleR - middle_OutR + middle_OutR / 2,
      parentHeight / 2 + middleR - middle_OutR + middle_OutR / 2);
    canvas.drawArc(oval5, 0, endAngle, false, paint5);
    drawChargeStatusInCircle(canvas, parentWidth, parentHeight, true);
    drawChargeNumber(canvas, parentWidth, parentHeight);
    drawLeftTopImage(canvas, parentWidth, parentHeight);
  }


  /**
   * 故障无数据
   *
   * @param canvas
   * @param parentWidth
   * @param parentHeight
   */
  private void drawChargeErrorNoData(Canvas canvas, int parentWidth, int parentHeight) {

    Paint paint = new Paint();
    paint.setAntiAlias(true);
    paint.setColor(getResources().getColor(R.color.out_middle_color_sweepgradient_error_start));
    paint.setAlpha(20);
    canvas.drawCircle(parentWidth / 2, parentHeight / 2, outR, paint);
    //画中间圆形
    Paint paint2 = new Paint();
    //定义画笔颜色
    paint2.setColor(getResources().getColor(R.color.out_middle_color_error_in));
    //消除锯齿
    paint2.setAntiAlias(true);
    canvas.drawCircle(parentWidth / 2, parentHeight / 2, middleR, paint2);
    //画中间-外圆环
    Paint paint3 = new Paint();
    paint3.setColor(getResources().getColor(R.color.out_middle_color_error_out));
    paint3.setStyle(Paint.Style.STROKE);
    paint3.setStrokeWidth(middle_OutR);
    paint3.setAntiAlias(true);
    canvas.drawCircle(parentWidth / 2, parentHeight / 2, middleR, paint3);
    //   画内圆
    Paint paint4 = new Paint();
    paint4.setColor(getResources().getColor(R.color.white));
    paint4.setAntiAlias(true);
    canvas.drawCircle(parentWidth / 2, parentHeight / 2, inR, paint4);
    //画电量
    Paint text = new Paint();
    text.setColor(Color.BLACK);
    text.setTextAlign(Paint.Align.CENTER);
    text.setTypeface(Typeface.DEFAULT_BOLD);
    text.setTextSize(dpToPx(90));
    Matrix matrix = new Matrix();
    canvas.setMatrix(matrix);
    canvas.drawText("--", parentWidth / 2, parentHeight / 2 + inR / 5, text);
    //电池健康状态
    int iconWidth = dpToPx(36);
    int iconHeight = dpToPx(36);
    int iconLeft = parentWidth / 2 - iconWidth - outR + iconWidth / 3;
    int iconTop = parentHeight / 2 - iconHeight - outR + iconHeight / 3;
    int iconRight = parentWidth / 2 + iconWidth - outR + iconWidth / 3;
    int iconBottom = parentHeight / 2 + iconHeight - outR + iconHeight / 3;
    //画当前状态
    Paint paint6 = new Paint();
    Bitmap bitmap = BitmapFactory.decodeResource(getContext().getResources(), R.drawable.icon_ch7000_charge_error_left_top);
    //Rect src: 是对图片进行裁截
    Rect srcRect = new Rect(0, 0, bitmap.getWidth(), bitmap.getHeight());
    //图片需要绘制的矩形区域
    Rect dstRect = new Rect(
      iconLeft,
      iconTop,
      iconRight,
      iconBottom);
    statusImageRect = dstRect;
    canvas.drawBitmap(bitmap, srcRect, dstRect, paint6);
    drawLeftTopImage(canvas, parentWidth, parentHeight);
  }

  /**
   * 故障有数据
   *
   * @param canvas
   * @param parentWidth
   * @param parentHeight
   */
  private void drawChargeErrorWithData(Canvas canvas, int parentWidth, int parentHeight) {
    if (value == -1) {
      return;
    }
    if (value == 100) {
      drawChargeErrorWithFullyData(canvas, parentWidth, parentHeight);
      return;
    }
    float endAngle = 3.6f * value;
    float[] position = new float[2];
    position[0] = 0f;
    position[1] = ((endAngle) * 1.0f) / 360f;
    Paint paint = new Paint();
    paint.setAntiAlias(true);
    paint.setAlpha(15);
    RadialGradient mRadialGradient = new RadialGradient(parentWidth / 2,
      parentHeight / 2,
      outR,
      getResources().getColor(R.color.out_color_sweepgradient_error_with_data_end),
      getResources().getColor(R.color.out_color_sweepgradient_error_with_data_start),
      Shader.TileMode.MIRROR);
    paint.setShader(mRadialGradient);
    canvas.drawCircle(parentWidth / 2, parentHeight / 2, outR, paint);


    Paint paint2 = new Paint();
    paint2.setAntiAlias(true);
    LinearGradient linearGradient = new LinearGradient(
      parentWidth / 2 - outR / 2,
      parentHeight / 2 + outR / 2,
      parentWidth / 2 + outR,
      parentWidth / 2 - outR,
      getResources().getColor(R.color.out_color_sweepgradient_over_temp_bg_end),
      getResources().getColor(R.color.out_color_sweepgradient_over_temp_bg_start),
      Shader.TileMode.CLAMP);
    paint2.setShader(linearGradient);
    canvas.drawCircle(parentWidth / 2, parentHeight / 2, middleR, paint2);
    Paint paint3 = new Paint();
    paint3.setAntiAlias(true);
    int[] SECTION_COLORS = {getResources().getColor(R.color.out_color_sweepgradient_error_with_data_end),
      getResources().getColor(R.color.out_color_sweepgradient_error_with_data_ch_num_start)};
    SweepGradient mSweepGradient = new SweepGradient(
      parentWidth / 2,
      parentHeight / 2,
      SECTION_COLORS,
      position);
    paint3.setShader(mSweepGradient);
    RectF oval = new RectF(
      parentWidth / 2 - middleR,
      parentHeight / 2 - middleR,
      parentWidth / 2 + middleR,
      parentHeight / 2 + middleR);
    canvas.drawArc(oval, 0, endAngle, true, paint3);
    //   画内圆
    Paint paint4 = new Paint();
    paint4.setColor(getResources().getColor(R.color.white));
    paint4.setAntiAlias(true);
    canvas.drawCircle(parentWidth / 2, parentHeight / 2, inR, paint4);
    Paint paint5 = new Paint();
    paint5.setAntiAlias(true);
    paint5.setStyle(Paint.Style.STROKE);
    paint5.setStrokeWidth(middle_OutR);
    int[] SECTION_COLORS5 = {
      getResources().getColor(R.color.out_middle_color_sweepgradient_charging_number_middle_out_end),
      getResources().getColor(R.color.out_middle_color_sweepgradient_error_with_data_middle_out_start)
    };
    SweepGradient mSweepGradient5 = new SweepGradient(
      parentWidth / 2,
      parentHeight / 2,
      SECTION_COLORS5,
      position);
    paint5.setShader(mSweepGradient5);
    RectF oval5 = new RectF(
      parentWidth / 2 - middleR + middle_OutR - middle_OutR / 2,
      parentHeight / 2 - middleR + middle_OutR - middle_OutR / 2,
      parentWidth / 2 + middleR - middle_OutR + middle_OutR / 2,
      parentHeight / 2 + middleR - middle_OutR + middle_OutR / 2);
    canvas.drawArc(oval5, 0, endAngle, false, paint5);

    drawStarLine(canvas, parentWidth, parentHeight, getResources().getColor(R.color.line_error_with_data));

    drawChargeNumber(canvas, parentWidth, parentHeight);
    drawLeftTopImage(canvas, parentWidth, parentHeight);
  }


  /**
   * 满电故障有数据
   *
   * @param canvas
   * @param parentWidth
   * @param parentHeight
   */
  private void drawChargeErrorWithFullyData(Canvas canvas, int parentWidth, int parentHeight) {

    if (value == -1) {
      return;
    }
    float endAngle = 3.6f * value;
    float[] position = new float[2];
    position[0] = 0f;
    position[1] = ((endAngle) * 1.0f) / 360f;

    Paint paint = new Paint();
    paint.setAntiAlias(true);
    paint.setAlpha(15);
    RadialGradient mRadialGradient = new RadialGradient(parentWidth / 2,
      parentHeight / 2,
      outR,
      getResources().getColor(R.color.out_color_sweepgradient_error_with_data_end),
      getResources().getColor(R.color.out_color_sweepgradient_error_with_data_start),
      Shader.TileMode.MIRROR);
    paint.setShader(mRadialGradient);
    canvas.drawCircle(parentWidth / 2, parentHeight / 2, outR, paint);

    Paint paint2 = new Paint();
    paint2.setAntiAlias(true);
    paint2.setColor(getResources().getColor(R.color.out_color_sweepgradient_over_temp_fully_bg_end));
    canvas.drawCircle(parentWidth / 2, parentHeight / 2, middleR, paint2);


    Paint paint3 = new Paint();
    paint3.setAntiAlias(true);
    paint3.setColor(getResources().getColor(R.color.out_color_sweepgradient_over_temp_fully_bg_end));
    RectF oval = new RectF(
      parentWidth / 2 - middleR,
      parentHeight / 2 - middleR,
      parentWidth / 2 + middleR,
      parentHeight / 2 + middleR);

    canvas.drawArc(oval, 0, endAngle, true, paint3);
    //   画内圆
    Paint paint4 = new Paint();
    paint4.setColor(getResources().getColor(R.color.white));
    paint4.setAntiAlias(true);
    canvas.drawCircle(parentWidth / 2, parentHeight / 2, inR, paint4);

    Paint paint5 = new Paint();
    paint5.setAntiAlias(true);
    paint5.setStyle(Paint.Style.STROKE);
    paint5.setStrokeWidth(middle_OutR);
    paint5.setColor(getResources().getColor(R.color.out_color_sweepgradient_over_temp_fully_middle));
    RectF oval5 = new RectF(
      parentWidth / 2 - middleR + middle_OutR - middle_OutR / 2,
      parentHeight / 2 - middleR + middle_OutR - middle_OutR / 2,
      parentWidth / 2 + middleR - middle_OutR + middle_OutR / 2,
      parentHeight / 2 + middleR - middle_OutR + middle_OutR / 2);
    canvas.drawArc(oval5, 0, endAngle, false, paint5);

    if (value == -1) {
      return;
    }
    int height = 90;
    //画电量
    Paint text = new Paint();
    text.setColor(Color.BLACK);

    text.setTextAlign(Paint.Align.CENTER);
    text.setTextSize(dpToPx(100));
    Matrix matrix = new Matrix();
    canvas.setMatrix(matrix);
    canvas.drawText(String.valueOf(value), parentWidth / 2, parentHeight / 2 + height / 2, text);
    //计算文字偏移量
    float x;
    x = text.measureText(String.valueOf(value));
    //画百分号
    Paint text2 = new Paint();
    text2.setColor(Color.BLACK);

    text2.setTextAlign(Paint.Align.CENTER);
    text2.setTypeface(Typeface.DEFAULT_BOLD);
    text2.setTextSize(dpToPx(58));
    Matrix matrix2 = new Matrix();
    canvas.setMatrix(matrix2);
    canvas.drawText("%", parentWidth / 2 + x + dpToPx(2) - x / 3,
      parentHeight / 2 + height / 2,
      text2);
    drawLeftTopImage(canvas, parentWidth, parentHeight);
  }

  private void drawChargeWornImage(Canvas canvas, int parentWidth, int parentHeight) {
    if (!isOptimized) {
      return;
    }
    int width = dpToPx(58);
    int height = dpToPx(34);

    //画当前状态
    Paint paint = new Paint();
    Bitmap bitmap = BitmapFactory.decodeResource(getContext().getResources(), R.drawable.icon_charge_protect);
    //Rect src: 是对图片进行裁截
    Rect srcRect = new Rect(0, 0, bitmap.getWidth(), bitmap.getHeight());
    //图片需要绘制的矩形区域
    Rect dstRect = new Rect(parentWidth / 2 - outR / 2 - dpToPx(75),
      parentHeight / 2 - outR + dpToPx(50),
      parentWidth / 2 - outR / 2 + width - dpToPx(75),
      parentHeight / 2 - outR + height + dpToPx(50));
    canvas.drawBitmap(bitmap, srcRect, dstRect, paint);
  }


  private void drawStarLine(Canvas canvas, int parentWidth, int parentHeight, int color) {

    float angel = 0;

    int startX = (int) (parentWidth / 2 + dpToPx(248) * Math.cos(angel * 3.14 / 180));

    int startY = (int) (parentHeight / 2 + dpToPx(248) * Math.sin(angel * 3.14 / 180));


    int endX = (int) (parentWidth / 2 + dpToPx(180) * Math.cos(angel * 3.14 / 180));

    int endY = (int) (parentHeight / 2 + (dpToPx(180) * Math.sin(angel * 3.14 / 180)));

    Paint paint = new Paint();
    paint.setColor(color);
    paint.setStyle(Paint.Style.STROKE);
    paint.setStrokeWidth(dpToPx(3));
    canvas.drawLine(startX, startY, endX, endY, paint);

  }

  private void drawWarningLine(Canvas canvas, int parentWidth, int parentHeight, float limit) {

    //充电保护未开启--不做绘制
    if (!isOptimized) {
      return;
    }

    float angel = limit;

    int startX = (int) (parentWidth / 2 + dpToPx(248) * Math.cos(angel * 3.14 / 180));

    int startY = (int) (parentHeight / 2 + dpToPx(248) * Math.sin(angel * 3.14 / 180));

    int endX = (int) (parentWidth / 2 + dpToPx(180) * Math.cos(angel * 3.14 / 180));

    int endY = (int) (parentHeight / 2 + (dpToPx(180) * Math.sin(angel * 3.14 / 180)));

    Paint paint = new Paint();
    paint.setColor(getResources().getColor(R.color.white));
    paint.setStyle(Paint.Style.STROKE);
    paint.setStrokeWidth(dpToPx(5));
    paint.setAlpha(150);
    canvas.drawLine(startX, startY, endX, endY, paint);


  }


  /**
   * 画内圆充电状态
   *
   * @param canvas
   * @param parentWidth
   * @param parentHeight
   */
  private void drawChargeStatusInCircle(Canvas canvas, int parentWidth, int parentHeight, boolean worning) {

    int width = dpToPx(32);
    int height = dpToPx(32);
    int diff = dpToPx(180) / 2;

    //画当前状态
    Paint chargeStatusPaint = new Paint();
    Bitmap bitmap;
    if (worning) {
      bitmap = BitmapFactory.decodeResource(getContext().getResources(), R.drawable.icon_ch7000_worn);
    } else {
      bitmap = BitmapFactory.decodeResource(getContext().getResources(), R.drawable.icon_ch7000_charing);
    }

    //Rect src: 是对图片进行裁截
    Rect srcRect = new Rect(0, 0, bitmap.getWidth(), bitmap.getHeight());
    //图片需要绘制的矩形区域
    Rect dstRect = new Rect(parentWidth / 2 - width,
      parentHeight / 2 - height - diff,
      parentWidth / 2 + height,
      parentHeight / 2 + height - diff);

    Matrix matrix2 = new Matrix();
    canvas.setMatrix(matrix2);
    canvas.drawBitmap(bitmap, srcRect, dstRect, chargeStatusPaint);
  }

  /**
   * 画电量
   *
   * @param canvas
   * @param parentWidth
   * @param parentHeight
   */
  private void drawChargeNumber(Canvas canvas, int parentWidth, int parentHeight) {


    if (value == -1) {
      return;
    }

    int height = dpToPx(45);

    //画电量
    Paint text = new Paint();
    text.setColor(Color.BLACK);
    text.setFakeBoldText(true);
    text.setTextAlign(Paint.Align.CENTER);
    text.setTextSize(dpToPx(110));
    Matrix matrix = new Matrix();
    canvas.setMatrix(matrix);

    //画百分号
    Paint text2 = new Paint();
    text2.setColor(Color.BLACK);
    text2.setTextAlign(Paint.Align.CENTER);
    text2.setTypeface(Typeface.DEFAULT_BOLD);
    text2.setTextSize(dpToPx(58));
    Matrix matrix2 = new Matrix();
    canvas.setMatrix(matrix2);

    if (value>0){
      float textWidth = text.measureText(String.valueOf(value));
      float text2Width = text2.measureText("%");
      canvas.drawText(String.valueOf(value), parentWidth / 2 - text2Width / 2, parentHeight / 2 + height , text);
      canvas.drawText("%", parentWidth / 2 + textWidth / 2, parentHeight / 2 + height , text2);
    }else {
      String zeroEle = "--";
      canvas.drawText(zeroEle, parentWidth / 2 , parentHeight / 2 + height , text);
    }

    drawBatteryContent( canvas,  parentWidth,  parentHeight);

    //画容量及当前充电的AH背景
//    String chargedText = "2.5"; // 设置要绘制的第一个文字
//    int chargedTextColor = getResources().getColor(R.color.out_middle_color_battery_charged_color); // 设置要绘制的第一个文字
//
//    String batterySizeText = "/8AH"; // 设置要绘制的第二个文字
//    int batterySizeTextColor = getResources().getColor(R.color.out_middle_color_battery_size_color);
//
//    for (int i = 0; i < capacityModelArrayList.size(); i++) {
//      if (i==0){
//        CapacityModel capacityLeft  = capacityModelArrayList.get(i);
//        chargedText = capacityLeft.getText();
//        chargedTextColor = TextUtils.isEmpty(capacityLeft.getColor())?
//                getResources().getColor(R.color.out_middle_color_battery_charged_color):Color.parseColor(capacityLeft.getColor());
//      }else {
//        CapacityModel capacityRight  = capacityModelArrayList.get(i);
//        batterySizeText = capacityRight.getText();
//        batterySizeTextColor = TextUtils.isEmpty(capacityRight.getColor())?
//                getResources().getColor(R.color.out_middle_color_battery_size_color):Color.parseColor(capacityRight.getColor());
//      }
//    }
//
//    int batteryViewWidth = dpToPx(140);
//    int batteryViewHeight = dpToPx(70);
//    int marginTextTop = dpToPx(15);
//    Paint batterySizePaint = new Paint(Paint.ANTI_ALIAS_FLAG);
//    batterySizePaint.setColor(getResources().getColor(R.color.out_middle_color_battery_border_color));
//    batterySizePaint.setStyle(Paint.Style.STROKE);
//    batterySizePaint.setStrokeWidth(2.0f);
//    RectF rect = new RectF(
//            (getWidth() - batteryViewWidth)/2,
//            parentHeight / 2 + height + marginTextTop,
//            (getWidth() - batteryViewWidth)/2 + batteryViewWidth,
//            parentHeight / 2 + height + batteryViewHeight);
//    float cornerRadius = dpToPx(37); // 设置圆角半径
//    canvas.drawRoundRect(rect, cornerRadius, cornerRadius, batterySizePaint);
//
//    int textLeft = (getWidth() - batteryViewWidth)/2 + 20;
//    int textTop = parentHeight / 2 + height + batteryViewHeight - 25;
//    //画已经充满多少安时（AH）
//    Paint textCharged = new Paint();
//    textCharged.setTextSize(dpToPx(32));
//    textCharged.setTextAlign(Paint.Align.LEFT);
//    textCharged.setColor(chargedTextColor);
//    canvas.drawText(chargedText, textLeft, textTop , textCharged);
//
//    float textChargedWidth = text2.measureText(chargedText);
//    float batterySizeTextLeft = textLeft + textChargedWidth/2;
//    //总电池包的容量/8AH
//    Paint allCharged = new Paint();
//    textCharged.setTextAlign(Paint.Align.RIGHT);
//    allCharged.setTextSize(dpToPx(32));
//    allCharged.setColor(batterySizeTextColor);
//    canvas.drawText(batterySizeText,  batterySizeTextLeft, textTop  , allCharged);

  }


  /**
   * 画圆环--2.7/64Ah
   * @param canvas
   * @param parentWidth
   * @param parentHeight
   */
  private void drawBatteryContent(Canvas canvas, int parentWidth, int parentHeight) {
    // 基础画笔设置
    Paint paint = new Paint(Paint.ANTI_ALIAS_FLAG);
    paint.setStyle(Paint.Style.FILL);
    paint.setTextAlign(Paint.Align.LEFT);

    // 计算文本总宽度
    float totalTextWidth = calculateTotalTextWidth(paint);
    
    // 矩形框尺寸计算
    int batteryViewWidth = dpToPx((int)totalTextWidth); // 不需要额外padding
    int batteryViewHeight = dpToPx(60); // 2倍图下为62px
    int marginTextTop = dpToPx(15);
    int height = dpToPx(45);

    // 绘制矩形框
    Paint batterySizePaint = new Paint(Paint.ANTI_ALIAS_FLAG);
    batterySizePaint.setColor(getResources().getColor(R.color.out_middle_color_battery_border_color));
    batterySizePaint.setStyle(Paint.Style.STROKE);
    batterySizePaint.setStrokeWidth(2.0f);

    // 计算矩形框位置
    RectF rectF = new RectF(
            (getWidth() - batteryViewWidth) / 2,
            parentHeight / 2 + height + marginTextTop,
            (getWidth() - batteryViewWidth) / 2 + batteryViewWidth,
            parentHeight / 2 + height + marginTextTop + batteryViewHeight
    );
    
    // 绘制圆角矩形
    float cornerRadius = dpToPx(37);
    canvas.drawRoundRect(rectF, cornerRadius, cornerRadius, batterySizePaint);

    // 计算所有文本实际总宽度
    float actualTotalWidth = 0;
    for (CapacityModel item : capacityModelArrayList) {
        paint.setTextSize(dpToPx(item.getFontSize()));
        actualTotalWidth += paint.measureText(item.getText());
        if (capacityModelArrayList.indexOf(item) < capacityModelArrayList.size() - 1) {
            actualTotalWidth += dpToPx(item.getMarginRight());
        }
    }


    // 计算文本起始位置，确保居中，不需要额外padding
    float startX = rectF.left + (rectF.width() - actualTotalWidth) / 2;
    
    // 计算文本基线位置，确保垂直居中
    Paint.FontMetrics fm = paint.getFontMetrics();
    float textHeight = fm.bottom - fm.top;
    float textBaselineY = rectF.centerY() - (fm.bottom + fm.top) / 2;

    // 绘制所有文本
    float currentX = startX;
    for (CapacityModel item : capacityModelArrayList) {
        paint.setTextSize(dpToPx(item.getFontSize()));
        paint.setColor(Color.parseColor(item.getColor()));
        
        canvas.drawText(item.getText(), currentX, textBaselineY, paint);
        currentX += paint.measureText(item.getText());
        if (capacityModelArrayList.indexOf(item) < capacityModelArrayList.size() - 1) {
            currentX += dpToPx(item.getMarginRight());
        }
    }
  }


  // 计算所有文本的总宽度
  private float calculateTotalTextWidth(Paint paint) {
    float totalWidth = 0;
    float density = getResources().getDisplayMetrics().density;
    
    for (CapacityModel item : capacityModelArrayList) {
        paint.setTextSize(item.getFontSize() * density);
        float textWidth = paint.measureText(item.getText()) / ScreenUtils.getScreenDensity(); // 除以2来补偿高分辨率屏幕的缩放
        totalWidth += textWidth + dpToPx(item.getMarginRight());
    }

    float padding = 8 * ScreenUtils.getScreenDensity();
    totalWidth  =  totalWidth + padding;

    // 移除最后一个item的marginRight
    if (!capacityModelArrayList.isEmpty()) {
        totalWidth -= dpToPx(capacityModelArrayList.get(capacityModelArrayList.size() - 1).getMarginRight());
    }
    return totalWidth;
  }

  /**
   * 画电量
   * 隐藏充电图标
   * @param canvas
   * @param parentWidth
   * @param parentHeight
   */
  private void drawChargeNumber2(Canvas canvas, int parentWidth, int parentHeight) {


    if (value == -1) {
      return;
    }

    int height = 90;

    //画电量
    Paint text = new Paint();
    text.setColor(Color.BLACK);
    text.setTextAlign(Paint.Align.CENTER);
    text.setTextSize(dpToPx(100));
    Matrix matrix = new Matrix();
    canvas.setMatrix(matrix);


    //画百分号
    Paint text2 = new Paint();
    text2.setColor(Color.BLACK);
    text2.setTextAlign(Paint.Align.CENTER);
    text2.setTypeface(Typeface.DEFAULT_BOLD);
    text2.setTextSize(dpToPx(58));
    Matrix matrix2 = new Matrix();
    canvas.setMatrix(matrix2);


    float textWidth = text.measureText(String.valueOf(value));
    float text2Width = text2.measureText("%");

    canvas.drawText(String.valueOf(value), parentWidth / 2 - text2Width / 2, parentHeight / 2 + height / 2, text);

    canvas.drawText("%", parentWidth / 2 + textWidth / 2, parentHeight / 2 + height / 2, text2);


  }

  private void drawLeftTopImage(Canvas canvas, int parentWidth, int parentHeight) {
    if (TextUtils.isEmpty(healthStatus)) {
      return;
    }
    //电池健康状态
    int iconWidth = dpToPx(36);
    int iconHeight = dpToPx(36);
    int iconLeft = parentWidth / 2 - iconWidth - outR + iconWidth / 3;
    int iconTop = parentHeight / 2 - iconHeight - outR + iconHeight / 3;
    int iconRight = parentWidth / 2 + iconWidth - outR + iconWidth / 3;
    int iconBottom = parentHeight / 2 + iconHeight - outR + iconHeight / 3;
    //画当前状态
    Paint paint6 = new Paint();
    Bitmap bitmap = BitmapFactory.decodeResource(getContext().getResources(),
      healthStatus.equals(HEALTHSTATUS_NORMAL) ? R.drawable.icon_ch7000_charge_s_left_top : R.drawable.icon_ch7000_charge_error_left_top
    );
    //Rect src: 是对图片进行裁截
    Rect srcRect = new Rect(0, 0, bitmap.getWidth(), bitmap.getHeight());
    //图片需要绘制的矩形区域
    Rect dstRect = new Rect(
      iconLeft,
      iconTop,
      iconRight,
      iconBottom);
    statusImageRect = dstRect;
    canvas.drawBitmap(bitmap, srcRect, dstRect, paint6);

    if (!showTips) {
      return;
    }

    if (TextUtils.isEmpty(healthStatusTip)) {
      return;
    }

    //画气泡 TODO
    //画向上箭头
    Paint paint7 = new Paint();
    int arrowWidth = dpToPx(12);
    int arrowHeight = dpToPx(12);
    int arrowLeft = iconLeft + arrowWidth * 2;
    int arrowRight = iconRight - arrowWidth * 2;
    int arrowTop = iconBottom + dpToPx(10);
    int arrowBottom = iconBottom + arrowHeight + dpToPx(12);
    //向上箭头
    Bitmap bitmapTipArw = BitmapFactory.decodeResource(getContext().getResources(), R.drawable.icon_ch7000_arrow_up);
    Rect srcRectTipsArw = new Rect(0, 0, bitmapTipArw.getWidth(), bitmapTipArw.getHeight());
    Rect dstRectTipsArw = new Rect(arrowLeft,
      arrowTop,
      arrowRight,
      arrowBottom);
    canvas.drawBitmap(bitmapTipArw, srcRectTipsArw, dstRectTipsArw, paint7);

    int marginTopAndBottom = dpToPx(25);
    int marginLeftAndRight = dpToPx(25);

    int maxWidthTips = dpToPx(470);
    int minWidthTips = parentWidth / 2 + dpToPx(10);

    int minHeight = dpToPx(90);
    int maxHeightTips = dpToPx(90 * 4);

    int rectLeft = iconLeft;
    int rectTop = arrowBottom;
    //需要计算
    int rectRight = minWidthTips;
    //需要计算
    int rectBottom = arrowBottom + minHeight;

    String textShort = healthStatusTip;
    //绘制文字
    TextPaint textPaint = new TextPaint();
    textPaint.setColor(getResources().getColor(R.color.white));
    textPaint.setAntiAlias(true);
    textPaint.setTextAlign(Paint.Align.LEFT);
    textPaint.setTextSize(sp2px(14));
    Rect rect = new Rect();
    textPaint.getTextBounds(textShort, 0, textShort.length(), rect);
    //获取文本宽度
    int textWidth = rect.width() + pxTodp(marginLeftAndRight) * 2;
    //获取文本高度
    int textHeight = rect.height();
    //计算文字行宽
    int lineWidth = textWidth > maxWidthTips ? maxWidthTips : textWidth;
    StaticLayout staticLayout = new StaticLayout(textShort, textPaint, lineWidth,
      Layout.Alignment.ALIGN_CENTER, 1.0f, 0.0f, false);


    // 计算行数
    int lineCount = staticLayout.getLineCount();
    //计算阴影宽度
    rectRight = rectLeft + lineWidth + marginLeftAndRight + dpToPx(5);

    Paint paint8 = new Paint(Paint.ANTI_ALIAS_FLAG);
    paint8.setColor(Color.BLACK);
    paint8.setAlpha(200);
    paint8.setStyle(Paint.Style.FILL);


    //获取文字区域大小用于计算。当前文字不绘制TODO
    Rect bounds = new Rect();
    int yoffDiff = 0;
    for (int i = 0; i < lineCount; i++) {

      int lineStart = staticLayout.getLineStart(i);

      int lineEnd = staticLayout.getLineEnd(i);

      String line = textShort.substring(lineStart, lineEnd);


      textPaint.getTextBounds(line, 0, line.length(), bounds);

      yoffDiff += bounds.height() + dpToPx(5);
    }

    //设置阴影区域
    RectF rectF = new RectF(
      rectLeft,
      rectTop,
      rectRight,
      rectTop + yoffDiff + marginTopAndBottom * 2);

    canvas.drawRoundRect(rectF, dpToPx(10), dpToPx(10), paint8);

    //绘制最外面文字
    Rect realBounds = new Rect();
    int yoff = 0;
    for (int i = 0; i < lineCount; i++) {

      int lineStart = staticLayout.getLineStart(i);

      int lineEnd = staticLayout.getLineEnd(i);

      String line = textShort.substring(lineStart, lineEnd);

      int bottom = rectBottom - textHeight / 2 - marginTopAndBottom
        + yoff;

      canvas.drawText(line, rectLeft + marginLeftAndRight, bottom
        , textPaint);
      textPaint.getTextBounds(line, 0, line.length(), realBounds);
      yoff += realBounds.height() + dpToPx(5);

    }
  }

  private void drawChargeIsOptimized(Canvas canvas, int parentWidth, int parentHeight){
    if (!isOptimized){
      return;
    }
    Paint paint3 = new Paint();
    paint3.setAntiAlias(true);
    paint3.setColor(getResources().getColor(R.color.out_middle_color_isOptimized_bg));
    RectF oval = new RectF(
            parentWidth / 2 - middleR,
            parentHeight / 2 - middleR,
            parentWidth / 2 + middleR,
            parentHeight / 2 + middleR);
    canvas.drawArc(oval, 0, 324, true, paint3);
  }

  public int dpToPx(int dp) {
    float density = getResources().getDisplayMetrics().density;
    return (int) (Math.round(dp * density) / 2);
  }

  /*
   *px转dp
   */
  public int pxTodp(float px) {
    float scale = getResources().getDisplayMetrics().density;
    return (int) (px / scale + 0.5f);
  }

  /**
   * 将sp值转换为px值，保证文字大小不变
   *
   * @param spValue 字体的大小
   * @return
   */
  public float sp2px(float spValue) {
    //fontScale （DisplayMetrics类中属性scaledDensity）
    final float fontScale = getResources().getDisplayMetrics().scaledDensity;
    return (spValue * fontScale + 0.5f);
  }

  @SuppressLint("ClickableViewAccessibility")
  @Override
  public boolean onTouchEvent(MotionEvent event) {
    if (event.getAction() == MotionEvent.ACTION_UP) {
      if (null == statusImageRect) {
        return true;
      }
      if (statusImageRect.contains((int) event.getX(), (int) event.getY())) {
        if (!TextUtils.isEmpty(healthStatusTip)) {
          showTips = !showTips;
          invalidate();
        }

      }
    }
    return true;
  }


  //数据设置相关
  protected static final String EMPTY = "empty";
  protected static final String STANDBY = "standby";
  protected static final String DISCHARGING = "discharging";
  protected static final String CHARGING = "charging";
  protected static final String FULLYCHARGED = "fullyCharged";
  protected static final String CHARGINGFAILED = "chargingFailed";

  protected static final String OVERHEAT = "overheat";
  protected static final String ERROR = "error";



  //TODO 电池电量----value
  protected int value = 30;
  //TODO 描述：电池状态---status
  protected String status = CHARGING;

  //TODO 健康状态 枚举值：'normal' | 'abnormal'
  protected static final String HEALTHSTATUS_NORMAL = "normal";
  protected static final String HEALTHSTATUS_ABNORMAL = "abnormal";
  protected String healthStatus = "";

  //TODO 电池健康状态提示内容-------healthStatusTip
  protected String healthStatusTip = "";
  //TODO 电池是否开启充电保护
  protected boolean isOptimized = true;

  public void setStatus(String status) {
    this.status = status;
    invalidate();
  }

  public void setValue(int value) {
    this.value = value;
    invalidate();
  }


  public void setHealthStatus(String healthStatus) {
    this.healthStatus = healthStatus;
    invalidate();
  }

  public void setHealthStatusTip(String healthStatusTip) {
    this.healthStatusTip = healthStatusTip;
    invalidate();
  }

  public void setOptimized(boolean optimized) {
    isOptimized = optimized;
    invalidate();
  }

  public void setCapacityModelArrayList(List<CapacityModel> list){
    if (list.isEmpty()){
      return;
    }
    capacityModelArrayList.clear();
    capacityModelArrayList.addAll(list);
    invalidate();
  }


}
