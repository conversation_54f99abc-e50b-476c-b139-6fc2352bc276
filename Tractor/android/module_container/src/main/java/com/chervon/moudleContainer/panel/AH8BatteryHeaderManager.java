package com.chervon.moudleContainer.panel;

import android.view.View;

import androidx.annotation.NonNull;

import com.blankj.utilcode.util.LogUtils;
import com.chervon.moudleContainer.panel.data.entity.CapacityModel;
import com.chervon.moudleContainer.widget.AH8BatteryHeader;
import com.facebook.react.bridge.ReadableArray;
import com.facebook.react.bridge.ReadableMap;
import com.facebook.react.bridge.ReadableType;
import com.facebook.react.uimanager.SimpleViewManager;
import com.facebook.react.uimanager.ThemedReactContext;
import com.facebook.react.uimanager.annotations.ReactProp;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: 184862
 * @CreateDate: 2023/10/8
 * @UpdateDate: 2023/10/8
 */
public class AH8BatteryHeaderManager extends SimpleViewManager<AH8BatteryHeader> {

    private AH8BatteryHeader ah8BatteryHeader;
    private static final String TAG = "AH8BatteryHeaderManager";

    @NonNull
    @Override
    public String getName() {
        return "CVNRCTBatteryCircle";
    }

    @NonNull
    @Override
    protected AH8BatteryHeader createViewInstance(@NonNull ThemedReactContext reactContext) {
        ah8BatteryHeader = new AH8BatteryHeader(reactContext);
        return ah8BatteryHeader;
    }

    @ReactProp(name = "value")
    public void setValue(View view, int value) {
        LogUtils.e(TAG, "setValue---->" + value);
        if (null != ah8BatteryHeader) {
            ah8BatteryHeader.setValue(value);
        }
    }

    @ReactProp(name = "status")
    public void setStatus(View view, String value) {
        LogUtils.e(TAG, "status---->" + value);
        if (null != ah8BatteryHeader) {
            ah8BatteryHeader.setStatus(value);
        }
    }

    @ReactProp(name = "healthStatus")
    public void setHealthStatus(View view, String value) {
        // LogUtils.e(TAG, "healthStatus---->" + value);
        if (null != ah8BatteryHeader) {
            ah8BatteryHeader.setHealthStatus(value);
        }
    }

    @ReactProp(name = "healthStatusTip")
    public void setHealthStatusTip(View view, String value) {
//    LogUtils.e(TAG, "healthStatusTip---->" + value);
        if (null != ah8BatteryHeader) {
            ah8BatteryHeader.setHealthStatusTip(value);
        }
    }

    @ReactProp(name = "capacity")
    public void setCapacity(View view, ReadableArray capacity) {
        LogUtils.e(TAG, "setCapacity---->" + capacity.size());
        handleReadableArray(capacity);

    }

    @ReactProp(name = "isOptimized")
    public void setOptimized(View view, boolean value) {
        LogUtils.e(TAG, "isOptimized---->" + value);
        if (null != ah8BatteryHeader) {
            ah8BatteryHeader.setOptimized(value);
        }
    }


    public void handleReadableArray(ReadableArray array) {
        if (null == array) {
            return;
        }
        if (array.size()==0){
            return;
        }



        List<CapacityModel> list = new ArrayList<>();
        for (int i = 0; i < array.size(); i++) {
            ReadableMap item = array.getMap(i);
            String text = item.getString("text");
            String color = item.getString("color");
            int fontSize = item.getInt("fontSize") *2;
            int marginRight = item.getInt("marginRight");
            if (fontSize == 0){
                fontSize = 32;
            }
            list.add(new CapacityModel(text, color,fontSize,marginRight));
        }

//        list.add(new CapacityModel("12.7", "#77BC1F",32,0));
//        list.add(new CapacityModel("/", "#8F8F8F",32,0));
//        list.add(new CapacityModel("64Ah", "#8F8F8F",32,0));


        ah8BatteryHeader.setCapacityModelArrayList(list);
    }

}
