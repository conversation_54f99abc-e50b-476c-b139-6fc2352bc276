{"project_info": {"project_number": "1185542758", "project_id": "ego-connect-na-pre", "storage_bucket": "ego-connect-na-pre.firebasestorage.app"}, "client": [{"client_info": {"mobilesdk_app_id": "1:1185542758:android:123b24266dbd98fbd873a7", "android_client_info": {"package_name": "com.chervon.connect.eu.pre"}}, "oauth_client": [], "api_key": [{"current_key": "AIzaSyBY9IKs93wS-lVe7xHVkWE64B8JPXnylM0"}, {"current_key": "AIzaSyBVgx-0-oYLr2mobMoerJdxl9LUMQGyGNc"}], "services": {"appinvite_service": {"other_platform_oauth_client": []}}}, {"client_info": {"mobilesdk_app_id": "1:1185542758:android:63131511689dbb68d873a7", "android_client_info": {"package_name": "com.chervon.connect_sit"}}, "oauth_client": [], "api_key": [{"current_key": "AIzaSyBY9IKs93wS-lVe7xHVkWE64B8JPXnylM0"}, {"current_key": "AIzaSyBVgx-0-oYLr2mobMoerJdxl9LUMQGyGNc"}], "services": {"appinvite_service": {"other_platform_oauth_client": []}}}, {"client_info": {"mobilesdk_app_id": "1:1185542758:android:07a47a96a59501ddd873a7", "android_client_info": {"package_name": "com.chervon.connect_sit2"}}, "oauth_client": [], "api_key": [{"current_key": "AIzaSyBY9IKs93wS-lVe7xHVkWE64B8JPXnylM0"}, {"current_key": "AIzaSyBVgx-0-oYLr2mobMoerJdxl9LUMQGyGNc"}], "services": {"appinvite_service": {"other_platform_oauth_client": []}}}, {"client_info": {"mobilesdk_app_id": "1:1185542758:android:574de5acf0145941d873a7", "android_client_info": {"package_name": "com.chervon.iot"}}, "oauth_client": [], "api_key": [{"current_key": "AIzaSyBY9IKs93wS-lVe7xHVkWE64B8JPXnylM0"}, {"current_key": "AIzaSyBVgx-0-oYLr2mobMoerJdxl9LUMQGyGNc"}], "services": {"appinvite_service": {"other_platform_oauth_client": []}}}, {"client_info": {"mobilesdk_app_id": "1:1185542758:android:aba7811b444b0a0cd873a7", "android_client_info": {"package_name": "com.chervon.iot_pre"}}, "oauth_client": [], "api_key": [{"current_key": "AIzaSyBY9IKs93wS-lVe7xHVkWE64B8JPXnylM0"}, {"current_key": "AIzaSyBVgx-0-oYLr2mobMoerJdxl9LUMQGyGNc"}], "services": {"appinvite_service": {"other_platform_oauth_client": []}}}], "configuration_version": "1"}