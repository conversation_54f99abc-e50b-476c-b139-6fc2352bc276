package com.chervon.iot;

import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libBase.utils.Utils.sendDurationTrace;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import com.blankj.utilcode.util.GsonUtils;
import com.blankj.utilcode.util.LogUtils;
import com.chervon.iot.broadcast.NetStateChangeObserver;
import com.chervon.iot.broadcast.NetStateChangeReceiver;
import com.chervon.iot.broadcast.NetworkUtil;
import com.chervon.libBase.BaseApplication;
import com.chervon.libBase.utils.ObjectListCompressor;
import com.chervon.libBase.utils.Utils;
import com.chervon.libBase.utils.mlang.MyLang;
import com.chervon.libBluetooth.BluetoothConection;
import com.chervon.libBluetooth.BluetoothService;
import com.chervon.libDB.SoftRoomDatabase;
import com.chervon.libDB.dao.LanguageInfoDao;
import com.chervon.libDB.dao.LanguagePackageDao;
import com.chervon.libDB.entities.LanguageInfo;
import com.chervon.libDB.entities.LanguagePackage;
import com.chervon.libNetwork.http.ApiService;
import com.chervon.libNetwork.http.HttpObserver;
import com.chervon.libNetwork.http.model.request.LanguageV2Request;
import com.chervon.libNetwork.http.model.result.LanguageListBean;
import com.chervon.libNetwork.http.model.result.LanguageV2Res;
import com.chervon.libNetwork.iot.AwsMqttService;
import com.chervon.moudleContainer.panel.AH8BatteryHeaderPackage;
import com.chervon.moudleContainer.panel.CH7000HeaderPackage;
import com.chervon.moudleContainer.panel.CustomExceptionsManagerModule;
import com.chervon.moudleContainer.panel.CustomReactPackage;
import com.chervon.moudleContainer.panel.CustomToastPackage;
import com.chervon.moudleContainer.panel.SemiCircleProgressBarPackage;
import com.chervon.moudleContainer.panel.StallViewPackage;
import com.chervon.trace.TraceManager;
import com.chervon.trace.bean.EnvironmentModel;
import com.facebook.react.PackageList;
import com.facebook.react.ReactApplication;
import com.facebook.react.ReactNativeHost;
import com.facebook.react.ReactPackage;
import com.facebook.react.defaults.DefaultNewArchitectureEntryPoint;
import com.facebook.react.defaults.DefaultReactNativeHost;
import com.facebook.soloader.SoLoader;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.timecat.component.locale.LocaleInfo;
import com.timecat.component.locale.MLang;
import java.io.File;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.ocetnik.timer.BackgroundTimerPackage;


/**
 * @ProjectName: app
 * @Package: com.chervon.iot
 * @ClassName: MainAppliction
 * @Description: Appliction  of shell
 * @Author: wangheng
 * @CreateDate: 2022/4/21 下午7:02
 * @UpdateUser: wangheng
 * @UpdateDate: 2022/4/21 下午7:02
 * @UpdateRemark: new
 * @Version: 1.0
 */
public class MainApplication extends BaseApplication implements ReactApplication, NetStateChangeObserver {
    private final String TAG = "MainApplication";
    public static final String PAGE_ID = "1";
    private long entryTime = System.currentTimeMillis();
    private int mActivityCount = 0;
    //是否进入后台
    private boolean mIsOnBackground = false;
    //默认多语言时间戳
    private final String DEFAULT_LASTREQUESTTS = "1000000000";
    //默认的sys_code
    private final String DEFAULT_SYSCODE = "app";
    private ReactNativeHost mReactNativeHost;

    @Override
    public ReactNativeHost getReactNativeHost() {
        return mReactNativeHost;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        clearCache();
        mReactNativeHost = new MyReactNativeHost(this);
        new Thread(new Runnable() {
            @Override
            public void run() {
                SoLoader.init(MainApplication.this, /* native exopackage */ false);
                if (BuildConfig.IS_NEW_ARCHITECTURE_ENABLED) {
                    DefaultNewArchitectureEntryPoint.load();
                }
                initGson();

            }
        }).start();
        MyLang.init(MainApplication.this);
        initTrace();
        NetStateChangeReceiver.registerObserver(this);
        NetStateChangeReceiver.registerReceiver(this);
        configureCrashReporting();

        getLanguageList();
        registerActivityLifecycle();
    }

    /**
     * 对于新安装用户进行缓存清理
     */
    private void clearCache() {
        if (isFirstInstall()) {
            File cacheDir = getCacheDir();
            File[] files = cacheDir.listFiles();
            if (null != files) {
                for (File file : files) {
                    file.delete();
                }
            }
        }
    }

    private boolean isFirstInstall() {
        String packageName = getPackageName();
        PackageManager packageManager = this.getPackageManager();
        try {
            packageManager.getPackageInfo(packageName, 0);
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
            return true;
        }

        return false;
    }

    private void registerActivityLifecycle() {
        registerActivityLifecycleCallbacks(new ActivityLifecycleCallbacks() {
            @Override
            public void onActivityCreated(@NonNull Activity activity, @Nullable Bundle bundle) {
            }

            @Override
            public void onActivityStarted(@NonNull Activity activity) {

                mActivityCount++;
                if (mIsOnBackground) {
                    //重置后台标识
                    mIsOnBackground = false;
                } else {
                    AwsMqttService.getInstance().reConnect();
                }
            }

            @Override
            public void onActivityResumed(@NonNull Activity activity) {


            }

            @Override
            public void onActivityPaused(@NonNull Activity activity) {

            }

            @Override
            public void onActivityStopped(@NonNull Activity activity) {

                mActivityCount--;
                //处于后台
                if (mActivityCount == 0) {
                    sendDurationTrace(activity, "1", PAGE_ID, getCurrentPageResouce(), "1", "2", System.currentTimeMillis() - entryTime);
                    mIsOnBackground = true;
                    //停止扫描服务
                    BluetoothService.getInstance().stopBlueToothService();
                }

            }

            @Override
            public void onActivitySaveInstanceState(@NonNull Activity activity, @NonNull Bundle bundle) {

            }

            @Override
            public void onActivityDestroyed(@NonNull Activity activity) {
            }
        });
    }


    private void initGson() {
        GsonUtils.setGsonDelegate(new GsonBuilder().create());
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        //监听系统语言切换
        MyLang.onConfigurationChanged(newConfig);
    }


    private void initTrace() {

        TraceManager.Builder builder = new TraceManager.Builder(this);
        builder.setDebug(false)
                .setEnv(getEnvironment())
                .setSidPeriodMinutes(15)
                .setPushLimitMinutes(1)
                .setPushLimitNum(5)
                .start();
        sendAppClick();
    }

    private void sendAppClick() {
        setCurrentPageResouce(PAGE_ID);
        sendClickTrace(this, "1", PAGE_ID, "1", "1", "1");
    }

    /**
     * 埋点环境区分
     *
     * @return
     */
    private EnvironmentModel getEnvironment() {
        final String SIT = "sit";
        final String PRE_NA = "pre_na";
        final String PROD_NA = "prod_na";
        final String PROD_EU = "prod_eu";

        final String PRE_EU = "pre_eu";

        //sit pre_na pre_eu prd_na prd_eu
        if (BuildConfig.FLAVOR.equals(SIT)) {
            LogUtils.i("当前埋点环境为----SIT");
            return EnvironmentModel.SIT;
        } else if (BuildConfig.FLAVOR.equals(PRE_NA)) {
            LogUtils.i("当前埋点环境为----PRE_NA");
            return EnvironmentModel.PRE_NA;
        } else if (BuildConfig.FLAVOR.equals(PRE_EU)){
            LogUtils.i("当前埋点环境为----PRE_EU");
            return EnvironmentModel.PRE_EU;
        }else if (BuildConfig.FLAVOR.equals(PROD_NA)) {
            LogUtils.i("当前埋点环境为----PRD_NA");
            return EnvironmentModel.PRD_NA;
        } else if (BuildConfig.FLAVOR.equals(PROD_EU)) {
            LogUtils.i("当前埋点环境为----PRD_EU");
            return EnvironmentModel.PRD_EU;
        } else {
            LogUtils.i("无配置相关---当前埋点环境为----SIT");
            return EnvironmentModel.SIT;
        }
    }

    @Override
    public void onTerminate() {
        super.onTerminate();
        //app被杀死
        try {
            BluetoothConection.getInstance().disConnectDevice();
        } catch (Exception e) {
            LogUtils.e(TAG, "onTerminate is error:" + e.getMessage());
        }

        mReactNativeHost = null;
        sendDurationTrace(this, "1", PAGE_ID, getCurrentPageResouce(), "1", "2", System.currentTimeMillis() - entryTime);

        NetStateChangeReceiver.unRegisterReceiver(this);
        //停止AWS服务
        AwsMqttService.getInstance().stopAwsService();

    }

    @Override
    public void onNetDisconnected() {

    }

    @Override
    public void onNetConnected(NetworkUtil.NetworkType networkType) {
        networkAvailable = true;
        AwsMqttService.getInstance().reConnect();
    }


    private void configureCrashReporting() {
        if (BuildConfig.DEBUG) {
            FirebaseCrashlytics.getInstance().setCrashlyticsCollectionEnabled(false);
        }
    }


    @Override
    protected void attachBaseContext(Context base) {
        super.attachBaseContext(base);
        fixTimeoutException();
    }


    private void fixTimeoutException() {
        if ("oppo".equalsIgnoreCase(Build.BRAND) && Build.VERSION.SDK_INT < Build.VERSION_CODES.N) {
            try {
                Class clazz = Class.forName("java.lang.Daemons$FinalizerWatchdogDaemon");

                Method method = clazz.getSuperclass().getDeclaredMethod("stop");

                method.setAccessible(true);

                Field field = clazz.getDeclaredField("INSTANCE");

                field.setAccessible(true);

                method.invoke(field.get(null));

            } catch (Exception e) {
                e.printStackTrace();
            }

        }

    }


    private  class MyReactNativeHost extends DefaultReactNativeHost {
        private final String defaultJSName = "index";
        private final String defaultBundleName = "_dll.android.bundle";

        protected MyReactNativeHost(Application application) {
            super(application);
        }

        @Override
        public boolean getUseDeveloperSupport() {
            return false;
        }

        @Override
        protected String getBundleAssetName() {
            return defaultBundleName;
        }

        @Override
        protected String getJSMainModuleName() {
            return defaultJSName;
        }

        @Override
        protected boolean isNewArchEnabled() {
            return BuildConfig.IS_NEW_ARCHITECTURE_ENABLED;
        }

        @Override
        protected Boolean isHermesEnabled() {
            return BuildConfig.IS_HERMES_ENABLED;
        }


        @Override
        protected List<ReactPackage> getPackages() {
            //Auto linking方案
            List<ReactPackage> packages = new PackageList(this).getPackages();
            packages.add(new CH7000HeaderPackage());
            packages.add(new CustomToastPackage());
            packages.add(new StallViewPackage());
            packages.add(new SemiCircleProgressBarPackage());
            packages.add(new AH8BatteryHeaderPackage());
            // 添加自定义异常处理包
            packages.add(new CustomReactPackage());
            return packages;
        }
    }

    /**
     * 获取平台支持的语言列表
     */
    private void getLanguageList() {

        //获取远端语言列表信息
        ApiService.instance().getLanguageList().subscribe(new HttpObserver<LanguageListBean>() {
            @Override
            protected void Next(LanguageListBean entity) {

                if (entity.getEntry() != null && !entity.getEntry().isEmpty()) {
                    LanguageInfoDao languageInfoDao = SoftRoomDatabase.getDatabase(MainApplication.this).languageInfoDao();
                    //插入语言信息数据
                    for (LanguageInfo languageInfo : entity.getEntry()) {
                        languageInfoDao.insert(languageInfo);
                    }
                    //如果当前语种，本地不支持，那么就拉去英文
                    LanguageInfo languageInfo = SoftRoomDatabase.getDatabase(MainApplication.this).languageInfoDao().getLanguageInfoByCode(MyLang.getCurrentLanguageCode());
                    if (null == languageInfo) {
                        getLanguageWithLangCode(Utils.NA_LANGUAGE);
                    } else {
                        getLanguageWithLangCode(MyLang.getCurrentLanguageCode());
                    }
                }
            }
        });
    }


    private void getLanguageWithLangCode(String langCode) {
        //获取远端语言包
        LanguagePackage languagePackage = SoftRoomDatabase.getDatabase(this).languagePackageDao().getLanguageByCode(langCode);
        //本地是否存在语言包，如果有获取
        if (null == languagePackage) {
            //如果不存在语言包 根据LangCode下载语言包，同时更新 lastRequestTs
            getLanguageWithLangCode(langCode, DEFAULT_SYSCODE, DEFAULT_LASTREQUESTTS);
        } else {
            //存在该语种的语言包 根据lastRequestTs获取
            if (TextUtils.isEmpty(languagePackage.getLastRequestTs())) {
                //当本地lastRequestTs为空  那么本地就不存在多语言时间戳，全量拉取该语种多语言
                getLanguageWithLangCode(langCode, DEFAULT_SYSCODE, DEFAULT_LASTREQUESTTS);
            } else {
                String lastRequestTs = languagePackage.getLastRequestTs();
                //当本地lastRequestTs不为空，更新差异化词条
                getLanguageWithLangCode(langCode, DEFAULT_SYSCODE, lastRequestTs);
            }

        }
    }

    /**
     * 获取LangCode下所有的语言
     */
    private void getLanguageWithLangCode(String langCode, String sysCode, String lastRequestTs) {

        ArrayList<String> codeList = new ArrayList<>();
        codeList.add(Utils.enToUSAndGB(langCode));
        LanguageV2Request req = new LanguageV2Request(codeList, sysCode, lastRequestTs);

        ApiService.instance().getLanguagePackageV2(req).subscribe(new HttpObserver<LanguageV2Res>() {
            @RequiresApi(api = Build.VERSION_CODES.O)
            @Override
            protected void Next(LanguageV2Res entity) {
                if (entity.isStatus()) {
                    LanguagePackageDao languagePackageDao = SoftRoomDatabase.getDatabase(MainApplication.this).languagePackageDao();

                    if (null == entity.getEntry()) {
                        return;
                    }
                    if (null == entity.getEntry().getData()) {
                        return;
                    } else {
                        try {
                            String decompress = ObjectListCompressor.decompress(Base64.getDecoder().decode(entity.getEntry().getData()));
                            List<LanguageV2Res.LanguageModel.LanguageItem> langList = new Gson().fromJson(decompress, new TypeToken<List<LanguageV2Res.LanguageModel.LanguageItem>>() {
                            }.getType());
                            entity.getEntry().setDecompressData(langList);
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                    if (entity.getEntry().getDecompressData().isEmpty()) {
                        return;
                    }

                    LanguagePackage languagePackage = new LanguagePackage();
                    Map<String, String> languageMap = new HashMap<>();

                    //如果lastRequestTs = 1000000000
                    if (lastRequestTs.equals(DEFAULT_LASTREQUESTTS)) {
                        //遍历后插入数据库
                        for (LanguageV2Res.LanguageModel.LanguageItem item : entity.getEntry().getDecompressData()) {
                            languageMap.put(item.getLangCode(), item.getContent());
                        }
                    } else {
                        //替换和新增词条
                        languagePackage = languagePackageDao.getLanguageByCode(langCode);
                        languageMap = languagePackage.getApp();
                        for (LanguageV2Res.LanguageModel.LanguageItem item : entity.getEntry().getDecompressData()) {
                            languageMap.put(item.getLangCode(), item.getContent());
                        }
                    }


                    languagePackage.setApp(languageMap);
                    languagePackage.setLang_code(Utils.uSAndGBToEn(langCode));
                    languagePackage.setLastRequestTs(TextUtils.isEmpty(entity.getEntry().getLastTime()) ? Utils.DEFAULT_LANGUAGE_TIME : entity.getEntry().getLastTime());
                    languagePackageDao.insert(languagePackage);

                    applyLanguage(Utils.uSAndGBToEn(langCode));
                }
            }
        });
    }

    private void applyLanguage(String langCode) {
        //加载远端语言包
        MyLang.loadRemoteLanguages(MainApplication.this, new MLang.FinishLoadCallback() {
            @Override
            public void finishLoad() {

                for (LocaleInfo info : MyLang.getInstance().remoteLanguages) {
                    if (info.getLangCode().equalsIgnoreCase(langCode)) {
                        //设置语言
                        MyLang.getInstance().applyLanguage(MainApplication.this, info, true, false, false, true, new MLang.FinishLoadCallback() {
                            @Override
                            public void finishLoad() {
                                LogUtils.e("设置语言完成--" + langCode);
                            }
                        });
                    }
                }

            }
        });
    }
}
