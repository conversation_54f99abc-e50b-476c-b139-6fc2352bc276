package com.chervon.iot;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.MutableLiveData;

import com.blankj.utilcode.util.NetworkUtils;
import com.chervon.iot.repository.MainRepo;
import com.chervon.libBase.ui.viewmodel.BaseViewModel;
import com.chervon.libBase.utils.AppUtils;
import com.chervon.libBase.utils.SingleLiveEvent;
import com.chervon.libDB.SoftRoomDatabase;
import com.chervon.libDB.entities.AppVersionEntry;
import com.chervon.moudleContainer.panel.data.entity.PhoneInfoParam;

import java.util.Map;

/**
 * @ProjectName: app
 * @Package: com.chervon.iot
 * @ClassName: MainViewModel
 * @Description: ViewModel  of shell
 * @Author: wangheng
 * @CreateDate: 2022/4/21 下午7:02
 * @UpdateUser: wangheng
 * @UpdateDate: 2022/4/21 下午7:02
 * @UpdateRemark: new
 * @Version: 1.0
 */
public class MainViewModel extends BaseViewModel {

    MainRepo mMainRepo;

    public final MutableLiveData<PhoneInfoParam> mliveData = new MutableLiveData<>();
    public  MutableLiveData<Boolean> navigationBarLiveData = new SingleLiveEvent<>();
    public  MutableLiveData<AppVersionEntry> versionSwitchData = new SingleLiveEvent<>();

    public MainViewModel(@NonNull Application application) {
        super(application);
        mMainRepo = new MainRepo(application);
    }

    @Override
    public void onCreate(@org.jetbrains.annotations.NotNull LifecycleOwner lifecycleOwner) {
    }


    @Override
    public void onStart(@org.jetbrains.annotations.NotNull LifecycleOwner lifecycleOwner) {

    }

    @Override
    public void onResume(@org.jetbrains.annotations.NotNull LifecycleOwner lifecycleOwner) {

    }

    @Override
    public void onPause(@org.jetbrains.annotations.NotNull LifecycleOwner lifecycleOwner) {

    }

    @Override
    public void onStop(@org.jetbrains.annotations.NotNull LifecycleOwner lifecycleOwner) {

    }

    @Override
    public void onDestroy(@org.jetbrains.annotations.NotNull LifecycleOwner lifecycleOwner) {

    }


    public void getIphoneInfo() {
        String unKnowPhoneModel = "unKnowPhoneModel";
        String replace_phoneModel_left = "(";
        String replace_phoneModel_right = ")";
        String replace_phoneModel_left_result = "[";
        String replace_phoneModel_right_result = "]";

        SoftRoomDatabase.databaseWriteExecutor.execute(() -> {
            String phoneOsVersion = android.os.Build.VERSION.RELEASE;
            String phoneModel = android.os.Build.MODEL;
            try {
                phoneModel = phoneModel.replace(replace_phoneModel_left, replace_phoneModel_left_result);
                phoneModel = phoneModel.replace(replace_phoneModel_right, replace_phoneModel_right_result);
            } catch (Exception e) {
                phoneModel = unKnowPhoneModel;
            }

            String appVersion = AppUtils.getAppVersionName();
            Map rnMap = mMainRepo.getBundleInfoList();
            String ip = NetworkUtils.getIPAddress(true);
            String appTypeCode = "android";
            mliveData.postValue(new PhoneInfoParam(phoneOsVersion, phoneModel, appVersion, rnMap, ip, appTypeCode));

        });
    }

    public void reportIphoneInfo(PhoneInfoParam phoneInfoParam) {
        mMainRepo.reportPhoneInfo(phoneInfoParam);
    }

    public void getNavigationBar(){
      mMainRepo.getNavigationBar(navigationBarLiveData);
    }

    public void getAndroidVersionSwitch(){
        mMainRepo.getAndroidVersionSwitch(versionSwitchData);
    }


}
