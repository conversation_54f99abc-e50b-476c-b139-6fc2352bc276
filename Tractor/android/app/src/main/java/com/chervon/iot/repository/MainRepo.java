package com.chervon.iot.repository;

import android.content.Context;
import android.text.TextUtils;

import androidx.lifecycle.MutableLiveData;

import com.blankj.utilcode.util.LogUtils;
import com.chervon.libBase.model.DeviceDictEntry;
import com.chervon.libBase.model.DictItem;
import com.chervon.libBase.model.DictNode;
import com.chervon.libBase.utils.APPConstants;
import com.chervon.libDB.SoftRoomDatabase;
import com.chervon.libDB.dao.DeviceDao;
import com.chervon.libDB.dao.RnBundleInfoDao;
import com.chervon.libDB.entities.AppVersionEntry;
import com.chervon.libDB.entities.BundleInfoEntry;
import com.chervon.libDB.entities.DeviceInfo;
import com.chervon.libNetwork.http.HttpResponse;
import com.chervon.moudleContainer.panel.data.api.PanelApi;
import com.chervon.moudleContainer.panel.data.entity.PhoneInfoParam;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.reactivex.observers.DefaultObserver;
import io.reactivex.schedulers.Schedulers;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleContainer.panel.data.repository
 * @ClassName: PanelRepo
 * @Description: Data processing warehouse of Panel moudule
 * @Author: wangheng
 * @CreateDate: 2022/4/21 下午7:02
 * @UpdateUser: wangheng
 * @UpdateDate: 2022/4/21 下午7:02
 * @UpdateRemark: new
 * @Version: 1.0
 */
public class MainRepo {
    private RnBundleInfoDao mRnBundleInfoDao;
    private DeviceDao mDeviceInfoDao;

    public MainRepo() {
    }

    public MainRepo(Context context) {
        SoftRoomDatabase db = SoftRoomDatabase.getDatabase(context.getApplicationContext());
        mRnBundleInfoDao = db.rnBundleInfoDao();
        mDeviceInfoDao = db.deviceDao();
    }


    public void reportPhoneInfo(PhoneInfoParam phoneInfoParam) {
        new PanelApi().reportPhoneInfo(phoneInfoParam).subscribeOn(Schedulers.io())
                .observeOn(Schedulers.io()).subscribe(new DefaultObserver<HttpResponse>() {
                    @Override
                    public void onNext(HttpResponse httpResponse) {
                        LogUtils.d("");
                    }

                    @Override
                    public void onError(Throwable e) {
                        LogUtils.d("");
                    }

                    @Override
                    public void onComplete() {

                    }
                });
    }

    public void getNavigationBar(MutableLiveData<Boolean> liveData) {
        new PanelApi().getNavigationBar()
                .subscribeOn(Schedulers.io())
                .observeOn(Schedulers.io())
                .subscribe(new DefaultObserver<HttpResponse<DictItem[]>>() {
                    @Override
                    public void onNext(HttpResponse<DictItem[]> httpResponse) {
                        if (httpResponse.status) {
                            DeviceDictEntry dictEntry = new DeviceDictEntry();
                            dictEntry.setEntry(httpResponse.response);

                            String ExploreShowTag = "1";
                            DictItem[] exploreSwitches = dictEntry.getEntry();
                            if (exploreSwitches.length == 0) {
                                return;
                            }
                            boolean isShowExplore = false;
                            for (DictItem exploreSwitch : exploreSwitches) {
                                for (DictNode node : exploreSwitch.getNodes()) {
                                    if (null != node) {
                                        String label = node.getLabel();
                                        if (!TextUtils.isEmpty(label)) {
                                            if (label.equals(ExploreShowTag)) {
                                                isShowExplore = true;
                                            } else {
                                                isShowExplore = false;
                                            }
                                        }
                                    }
                                }
                            }
                            liveData.postValue(isShowExplore);

                        }
                    }

                    @Override
                    public void onError(Throwable e) {
                        LogUtils.d("getNavigationBar is error--" + e.getMessage());
                    }

                    @Override
                    public void onComplete() {
                    }
                });
    }

    public void getAndroidVersionSwitch(MutableLiveData<AppVersionEntry> liveData) {
        new PanelApi().getAndroidVersionSwitch()
                .subscribeOn(Schedulers.io())
                .observeOn(Schedulers.io())
                .subscribe(new DefaultObserver<HttpResponse<DictItem[]>>() {
                    @Override
                    public void onNext(HttpResponse<DictItem[]> httpResponse) {
                        if (httpResponse.status) {
                            DeviceDictEntry dictEntry = new DeviceDictEntry();
                            dictEntry.setEntry(httpResponse.response);
                            AppVersionEntry appDictModel = new AppVersionEntry();
                            DictItem[] dictItems = dictEntry.getEntry();
                            if (dictItems.length == 0) {
                                return;
                            }

                            for (DictItem dictItem : dictItems) {
                                for (DictNode node : dictItem.getNodes()) {
                                    if (null != node) {
                                        String label = node.getLabel();
                                        if (!TextUtils.isEmpty(label)) {
                                            if (label.equalsIgnoreCase(APPConstants.VERSION_NAME_KEY)) {
                                                appDictModel.setVersionName(node.getDescription());
                                            }
                                            if (label.equalsIgnoreCase(APPConstants.VERSION_CODE_KEY)) {
                                                appDictModel.setVersionCode(Integer.parseInt(node.getDescription()));
                                            }
                                            if (label.equalsIgnoreCase(APPConstants.FORCE_DIALOG_KEY)) {
                                                if (!TextUtils.isEmpty(node.getDescription())){
                                                    appDictModel.setForceDialog(node.getDescription().equalsIgnoreCase("true")?1:0);
                                                }else {
                                                    appDictModel.setForceDialog(0);
                                                }

                                            }
                                        }
                                    }
                                }
                            }

                            liveData.postValue(appDictModel);

                        }
                    }

                    @Override
                    public void onError(Throwable e) {
                        LogUtils.d("getNavigationBar is error--" + e.getMessage());
                    }

                    @Override
                    public void onComplete() {
                    }
                });
    }


    public Map getBundleInfoList() {
        Map rnMap = new HashMap();
        List<DeviceInfo> deviceInfoslist = mDeviceInfoDao.getDevices();
        for (int i = 0; deviceInfoslist != null && i < deviceInfoslist.size(); i++) {
            DeviceInfo deviceInfo = deviceInfoslist.get(i);
            try {
                BundleInfoEntry bundleInfoEntry = mRnBundleInfoDao.getBundleInfo(Long.parseLong(deviceInfo.getProductId()));
                if (bundleInfoEntry != null) {
                    rnMap.put(deviceInfo.getDeviceId(), bundleInfoEntry.getLastRnVersion());
                }
            } catch (Exception e) {
                LogUtils.e("MainRepo", "getBundleInfoList is exception");
            }


        }
        return rnMap;
    }


}
