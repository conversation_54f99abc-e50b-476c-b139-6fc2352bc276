package com.chervon.iot;

import static com.chervon.libBase.utils.Utils.sendClickTrace;

import com.chervon.libBase.BuildConfig;
import com.chervon.libBase.ui.dialog.AppVersionService;
import com.chervon.libBase.ui.widget.QFBottomNavigationView;

import static com.chervon.libRouter.RouterConstants.ACTIVITY_URL_USER_HOME;
import static com.chervon.libRouter.RouterConstants.KEY_FROM;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_CREATE_TIME;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_DATA;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_MESSAGETYPE;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_UUID;
import static com.chervon.libRouter.RouterConstants.KEY_ROUTER_PATH;
import static com.chervon.libRouter.RouterConstants.MESSAGE_DATA_KEY;
import static com.chervon.libRouter.RouterConstants.REQUEST_CODE;
import static com.chervon.libRouter.RouterConstants.REQUEST_CODE_MAIN_TO_MESSAGE_DETAIL;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.location.LocationManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.Menu;
import android.view.MenuItem;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.alibaba.android.arouter.launcher.ARouter;
import com.blankj.utilcode.util.GsonUtils;
import com.blankj.utilcode.util.LogUtils;
import com.chervon.iot.databinding.ActivityMainBinding;
import com.chervon.libBase.BaseApplication;
import com.chervon.libBase.model.UserInfo;
import com.chervon.libBase.ui.UserTokenListernner;
import com.chervon.libBase.utils.AppConfig;
import com.chervon.libBase.utils.AppExitHelper;
import com.chervon.libBase.utils.AppUtils;
import com.chervon.libBase.utils.UiHelper;
import com.chervon.libBase.utils.Utils;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libBluetooth.BluetoothService;
import com.chervon.libDB.SoftRoomDatabase;
import com.chervon.libDB.entities.AppVersionEntry;
import com.chervon.libDB.entities.User;
import com.chervon.libNetwork.http.ApiService;
import com.chervon.libNetwork.http.HttpObserver;
import com.chervon.libNetwork.http.model.request.SetPushTokenRequest;
import com.chervon.libNetwork.http.model.result.BaseResult;
import com.chervon.libNetwork.http.model.result.MessageListBean;
import com.chervon.libNetwork.iot.AwsMqttService;
import com.chervon.module_explore.ExploreApplication;
import com.chervon.moudleContainer.panel.data.entity.PhoneInfoParam;
import com.google.android.gms.tasks.OnCompleteListener;
import com.google.android.gms.tasks.Task;
import com.google.firebase.iid.FirebaseInstanceId;
import com.google.firebase.iid.InstanceIdResult;
import com.tbruyelle.rxpermissions2.Permission;
import com.tbruyelle.rxpermissions2.RxPermissions;
import com.chervon.libRouter.RouterConstants;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.Observer;
import androidx.navigation.NavController;
import androidx.navigation.NavGraph;
import androidx.navigation.NavInflater;
import androidx.navigation.Navigation;

import com.chervon.libBase.ui.BaseActivity;

import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Consumer;


/**
 * @ProjectName: app
 * @Package: com.chervon.iot
 * @ClassName: MainActivity
 * @Description: Main interface of shell
 * @Author: wangheng
 * @CreateDate: 2022/4/21 下午7:02
 * @UpdateUser: wangheng
 * @UpdateDate: 2022/4/21 下午7:02
 * @UpdateRemark: new
 * @Version: 1.0
 */


@Route(path = RouterConstants.ACTIVITY_URL_HOME)
public class MainActivity extends BaseActivity<MainViewModel> {
    private final String TAG = "MainActivity";
    private MenuItem homeMenu;
    private MenuItem mineMenu;
    private MenuItem exploreMenu;
    private AppExitHelper mAppExitHelper;
    ActivityMainBinding mViewBinding;
    LocationManager mLocationManager;
    private static final String[] permissionsGroup = new String[]{
            Manifest.permission.SYSTEM_ALERT_WINDOW
            , Manifest.permission.CAMERA
            , Manifest.permission.ACCESS_COARSE_LOCATION
            , Manifest.permission.ACCESS_FINE_LOCATION
            , Manifest.permission.READ_EXTERNAL_STORAGE
            , Manifest.permission.WRITE_EXTERNAL_STORAGE
            , Manifest.permission.POST_NOTIFICATIONS
    };
    static MainApplication application;
    private String exploreShowTag = "1";
    private String exploreHiddenTag = "0";

    private boolean isVisibleExplore = true;

    @Override
    protected void onUnRegister() {

    }

    @Override
    protected void onRegister() {
        dealWithMessage();
    }

    @Override
    protected Integer getLayoutId() {
        return R.layout.activity_main;
    }

    @Override
    protected void initViews(ViewDataBinding viewDataBinding) {
        LogUtils.d(TAG, "initViews start");
        mViewBinding = (ActivityMainBinding) viewDataBinding;

        int fromId = getIntent().getIntExtra(KEY_FROM, 0);
        if (fromId == com.chervon.moudleOobe.R.id.loginFragment) {
            initFCM();
        }

        LogUtils.d(TAG, "initViews: getting navigation view");

        try {
            User user = SoftRoomDatabase.getDatabase(this).userDao().getUser();
            if (user != null && user.getIsVisibleExplore() != null) {
                String isVisibleExploreFromDb = user.getIsVisibleExplore();
                if (TextUtils.isEmpty(isVisibleExploreFromDb)) {
                    isVisibleExplore = true;
                } else {
                    isVisibleExplore = isVisibleExploreFromDb.equals(exploreShowTag);
                }
                LogUtils.e(TAG, "本地导航栏是显示explore---" + isVisibleExplore);
            } else {
                LogUtils.e(TAG, "User or isVisibleExplore is null");
            }
        } catch (Exception e) {
            LogUtils.e(TAG, "getIsVisibleExplore is exception---" + e.getMessage());
        }


        setNavController();

    }


    private void setNavController() {
        int keyFrom = getIntent().getIntExtra(KEY_FROM, com.chervon.moudleOobe.R.id.loginFragment);
        // 获取底部导航视图
        QFBottomNavigationView navView = mViewBinding.navigationBar.navView;
        if (navView == null) {
            LogUtils.e(TAG, "initViews: nav_view not found");
            return;
        }
        LogUtils.d(TAG, "initViews: navigation view found");

        // 设置菜单项标题
        Menu menu = navView.getMenu();
        homeMenu = menu.findItem(R.id.homeFragment);
        mineMenu = menu.findItem(R.id.mineFragment);
        exploreMenu = menu.findItem(R.id.exploreFragment);


        // 设置导航控制器和导航图
        navController = Navigation.findNavController(this, R.id.nav_host_fragment_content_main);
        NavInflater inflater = navController.getNavInflater();
        NavGraph navGraph = inflater.inflate(R.navigation.nav_graph_contain_explore_app);
        navController.setGraph(navGraph);

        if (!BuildConfig.EVN.equalsIgnoreCase(Utils.EVN_NA)) {
            isVisibleExplore = false;
        }
        // 根据isHiddenExplore控制Explore模块的显示
        MenuItem exploreMenu = menu.findItem(R.id.exploreFragment);
        if (exploreMenu != null) {
            exploreMenu.setVisible(isVisibleExplore);
            if (keyFrom == R.id.loginFragment) {
                //来源与登陆页面按照exploreFragment优先级处理
                if (isVisibleExplore) {
                    navController.navigate(R.id.exploreFragment);
                    BaseApplication.showCurrentFragmentLayoutId = R.id.exploreFragment;

                } else {
                    BaseApplication.showCurrentFragmentLayoutId = R.id.homeFragment;
                }
                getIntent().removeExtra(KEY_FROM);
            } else {
                //非登陆页面按点击逻辑处理
                if (BaseApplication.showCurrentFragmentLayoutId == -1) {
                    if (isVisibleExplore) {
                        navController.navigate(R.id.exploreFragment);
                        BaseApplication.showCurrentFragmentLayoutId = R.id.exploreFragment;

                    } else {
                        BaseApplication.showCurrentFragmentLayoutId = R.id.homeFragment;
                    }
                }
            }

        }

        // 设置导航监听
        navController.addOnDestinationChangedListener((controller, destination, arguments) -> {
            sendFragmentClick(destination.getId());
            if (BaseApplication.showCurrentFragmentLayoutId == R.id.homeFragment) {
                //当前Fragment是homeFragment
                LogUtils.e("addOnDestinationChangedListener--homeFragment");

            } else if (BaseApplication.showCurrentFragmentLayoutId == R.id.exploreFragment) {
                //当前Fragment是exploreFragment
                LogUtils.e("addOnDestinationChangedListener--exploreFragment");


            } else if (BaseApplication.showCurrentFragmentLayoutId == R.id.mineFragment) {
                //当前Fragment是mineFragment
                LogUtils.e("addOnDestinationChangedListener--mineFragment");

            }
            BaseApplication.showCurrentFragmentLayoutId = destination.getId();
        });
    }

    @Override
    protected void initData(Bundle savedInstanceState) {

        application = (MainApplication) getApplication();

        mViewModel.getIphoneInfo();

        if (AppConfig.featureWithNaAndAnz()) {
            mViewModel.getNavigationBar();
        }else {
            getAppVersion();
        }

        mViewModel.mliveData.observe(this, new Observer<PhoneInfoParam>() {
            @Override
            public void onChanged(PhoneInfoParam phoneInfoParam) {
                if (phoneInfoParam != null) {
                    mViewModel.reportIphoneInfo(phoneInfoParam);
                }
            }
        });

        mViewModel.navigationBarLiveData.observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean isVisible) {
                LogUtils.e(TAG, "接口字典导航栏是显示explore---" + isVisibleExplore);

                //后台配置字典和本地不一致，那么就更新底部导航栏
                if (isVisibleExplore != isVisible) {
                    isVisibleExplore = isVisible;
                    LogUtils.e(TAG, "导航栏更新显示explore---" + isVisibleExplore);
                    setNavController();
                    User user = SoftRoomDatabase.getDatabase(MainActivity.this).userDao().getUser();
                    user.setIsVisibleExplore(isVisible ? exploreShowTag : exploreHiddenTag);
                    SoftRoomDatabase.getDatabase(MainActivity.this).userDao().updateUser(user);
                }

                getAppVersion();
            }
        });

        mViewModel.versionSwitchData.observe(this, new Observer<AppVersionEntry>() {
            @Override
            public void onChanged(AppVersionEntry appVersionEntry) {
                if (null!=appVersionEntry){
                    if (isVisibleExplore && BaseApplication.showCurrentFragmentLayoutId == R.id.exploreFragment){
                        AppVersionService.getInstance().simpleConfirmDialog(MainActivity.this,appVersionEntry);
                    }else if (!isVisibleExplore&&BaseApplication.showCurrentFragmentLayoutId == R.id.homeFragment){
                        AppVersionService.getInstance().simpleConfirmDialog(MainActivity.this,appVersionEntry);
                    }
                }
            }
        });

        mAppExitHelper = new AppExitHelper();

        Bundle bundle = getIntent().getExtras();
        if (bundle != null) {
            String routerPath = getIntent().getExtras().getString(KEY_ROUTER_PATH);
            if ((!TextUtils.isEmpty(routerPath) && ACTIVITY_URL_USER_HOME.equals(routerPath))) {
                NavController controller = Navigation.findNavController(this, R.id.nav_host_fragment_content_main);
                controller.setGraph(R.navigation.nav_graph_contain_explore_app);
                controller.navigate(R.id.mineFragment);
            }
        }

    }

    @Override
    public void onStart() {
        super.onStart();
        initPermission();
        //切换语言需要刷新底部menu文案
        if (homeMenu != null) {
            homeMenu.setTitle(LanguageStrings.app_tabbar_home_textview_text());
        }
        if (mineMenu != null) {
            mineMenu.setTitle(LanguageStrings.app_tabbar_user_textview_text());
        }
        if (exploreMenu != null) {
            exploreMenu.setTitle(LanguageStrings.app_tabbar_explore_textview_text());
        }
    }

    // App层面动态申请权限
    private void initPermission() {
        RxPermissions rxPermissions = new RxPermissions(this);
        rxPermissions.requestEach(permissionsGroup)
                .subscribe(new Consumer<Permission>() {
                    @Override
                    public void accept(Permission permission) throws Exception {
                        checkBluePermission(rxPermissions);
                    }
                });
    }

    // 蓝牙层面动态申请权限
    private void checkBluePermission(RxPermissions rxPermissions) {

        if (ContextCompat.checkSelfPermission(MainActivity.this, Manifest.permission.BLUETOOTH_SCAN) == PackageManager.PERMISSION_GRANTED) {

            BluetoothService.getInstance().PERMISSION_GRANTED = true;

        } else {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                String[] bluetoothPermission = new String[]{Manifest.permission.BLUETOOTH_CONNECT, Manifest.permission.BLUETOOTH_SCAN};
                rxPermissions.request(bluetoothPermission).subscribe(new io.reactivex.Observer<Boolean>() {
                    @Override
                    public void onSubscribe(@NonNull Disposable d) {
                    }

                    @Override
                    public void onNext(@NonNull Boolean aBoolean) {
                        BluetoothService.getInstance().PERMISSION_GRANTED = aBoolean;
                    }

                    @Override
                    public void onError(@NonNull Throwable e) {
                    }

                    @Override
                    public void onComplete() {
                    }
                });

            } else {
                if (ContextCompat.checkSelfPermission(MainActivity.this, Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED) {
                    BluetoothService.getInstance().PERMISSION_GRANTED = true;
                } else {
                    BluetoothService.getInstance().PERMISSION_GRANTED = false;
                }

            }
        }

    }


    @Override
    protected Class getViewModelClass() {
        return MainViewModel.class;
    }


    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_DOWN) {
            goBack();
            mAppExitHelper.appExit(this);
            return false;
        }
        return super.onKeyDown(keyCode, event);

    }


    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        super.onWindowFocusChanged(hasFocus);
        if (hasFocus) {
            UiHelper.hideSystemUI(getWindow());
        }
    }


    private void initFCM() {
        String gmsServiceError = "GMS Service is Unable";
        if (AppUtils.googleApiAvailability()) {
            FirebaseInstanceId.getInstance().getInstanceId().addOnCompleteListener(new MyOnCompleteListener());
        } else {
            LogUtils.e(TAG, gmsServiceError);
        }
    }

    private void goBack() {
        if (mkeyBackListener != null) {
            if (mkeyBackListener.OnkeyBack()) {
                mkeyBackListener = null;
            }
        }
    }


    @Override
    protected void onDestroy() {
        super.onDestroy();

        UserInfo.addTokenListener(null);
        mkeyBackListener = null;
        LogUtils.d("MainActivity_BluetoothConection.getInstance().disConnectDevice()");
        ExploreApplication.onDestroyView = true;
        BaseApplication.getInstance().destroyVideoManager();
    }

    @Override
    protected void onPause() {
        super.onPause();

        mLocationManager = null;

    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
    }

    private void sendFragmentClick(int destination) {
        String moduleId = "";
        String pageId = "";
        String eleId = "";
        String eventId = "1";
        String mPageResouce = BaseApplication.getInstance().getCurrentPageResouce();
        if (BaseApplication.showCurrentFragmentLayoutId == R.id.homeFragment) {
            //当前Fragment是homeFragment
            if (destination == R.id.exploreFragment) {
                //跳转到exploreFragment--5_9_0_10_click
                moduleId = "5";
                pageId = "9";
                eleId = "10";
            } else {
                //跳转到mineFragment--5_9_0_3_click
                moduleId = "5";
                pageId = "9";
                eleId = "3";
            }

        } else if (BaseApplication.showCurrentFragmentLayoutId == R.id.exploreFragment) {
            //当前Fragment是exploreFragment
            if (destination == R.id.homeFragment) {
                //跳转到homeFragment 106_551_0_8_click
                moduleId = "106";
                pageId = "551";
                eleId = "8";
            } else {
                //跳转到mineFragment   106_551_0_9_click
                moduleId = "106";
                pageId = "551";
                eleId = "9";
            }

        } else if (BaseApplication.showCurrentFragmentLayoutId == R.id.mineFragment) {
            //当前Fragment是mineFragment
            if (destination == R.id.homeFragment) {
                //跳转到homeFragment 11_88_0_7_click
                moduleId = "11";
                pageId = "88";
                eleId = "7";
            } else {
                //跳转到explore---11_88_0_11_click
                moduleId = "11";
                pageId = "88";
                eleId = "11";
            }
        }
        if (!TextUtils.isEmpty(moduleId) && !TextUtils.isEmpty(pageId) && !TextUtils.isEmpty(eleId)) {
            mPageResouce = mPageResouce + "_" + pageId;
            sendClickTrace(this, moduleId, pageId, mPageResouce, eleId, eventId);
        }

    }


    public static class MyOnCompleteListener implements OnCompleteListener<InstanceIdResult> {

        @Override
        public void onComplete(@NonNull Task<InstanceIdResult> task) {
            if (!task.isSuccessful()) {
                LogUtils.eTag("FCM", "getInstanceId failed", task.getException());
                return;
            }
            // Get new Instance ID token
            InstanceIdResult result = task.getResult();
            if (result != null) {
                String token = result.getToken();
                //保存FCM token
                User user = UserInfo.get();
                user.setFcmToken(token);
                UserInfo.set(user);
                UserInfo.addTokenListener(new UserTokenListernner() {
                    @Override
                    public void onTokenChange() {
                        try {
                            FirebaseInstanceId.getInstance().deleteInstanceId();
                            AwsMqttService.getInstance().mqttDisconnect();
                        } catch (Exception e) {
                            e.printStackTrace();
                        }

                    }
                });
                LogUtils.iTag("FCM", "FCM_TOKEN:" + token);
                LogUtils.iTag("FCM", "userid:" + user.getId());
                ApiService.instance()
                        .setPushToken(new SetPushTokenRequest(token))
                        .subscribe(new HttpObserver<BaseResult>() {
                            @Override
                            protected void Next(BaseResult entity) {
                                if (entity.isStatus()) {
                                    LogUtils.d("Fcm_SetPushTokenRequest");
                                }

                            }
                        });
            }

        }


    }

    private void dealWithMessage() {

        try {
            String FEEDBACK_TAG = "3";
            Bundle bundle = getIntent().getExtras();
            if (bundle != null) {

                String messageContent = bundle.getString(MESSAGE_DATA_KEY);
                if (!TextUtils.isEmpty(messageContent)) {
                    MessageListBean.EntryDTO.ListDTO messageData = GsonUtils.fromJson(messageContent, MessageListBean.EntryDTO.ListDTO.class);
                    String FEED_BACK_ID = "feedbackId";
                    MessageListBean.EntryDTO.ListDTO.PayloadDataDTO payload = messageData.getPayloadData();
                    String rutePath = payload.getRutePath();
                    String uuid = payload.getUuid();
                    if (payload.getMessageType().equals(FEEDBACK_TAG)) {
                        if (!TextUtils.isEmpty(rutePath)) {
                            String path = Uri.parse(rutePath).getPath();
                            String feedbackId = Uri.parse(rutePath).getQueryParameter(FEED_BACK_ID);
                            if (TextUtils.isEmpty(path) || TextUtils.isEmpty(feedbackId)) {
                                return;
                            }
                            getIntent().removeExtra(MESSAGE_DATA_KEY);
                            ARouter.getInstance().build(path).withString(KEY_PREV_DATA, feedbackId)
                                    .withString(KEY_PREV_CREATE_TIME, payload.getCreateTime())
                                    .withString(KEY_PREV_MESSAGETYPE, payload.getMessageType())
                                    .withInt(REQUEST_CODE, REQUEST_CODE_MAIN_TO_MESSAGE_DETAIL)
                                    .withString(KEY_PREV_UUID, uuid).navigation();
                        }
                    }
                }
            }

        } catch (Exception e) {
            LogUtils.i(TAG, "viewMore is error--->" + e.getMessage());
        }

    }

    private void getAppVersion() {
        mViewModel.getAndroidVersionSwitch();
    }

}
