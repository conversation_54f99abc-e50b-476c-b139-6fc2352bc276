<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_graph"
    app:startDestination="@id/homeFragment"
    tools:ignore="MissingDefaultResource">

    <fragment
        android:id="@+id/homeFragment"
        android:name="com.chervon.moudleDeviceManage.ui.DeviceListFragment"
        android:label="@string/login_fragment_label"
         >

    </fragment>
    <fragment
        android:id="@+id/mineFragment"
        android:name="com.chervon.moudleUserCenter.ui.UserCenterFragment"
        android:label="@string/login_fragment_label"
      >

    </fragment>

</navigation>
