<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
<data>

    <variable
        name="toolbarData"
        type="com.chervon.libBase.model.ToolbarData" />

</data>

<androidx.constraintlayout.widget.ConstraintLayout
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorBackground">


    <include
        android:id="@+id/clBar"
        layout="@layout/base_toolbar_layout"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:visibility="gone"
        />



    <fragment
        android:id="@+id/nav_host_fragment_content_main"
        android:name="androidx.navigation.fragment.NavHostFragment"
        android:layout_width="0mm"
        android:layout_height="0mm"
        app:defaultNavHost="true"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@id/navigationBar"
        app:navGraph="@navigation/nav_graph_contain_explore_app" />


    <include
        android:id="@+id/navigationBar"
        layout="@layout/base_navigation_bar"
        android:layout_width="match_parent"
        android:layout_height="119mm"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        />


</androidx.constraintlayout.widget.ConstraintLayout>
</layout>
