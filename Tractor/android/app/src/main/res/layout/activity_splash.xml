<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <import type="com.chervon.libBase.utils.mlang.LanguageStrings" />
        <variable
            name="presenter"
            type="com.chervon.moudleOobe.ui.LoginHomePageActivity" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        >

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/middleLine"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintGuide_percent="0.5999" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/leftLine"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.066" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/rightLine"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.934" />

        <ImageView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"

            android:src="@mipmap/login_background"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:layout_width="650mm"
            android:layout_height="612mm"
            android:layout_marginTop="64mm"
            android:scaleType="fitCenter"
            android:src="@mipmap/login_logo"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />









    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
