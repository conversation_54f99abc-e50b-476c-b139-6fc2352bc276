{"name": "react-native", "version": "0.63.2", "bin": "./cli.js", "description": "A framework for building native apps using React", "license": "MIT", "repository": {"type": "git", "url": "**************:facebook/react-native.git"}, "engines": {"node": ">=10"}, "jest-junit": {"outputDirectory": "reports/junit", "outputName": "js-test-results.xml"}, "files": ["!template/node_modules", "!template/package-lock.json", "!template/yarn.lock", ".flowconfig", "android", "cli.js", "flow", "index.js", "init.sh", "interface.js", "jest-preset.js", "jest", "lib", "Libraries", "LICENSE", "local-cli", "packager", "React-Core.podspec", "react-native.config.js", "react.gradle", "React.podspec", "React", "ReactAndroid", "ReactCommon", "README.md", "rn-get-polyfills.js", "scripts/react_native_pods.rb", "scripts/compose-source-maps.js", "scripts/ios-configure-glog.sh", "scripts/launchPackager.bat", "scripts/launchPackager.command", "scripts/node-binary.sh", "scripts/packager.sh", "scripts/react-native-xcode.sh", "template.config.js", "template", "third-party-podspecs"], "scripts": {"start": "react-native start", "test": "jest", "test-ci": "jest --maxWorkers=2 --ci --reporters=\"default\" --reporters=\"jest-junit\"", "flow": "flow", "flow-check-ios": "flow check", "flow-check-android": "flow check --flowconfig-name .flowconfig.android", "lint": "eslint .", "lint-ci": "./scripts/circleci/analyze_code.sh && yarn shellcheck", "shellcheck": "./scripts/circleci/analyze_scripts.sh", "clang-format": "clang-format -i --glob=*/**/*.{h,cpp,m,mm}", "format": "npm run prettier && npm run clang-format", "prettier": "prettier --write \"./**/*.{js,md,yml}\"", "format-check": "prettier --list-different \"./**/*.{js,md,yml}\"", "docker-setup-android": "docker pull reactnativecommunity/react-native-android", "docker-build-android": "docker build -t reactnativeci/android -f .circleci/Dockerfiles/Dockerfile.android .", "test-android-run-instrumentation": "docker run --cap-add=SYS_ADMIN -it reactnativeci/android bash .circleci/Dockerfiles/scripts/run-android-docker-instrumentation-tests.sh", "test-android-run-unit": "docker run --cap-add=SYS_ADMIN -it reactnativeci/android bash .circleci/Dockerfiles/scripts/run-android-docker-unit-tests.sh", "test-android-run-e2e": "docker run --privileged -it reactnativeci/android bash .circleci/Dockerfiles/scripts/run-ci-e2e-tests.sh --android --js", "test-android-all": "yarn run docker-build-android && yarn run test-android-run-unit && yarn run test-android-run-instrumentation && yarn run test-android-run-e2e", "test-android-instrumentation": "yarn run docker-build-android && yarn run test-android-run-instrumentation", "test-android-unit": "yarn run docker-build-android && yarn run test-android-run-unit", "test-android-e2e": "yarn run docker-build-android && yarn run test-android-run-e2e", "build-ios-e2e": "detox build -c ios.sim.release", "test-ios-e2e": "detox test -c ios.sim.release RNTester/e2e", "test-ios": "./scripts/objc-test.sh test"}, "peerDependencies": {"react": "16.13.1"}, "dependencies": {"@babel/runtime": "^7.0.0", "@react-native-community/cli": "^4.7.0", "@react-native-community/cli-platform-android": "^4.7.0", "@react-native-community/cli-platform-ios": "^4.7.0", "abort-controller": "^3.0.0", "anser": "^1.4.9", "base64-js": "^1.1.2", "event-target-shim": "^5.0.1", "fbjs": "^1.0.0", "fbjs-scripts": "^1.1.0", "hermes-engine": "~0.5.0", "invariant": "^2.2.4", "jsc-android": "^245459.0.0", "metro-babel-register": "0.59.0", "metro-react-native-babel-transformer": "0.59.0", "metro-source-map": "0.59.0", "nullthrows": "^1.1.1", "pretty-format": "^24.9.0", "promise": "^8.0.3", "prop-types": "^15.7.2", "react-devtools-core": "^4.6.0", "react-refresh": "^0.4.0", "regenerator-runtime": "^0.13.2", "scheduler": "0.19.1", "stacktrace-parser": "^0.1.3", "use-subscription": "^1.0.0", "whatwg-fetch": "^3.0.0"}, "devDependencies": {"@babel/core": "^7.0.0", "@babel/generator": "^7.5.0", "@react-native-community/eslint-plugin": "file:packages/eslint-plugin-react-native-community", "@reactions/component": "^2.0.2", "async": "^2.4.0", "babel-eslint": "10.0.1", "clang-format": "^1.2.4", "connect": "^3.6.5", "coveralls": "^3.0.2", "detox": "15.4.4", "eslint": "5.1.0", "eslint-config-fb-strict": "^24.9.0", "eslint-config-fbjs": "2.1.0", "eslint-config-prettier": "^6.0.0", "eslint-plugin-babel": "^5.3.0", "eslint-plugin-eslint-comments": "^3.1.1", "eslint-plugin-flowtype": "2.50.3", "eslint-plugin-jest": "22.4.1", "eslint-plugin-jsx-a11y": "6.2.1", "eslint-plugin-prettier": "2.6.2", "eslint-plugin-react": "7.12.4", "eslint-plugin-react-hooks": "^3.0.0", "eslint-plugin-react-native": "3.8.1", "eslint-plugin-relay": "1.7.0", "flow-bin": "^0.122.0", "flow-remove-types": "1.2.3", "jest": "^24.9.0", "jest-junit": "^6.3.0", "jscodeshift": "^0.7.0", "mkdirp": "^0.5.1", "prettier": "1.17.0", "react": "16.13.1", "react-test-renderer": "16.13.1", "shelljs": "^0.7.8", "signedsource": "^1.0.0", "ws": "^6.1.4", "yargs": "^14.2.0"}, "detox": {"test-runner": "jest", "runner-config": "RNTester/e2e/config.json", "specs": "", "configurations": {"android.emu.release": {"binaryPath": "RNTester/android/app/build/outputs/apk/hermes/release/app-hermes-x86-release.apk", "testBinaryPath": "RNTester/android/app/build/outputs/apk/androidTest/hermes/release/app-hermes-release-androidTest.apk", "build": "./gradlew RNTester:android:app:assembleRelease RNTester:android:app:assembleAndroidTest -DtestBuildType=release", "type": "android.emulator", "device": {"avdName": "Nexus_6_API_29"}}, "android.emu.debug": {"binaryPath": "RNTester/android/app/build/outputs/apk/hermes/debug/app-hermes-x86-debug.apk", "testBinaryPath": "RNTester/android/app/build/outputs/apk/androidTest/hermes/debug/app-hermes-debug-androidTest.apk", "build": "./gradlew RNTester:android:app:assembleDebug RNTester:android:app:assembleAndroidTest -DtestBuildType=debug", "type": "android.emulator", "device": {"avdName": "Nexus_6_API_29"}}, "ios.sim.release": {"binaryPath": "RNTester/build/Build/Products/Release-iphonesimulator/RNTester.app/", "build": "xcodebuild -workspace RNTester/RNTesterPods.xcworkspace -scheme RNTester -configuration Release -sdk iphonesimulator -derivedDataPath RNTester/build -UseModernBuildSystem=NO -quiet", "type": "ios.simulator", "name": "iPhone 8"}, "ios.sim.debug": {"binaryPath": "RNTester/build/Build/Products/Debug-iphonesimulator/RNTester.app/", "build": "xcodebuild -workspace RNTester/RNTesterPods.xcworkspace -scheme RNTester -configuration Debug -sdk iphonesimulator -derivedDataPath RNTester/build -UseModernBuildSystem=NO -quiet", "type": "ios.simulator", "name": "iPhone 8"}}}}