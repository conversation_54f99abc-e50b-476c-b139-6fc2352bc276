<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:tools="http://schemas.android.com/tools"
  package="com.chervon.iot">

  <uses-feature android:name="android.hardware.location.gps" />
  <uses-permission android:name="android.permission.INTERNET" />
  <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
  <uses-permission android:name="android.permission.SYSTEM_OVERLAY_WINDOW" />
  <uses-permission android:name="android.permission.CAMERA" />
  <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
  <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
  <uses-permission android:name="android.permission.FLASHLIGHT" />
  <uses-permission android:name="android.permission.READ_PHONE_STATE" />
  <uses-permission android:name="android.settings.MANAGE_ALL_FILES_ACCESS_PERMISSION" />

<!--  <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />-->
<!--  <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />-->
<!--  <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />-->
<!--  <uses-permission android:name="android.permission.ACCESS_MEDIA_LOCATION" />-->

  <!--  蓝牙相关-->
  <uses-permission android:name="android.permission.BLUETOOTH"  android:maxSdkVersion="30"/>
  <uses-permission android:name="android.permission.BLUETOOTH_ADMIN"  android:maxSdkVersion="30" />

  <!-- Needed only if your app looks for Bluetooth devices.You must add an attribute to this permission, or declare the
       ACCESS_FINE_LOCATION permission, depending on the results when you check location usage in your app. -->
  <uses-permission android:name="android.permission.BLUETOOTH_SCAN" android:usesPermissionFlags="neverForLocation" />

  <!--  <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />-->
  <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
  <!--如果你的应用程序支持一个服务，并且可以运行在Android 10 (API级别29)或Android 11，你还必须声明ACCESS_BACKGROUND_LOCATION权限来发现蓝牙设备。-->
<!--  <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />-->

  <uses-permission android:name="android.permission.BIND_AUTOFILL_SERVICE" tools:ignore="ProtectedPermissions" />



  <application
    android:name=".MainApplication"
    android:allowBackup="false"
    android:icon="${app_icon}"
    android:label="@string/app_name"
    android:largeHeap="true"
    android:roundIcon="${app_icon_round}"
    android:supportsRtl="true"
    android:theme="@style/Theme.BottomNavgationApplication"
    android:usesCleartextTraffic="true"
    tools:ignore="GoogleAppIndexingWarning"
    android:requestLegacyExternalStorage="true"
    android:autofillHints="noAutofill"
    tools:targetApi="33">

    <!--        RN-->
    <activity android:name="com.facebook.react.devsupport.DevSettingsActivity" />


    <activity
      android:name="com.chervon.moudleOobe.ui.LoginHomePageActivity"
      android:configChanges="orientation|screenSize|keyboardHidden|fontScale|uiMode"
      android:theme="@style/LoginHomeTheme"
      android:exported="true">
      <intent-filter>
        <action android:name="android.intent.action.MAIN" />
        <category android:name="android.intent.category.LAUNCHER" />
      </intent-filter>
    </activity>

    <activity
      android:name=".MainActivity"
      android:configChanges="orientation|screenLayout|screenSize|smallestScreenSize|keyboardHidden|fontScale|uiMode"
      android:screenOrientation="portrait"
      android:exported="true">
    </activity>

    <provider
      android:name="androidx.core.content.FileProvider"
      android:authorities="${package_provider}"
      android:exported="false"
      android:grantUriPermissions="true">
      <meta-data
        android:name="android.support.FILE_PROVIDER_PATHS"
        android:resource="@xml/file_path" />
    </provider>
    <meta-data
      android:name="com.google.android.geo.API_KEY"
      android:value="${map_key}" />

  </application>
</manifest>
