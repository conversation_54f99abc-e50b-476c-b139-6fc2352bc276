


-dontskipnonpubliclibraryclasses

-dontskipnonpubliclibraryclassmembers

-dontoptimize

-dontpreverify

-ignorewarnings

-optimizations !code/simplification/arithmetic,!field/*,!class/merging/*

-keepattributes *Annotation*

-keepattributes Signature

-keepattributes SourceFile,LineNumberTable

-allowaccessmodification
-repackageclasses ''

-verbose


-keep public class * extends android.app.Activity
-keep public class * extends android.app.Application
-keep public class * extends android.app.Service
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider
-keep public class * extends android.app.backup.BackupAgentHelper
-keep public class * extends android.preference.Preference
-keep public class * extends android.view.View
-keep class android.support.** {*;}

#----------------------------------------------------

#
-keep public class * extends android.support.v4.**
-keep public class * extends android.support.v7.**
-keep public class * extends android.support.annotation.**


#
-keepclasseswithmembernames class * {
    native <methods>;
}



-keepclassmembers class * extends android.app.Activity{
    public void *(android.view.View);
}

#
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

#
#
-keep public class * extends android.view.View{
    *** get*();
    void set*(***);
    public <init>(android.content.Context);
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>(android.content.Context, android.util.AttributeSet, int);
}
-keepclasseswithmembers class * {
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>(android.content.Context, android.util.AttributeSet, int);
}

#
#
-keep class * implements android.os.Parcelable {
  public static final android.os.Parcelable$Creator *;
}
#
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}
#
#-keep class **.R$* {
# *;
#}
#
-keepclassmembers class **.R$* {
    public static <fields>;
}

#
-keepclassmembers class * {
    void *(**On*Event);
    void *(**On*Listener);
}

#
-keep public class * extends android.view.View{
    *** get*();
    void set*(***);
    public <init>(android.content.Context);
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>(android.content.Context, android.util.AttributeSet, int);
}


-keepclassmembers class * extends android.webkit.WebViewClient {
    public void *(android.webkit.WebView, java.lang.String, android.graphics.Bitmap);
    public boolean *(android.webkit.WebView, java.lang.String);
}


# ----------------------------- other -----------------------------
#
#
-assumenosideeffects class android.util.Log {
    public static boolean isLoggable(java.lang.String, int);
    public static int v(...);
    public static int i(...);
    public static int w(...);
    public static int d(...);
    public static int e(...);
}

#
-dontnote junit.framework.**
-dontnote junit.runner.**
-dontwarn android.test.**
-dontwarn android.support.test.**
-dontwarn org.junit.**



-dontwarn com.alibaba.android.**
-keep class com.alibaba.android.**{*;}
-keep interface com.alibaba.android.**{*;}

-dontwarn com.github.JessYanCoding.**
-keep class com.github.JessYanCoding.**{*;}
-keep interface com.github.JessYanCoding.**{*;}


-dontwarn com.github.tbruyelle.**
-keep class com.github.tbruyelle.**{*;}
-keep interface com.github.tbruyelle.**{*;}

-keep public class com.chervon.moudleContainer.panel.modules.**{*;}

-keep public class com.alibaba.android.arouter.routes.**{*;}
-keep public class com.alibaba.android.arouter.facade.**{*;}
-keep class * implements com.alibaba.android.arouter.facade.template.ISyringe{*;}

# 如果使用了 byType 的方式获取 Service，需添加下面规则，保护接口
-keep interface * implements com.alibaba.android.arouter.facade.template.IProvider


-keep public class  com.chervon.moudleMessageCenter.service.MessageCenterService

-keepattributes *Annotation*
-keepclassmembers class * {
    @org.greenrobot.eventbus.Subscribe <methods>;
}
-keep enum org.greenrobot.eventbus.ThreadMode { *; }

# And if you use AsyncExecutor:
-keepclassmembers class * extends org.greenrobot.eventbus.util.ThrowableFailureEvent {
    <init>(java.lang.Throwable);
}


-keep public class com.alibaba.android.arouter.routes.**{*;}
-keep public class com.alibaba.android.arouter.facade.**{*;}
-keep class * implements com.alibaba.android.arouter.facade.template.ISyringe{*;}

# 如果使用了 byType 的方式获取 Service，需添加下面规则，保护接口
-keep interface * implements com.alibaba.android.arouter.facade.template.IProvider
-keep public class com.chervon.moudleOobe.data.model.**{*;}

-keep public class com.chervon.libBase.utils.**{*;}
-keep public class com.chervon.libBase.model.**{*;}
-keep class  com.chervon.libDB.entities.**{*;}
-keep class  com.chervon.moudleConfigNet.databinding.**{*;}



-keep public class  com.chervon.libNetwork.http.**{*;}
-keep public class com.chervon.moudleConfigNet.data.model.**{*;}
-keep public class com.chervon.moudleConfigNet.ui.state.**{*;}

-keep class  com.chervon.moudleConfigNet.databinding.**{*;}

-keep class com.sangfor.**{*;}
-keep class com.sangfor.bugreport.**{*;}

-keep class com.chervon.moudleContainer.panel.data.entity.**{*;}
-keep class  com.chervon.libBase.ui.widget.**{*;}


-keep class   com.chervon.moudleContainer.panel.modules.**{*;}

-keep class   com.chervon.module_deviceManage.**{*;}
-keep class   com.chervon.module_messageCenter.**{*;}
-keep class   com.chervon.module_oobe.**{*;}
-keep class   com.chervon.module_userCenter.**{*;}



-keep class com.chervon.libRouter.interceptor.**{*;}
-keep class com.chervon.libNetwork.**{*;}
-keep class com.chervon.libBluetooth.**{*;}
-keep class com.chervon.libOTA.**{*;}
-keep class com.alibaba.fastjson.**{*;}
-keep class  org.webkit.androidjsc.**{*;}
-keep class  com.facebook.react.**{*;}
-keep class com.chervon.libOTA.data.**{*;}
-keep class com.chervon.libOTA.event.**{*;}
-keep class com.chervon.libBase.ui.BaseEnhanceFragment{*;}


# autosize适配方案
-keep class me.jessyan.autosize.** { *; }
-keep interface me.jessyan.autosize.** { *; }

-keep class  com.horcrux.svg.**{*;}


-keep public class com.dylanvann.fastimage.* {*;}
-keep public class com.dylanvann.fastimage.** {*;}
-keep public class * implements com.bumptech.glide.module.GlideModule
-keep public class * extends com.bumptech.glide.module.AppGlideModule
-keep public enum com.bumptech.glide.load.ImageHeaderParser$** {
  **[] $VALUES;
  public *;
}
