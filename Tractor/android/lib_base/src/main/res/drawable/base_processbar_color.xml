<?xml version="1.0" encoding="UTF-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!--  设置背景色（黑色）  -->
    <item  android:id="@android:id/background" >
        <shape>
            <corners
                android:radius="2dip" />
            <gradient
                android:startColor="#E3E3E3"
                android:endColor="#E3E3E3" />
        </shape>
    </item>

    <!--  设置进度条颜色（白色）  -->
    <item  android:id="@android:id/progress" >
        <clip>
            <shape>
                <corners
                    android:radius="2dip" />
                <gradient
                    android:startColor="@color/colorButtonNormal"
                    android:endColor="@color/colorButtonNormal" />
            </shape>
        </clip>
    </item>
</layer-list>
