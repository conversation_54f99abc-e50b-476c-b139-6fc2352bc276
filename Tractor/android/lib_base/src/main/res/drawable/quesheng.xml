<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="262dp"
    android:height="304dp"
    android:viewportWidth="262"
    android:viewportHeight="304">
  <path
      android:pathData="M24.38,153.19l30.02,-7.13l0,81.94l-30.02,0z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="34.81"
          android:startY="215.7"
          android:endX="34.81"
          android:endY="165.01"
          android:type="linear">
        <item android:offset="0" android:color="#00F8F8F8"/>
        <item android:offset="1" android:color="#FFEDEDED"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M60,38l40.38,0l0,190l-40.38,0z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="74.04"
          android:startY="277.41"
          android:endX="74.04"
          android:endY="81.93"
          android:type="linear">
        <item android:offset="0" android:color="#00F8F8F8"/>
        <item android:offset="1" android:color="#FFEDEDED"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M130.06,79.56l47.5,8.31l0,131.81l-47.5,0z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="146.57"
          android:startY="256.12"
          android:endX="146.57"
          android:endY="111.96"
          android:type="linear">
        <item android:offset="0" android:color="#00F8F8F8"/>
        <item android:offset="1" android:color="#FFEDEDED"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M177.56,105.69l29.69,0l0,140.13l-29.69,0z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="187.88"
          android:startY="282.26"
          android:endX="187.88"
          android:endY="138.09"
          android:type="linear">
        <item android:offset="0" android:color="#00F8F8F8"/>
        <item android:offset="1" android:color="#FFEDEDED"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M106.31,36.81L117.92,43.34L117.92,51.66L131.25,51.66L131.25,182.28L106.31,182.28L106.31,36.81ZM126.5,73.63L111.06,73.63L111.06,76L126.5,76L126.5,73.63ZM126.5,66.5L111.06,66.5L111.06,68.88L126.5,68.88L126.5,66.5ZM126.5,59.38L111.06,59.38L111.06,61.75L126.5,61.75L126.5,59.38Z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="114.98"
          android:startY="220.11"
          android:endX="114.98"
          android:endY="70.44"
          android:type="linear">
        <item android:offset="0" android:color="#00F8F8F8"/>
        <item android:offset="1" android:color="#FFEDEDED"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M215.56,135.38l11.88,0l0,93.81l-11.88,0z"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="219.69"
          android:startY="215.1"
          android:endX="219.69"
          android:endY="157.07"
          android:type="linear">
        <item android:offset="0" android:color="#00F8F8F8"/>
        <item android:offset="1" android:color="#FFEDEDED"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M67.13,29.69h26.13v8.31h-26.13z"
      android:strokeWidth="1"
      android:fillColor="#EDEDED"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M79,10.69h2.38v19h-2.38z"
      android:strokeWidth="1"
      android:fillColor="#EDEDED"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M235.73,263.57C235.73,274.71 189.88,283.75 133.32,283.75C76.76,283.75 30.92,274.71 30.92,263.57C30.92,252.42 76.76,243.38 133.32,243.38C189.88,243.38 235.73,252.42 235.73,263.57"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:centerX="-1161.48"
          android:centerY="26.28"
          android:gradientRadius="20.18"
          android:type="radial">
        <item android:offset="0" android:color="#FFDADADA"/>
        <item android:offset="1" android:color="#00ECECEC"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M70.84,229.66l79.56,-16.13l0,-84.98l-79.56,16.13z"
      android:strokeWidth="1"
      android:fillColor="#CCCCCC"
      android:fillType="nonZero"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M115.99,245.79l-45.72,-16.13l0,-84.98l45.72,16.13z"
      android:strokeWidth="1"
      android:fillColor="#D6D6D6"
      android:fillType="nonZero"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M195.96,229.66l-45.72,-16.13l0,-84.98l45.72,16.13z"
      android:strokeWidth="1"
      android:fillColor="#C0C0C0"
      android:fillType="nonZero"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M115.8,245.79l80.16,-16.13l0,-84.98l-80.16,16.13z"
      android:strokeWidth="1"
      android:fillColor="#E1E1E1"
      android:fillType="nonZero"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M136,191.43l80.52,-16.36l-20,-30.39l-80.52,16.36z"
      android:strokeWidth="1"
      android:fillColor="#FAFAFA"
      android:fillType="nonZero"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M116,154.54C116,154.54 118.48,118.92 140.01,115.54C161.54,112.17 145.62,138.78 133.71,134.64C121.8,130.49 125.98,101.6 148.75,89.44"
      android:strokeWidth="1.57"
      android:fillColor="#00000000"
      android:strokeColor="#DDDDDD"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M161.59,71.82l-6.76,13.79l15.82,-7.8z"
      android:strokeWidth="1"
      android:fillColor="#C0C0C0"
      android:fillType="nonZero"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M138.99,56.86l47.01,31.96l25.71,-61.5z"
      android:strokeWidth="1"
      android:fillColor="#E4E4E4"
      android:fillType="nonZero"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M154.77,67.31l0.06,18.3l11.2,-10.85l45.68,-47.44z"
      android:strokeWidth="1"
      android:fillColor="#D2D2D2"
      android:fillType="nonZero"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M49.34,214.34m-8.91,0a8.91,8.91 0,1 1,17.81 0a8.91,8.91 0,1 1,-17.81 0"
      android:strokeWidth="1"
      android:fillColor="#E0E0E0"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M213.8,244.63m-7.13,0a7.13,7.13 0,1 1,14.25 0a7.13,7.13 0,1 1,-14.25 0"
      android:strokeWidth="1"
      android:fillColor="#E0E0E0"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M233.99,194.75m-3.56,0a3.56,3.56 0,1 1,7.13 0a3.56,3.56 0,1 1,-7.13 0"
      android:strokeWidth="1"
      android:fillColor="#E4E4E4"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M96,191.43l-45.73,-16.36l20,-30.39l45.73,16.36z"
      android:strokeWidth="1"
      android:fillColor="#E5E5E5"
      android:fillType="nonZero"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M144.74,210.91C145.43,210.78 146.19,210.67 146.42,211.66C146.65,212.59 146.27,213.46 145.72,214.23C145.1,215.1 144.32,215.45 143.49,215.61C141.15,216.06 138.81,216.54 136.47,217L129.45,218.4C128.79,218.53 128.1,218.68 127.8,217.82C127.49,216.91 127.88,216.03 128.38,215.24C129,214.28 129.83,213.88 130.7,213.71C135.38,212.76 140.06,211.83 144.74,210.91ZM177.73,190.34C180.59,189.73 183.46,189.13 186.32,188.64C189.22,188.15 190.52,189.98 189.83,193.65C189.25,196.7 188.36,199.7 187.23,202.6C186.25,205.09 184.69,206.67 182.65,207.5C180.68,208.31 178.7,208.48 177.24,208.83C175.24,209.23 173.75,209.58 172.26,209.82C169.67,210.22 168.44,208.65 168.98,205.42C169.54,202.1 170.46,198.84 171.82,195.75C173.15,192.72 175.15,190.9 177.73,190.34ZM167.71,192.3C168.29,192.18 168.86,192.15 169.17,192.82C169.5,193.53 169.24,194.29 168.91,195C168.26,196.35 167.27,196.86 166.17,197.08C163.3,197.65 160.43,198.22 157.57,198.8C154.76,199.37 154.05,200.13 153.07,203.56C152.75,204.71 152.39,205.86 152.12,207.02C151.81,208.39 152.18,209.19 153.25,209.04C156.03,208.67 158.82,208.18 161.6,207.34C162.58,207.04 163.41,206.18 163.98,204.53L157.86,205.75C157.24,205.86 156.63,205.9 156.38,205.07C156.08,204.1 156.51,203.18 157.08,202.37C157.63,201.6 158.32,201.19 159.07,201.03C161.62,200.51 164.16,199.98 166.71,199.52C168.15,199.25 168.56,199.98 168.11,201.81C168.1,201.86 168.09,201.91 168.07,201.96C166.12,209.51 164.39,211.4 158.33,212.61C156.15,213.04 153.97,213.51 151.79,213.9C148.92,214.42 147.66,212.83 148.32,209.22C148.9,206.01 149.79,202.85 151.12,199.84C152.47,196.8 154.49,195 157.05,194.44C160.6,193.67 164.15,193.01 167.71,192.3ZM184.67,193.45C182.13,193.89 179.59,194.39 177.05,194.99C175.65,195.32 174.63,196.44 174.12,198.25C173.74,199.65 173.31,201.04 172.94,202.44C172.43,204.37 172.88,205.12 174.45,204.89C175.62,204.71 176.79,204.43 177.96,204.2C179.21,203.95 180.46,203.75 181.71,203.43C183.21,203.06 184.3,201.95 184.84,200.02C185.23,198.62 185.66,197.23 186.02,195.83C186.49,194.05 186.06,193.22 184.67,193.45ZM142,204.43C142.65,204.31 143.35,204.21 143.6,205.1C143.84,205.95 143.55,206.8 143.06,207.56C142.48,208.47 141.73,208.95 140.89,209.12C137.63,209.77 134.37,210.42 131.1,211.07C130.52,211.18 129.92,211.22 129.66,210.51C129.39,209.76 129.57,208.95 130,208.18C130.62,207.07 131.47,206.53 132.45,206.33C134.06,206.01 135.67,205.69 137.28,205.37C138.85,205.06 140.42,204.73 142,204.43ZM134.2,198.97C136.46,198.54 138.71,198.08 140.97,197.63C143.27,197.17 145.57,196.71 147.86,196.26C148.48,196.13 149.14,196 149.42,196.79C149.72,197.66 149.46,198.56 148.93,199.38C148.31,200.34 147.5,200.79 146.63,200.96C142.04,201.89 137.44,202.81 132.85,203.71C132.24,203.83 131.58,203.9 131.33,203.08C131.09,202.3 131.29,201.49 131.72,200.74C132.36,199.65 133.22,199.16 134.2,198.97ZM191.1,189.31L191.1,189.53L190.65,189.61L190.65,191.08L190.45,191.12L190.45,189.65L190.01,189.74L190.01,189.52L191.1,189.31ZM191.49,189.23L191.96,190.48L192.42,189.04L192.66,189L192.66,190.68L192.46,190.72L192.46,189.52L192.45,189.52L192.05,190.8L191.87,190.83L191.46,189.71L191.46,189.72L191.46,190.92L191.26,190.96L191.26,189.28L191.49,189.23Z"
      android:strokeWidth="1"
      android:fillColor="#C7C7C7"
      android:fillType="evenOdd"
      android:strokeColor="#00000000"/>
</vector>
