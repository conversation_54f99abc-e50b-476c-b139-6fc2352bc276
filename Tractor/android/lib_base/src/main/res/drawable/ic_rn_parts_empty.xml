<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="300dp"
    android:height="300dp"
    android:viewportWidth="300"
    android:viewportHeight="300">
  <path
      android:pathData="M57,157.9L83.43,151.62V223.85H57L57,157.9Z"
      android:strokeAlpha="0.625279"
      android:fillType="evenOdd"
      android:fillAlpha="0.625279">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="74.36"
          android:startY="213"
          android:endX="74.36"
          android:endY="168.32"
          android:type="linear">
        <item android:offset="0" android:color="#02F8F8F8"/>
        <item android:offset="1" android:color="#FFEDEDED"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M88.36,56.35H123.91V223.85H88.36L88.36,56.35Z"
      android:strokeAlpha="0.625279"
      android:fillType="evenOdd"
      android:fillAlpha="0.625279">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="119"
          android:startY="267.41"
          android:endX="119"
          android:endY="95.08"
          android:type="linear">
        <item android:offset="0" android:color="#02F8F8F8"/>
        <item android:offset="1" android:color="#FFEDEDED"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M150.04,92.99L191.86,100.32V216.52H150.04L150.04,92.99Z"
      android:strokeAlpha="0.625279"
      android:fillType="evenOdd"
      android:fillAlpha="0.625279">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="186.09"
          android:startY="248.65"
          android:endX="186.09"
          android:endY="121.55"
          android:type="linear">
        <item android:offset="0" android:color="#02F8F8F8"/>
        <item android:offset="1" android:color="#FFEDEDED"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M191.86,116.02H218V239.55H191.86L191.86,116.02Z"
      android:strokeAlpha="0.625279"
      android:fillType="evenOdd"
      android:fillAlpha="0.625279">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="214.39"
          android:startY="271.68"
          android:endX="214.39"
          android:endY="144.58"
          android:type="linear">
        <item android:offset="0" android:color="#02F8F8F8"/>
        <item android:offset="1" android:color="#FFEDEDED"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M139.36,61.06L129.14,55.3V183.54H151.09V68.39H139.36V61.06ZM146.91,75.19H133.32V77.29H146.91V75.19ZM133.32,81.47H146.91V83.57H133.32V81.47ZM146.91,87.76H133.32V89.85H146.91V87.76Z"
      android:strokeAlpha="0.625279"
      android:fillType="evenOdd"
      android:fillAlpha="0.625279">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="148.06"
          android:startY="216.89"
          android:endX="148.06"
          android:endY="84.95"
          android:type="linear">
        <item android:offset="0" android:color="#02F8F8F8"/>
        <item android:offset="1" android:color="#FFEDEDED"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M225.32,142.19L235.77,142.19V224.9H225.32V142.19Z"
      android:strokeAlpha="0.625279"
      android:fillType="evenOdd"
      android:fillAlpha="0.625279">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="232.19"
          android:startY="212.48"
          android:endX="232.19"
          android:endY="161.31"
          android:type="linear">
        <item android:offset="0" android:color="#02F8F8F8"/>
        <item android:offset="1" android:color="#FFEDEDED"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M94.64,49.02h23v7.33h-23z"
      android:strokeAlpha="0.625279"
      android:fillColor="#EDEDED"
      android:fillAlpha="0.625279"/>
  <path
      android:pathData="M105.09,32.27h2.09v16.75h-2.09z"
      android:strokeAlpha="0.625279"
      android:fillColor="#EDEDED"
      android:fillAlpha="0.625279"/>
  <path
      android:pathData="M243.07,255.2C243.07,265.03 202.71,273 152.91,273C103.12,273 62.76,265.03 62.76,255.2C62.76,245.37 103.12,237.41 152.91,237.41C202.71,237.41 243.07,245.37 243.07,255.2Z"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:centerX="152.91"
          android:centerY="255.2"
          android:gradientRadius="17.8"
          android:type="radial">
        <item android:offset="0" android:color="#FFDADADA"/>
        <item android:offset="1" android:color="#02ECECEC"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M97.91,225.32L167.95,211.1V136.18L97.91,150.4V225.32Z"
      android:fillColor="#CCCCCC"/>
  <path
      android:pathData="M137.66,239.53L97.41,225.32V150.4L137.66,164.62V239.53Z"
      android:fillColor="#D6D6D6"/>
  <path
      android:pathData="M208.06,225.32L167.81,211.1V136.18L208.06,150.4V225.32Z"
      android:fillColor="#C0C0C0"/>
  <path
      android:pathData="M137.49,239.53L208.06,225.32V150.4L137.49,164.62V239.53Z"
      android:fillColor="#E1E1E1"/>
  <path
      android:pathData="M155.27,191.61L226.16,177.19L208.56,150.4L137.67,164.82L155.27,191.61Z"
      android:fillColor="#FAFAFA"/>
  <path
      android:pathData="M137.67,159.09C137.67,159.09 139.85,127.68 158.81,124.71C177.76,121.74 163.74,145.2 153.26,141.54C142.77,137.89 146.45,112.42 166.5,101.7"
      android:strokeWidth="1.38424"
      android:fillColor="#00000000"
      android:strokeColor="#DDDDDD"/>
  <path
      android:pathData="M177.8,86.16L171.85,98.32L185.78,91.44L177.8,86.16Z"
      android:fillColor="#C0C0C0"/>
  <path
      android:pathData="M157.91,72.97L199.3,101.14L221.93,46.93L157.91,72.97Z"
      android:fillColor="#E4E4E4"/>
  <path
      android:pathData="M171.8,82.19L171.85,98.32L181.71,88.75L221.93,46.93L171.8,82.19Z"
      android:fillColor="#D2D2D2"/>
  <path
      android:pathData="M71.13,211.81a7.84,7.85 0,1 0,15.68 0a7.84,7.85 0,1 0,-15.68 0z"
      android:fillColor="#E0E0E0"/>
  <path
      android:pathData="M217.5,238.51a6.27,6.28 0,1 0,12.55 0a6.27,6.28 0,1 0,-12.55 0z"
      android:fillColor="#E0E0E0"/>
  <path
      android:pathData="M238.41,194.54a3.14,3.14 0,1 0,6.27 0a3.14,3.14 0,1 0,-6.27 0z"
      android:fillColor="#E4E4E4"/>
  <path
      android:pathData="M120.06,191.61L79.8,177.19L97.41,150.4L137.67,164.82L120.06,191.61Z"
      android:fillColor="#E5E5E5"/>
  <path
      android:pathData="M162.96,208.79C163.57,208.67 164.24,208.57 164.45,209.45C164.65,210.26 164.31,211.03 163.83,211.71C163.28,212.48 162.6,212.79 161.86,212.93C159.8,213.32 157.74,213.74 155.68,214.15L149.51,215.39C148.93,215.5 148.31,215.63 148.05,214.88C147.78,214.08 148.12,213.3 148.57,212.6C149.11,211.76 149.84,211.4 150.61,211.25C154.73,210.42 158.85,209.6 162.96,208.79ZM192.01,190.65C194.53,190.11 197.05,189.58 199.57,189.15C202.12,188.72 203.27,190.34 202.66,193.57C202.15,196.26 201.37,198.9 200.37,201.46C199.51,203.65 198.14,205.04 196.34,205.78C194.61,206.49 192.87,206.64 191.58,206.95C189.82,207.3 188.51,207.61 187.19,207.82C184.91,208.18 183.83,206.79 184.31,203.94C184.8,201.02 185.61,198.15 186.81,195.42C187.98,192.75 189.74,191.14 192.01,190.65ZM183.19,192.38C183.7,192.27 184.2,192.24 184.47,192.83C184.76,193.46 184.54,194.13 184.24,194.75C183.68,195.95 182.8,196.4 181.83,196.59C179.31,197.09 176.78,197.59 174.26,198.1C171.79,198.61 171.16,199.28 170.3,202.31C170.02,203.32 169.7,204.33 169.47,205.35C169.19,206.56 169.52,207.26 170.46,207.14C172.91,206.81 175.37,206.38 177.81,205.64C178.67,205.37 179.4,204.62 179.9,203.16L174.51,204.23C173.97,204.33 173.44,204.36 173.22,203.64C172.95,202.78 173.33,201.97 173.84,201.26C174.31,200.58 174.93,200.21 175.59,200.08C177.83,199.62 180.07,199.15 182.3,198.74C183.57,198.51 183.94,199.15 183.54,200.76C183.53,200.8 183.52,200.85 183.51,200.89C181.79,207.55 180.27,209.22 174.93,210.28C173.01,210.66 171.09,211.07 169.17,211.42C166.65,211.88 165.54,210.47 166.12,207.29C166.63,204.46 167.41,201.68 168.59,199.03C169.77,196.34 171.55,194.76 173.8,194.27C176.93,193.58 180.06,193 183.19,192.38ZM198.12,193.4C195.89,193.77 193.65,194.22 191.41,194.74C190.18,195.04 189.28,196.03 188.84,197.62C188.49,198.85 188.12,200.08 187.79,201.32C187.34,203.02 187.74,203.68 189.12,203.47C190.15,203.32 191.18,203.07 192.21,202.87C193.31,202.64 194.41,202.47 195.51,202.19C196.84,201.86 197.8,200.88 198.27,199.18C198.61,197.95 198.99,196.73 199.31,195.49C199.72,193.92 199.35,193.19 198.12,193.4ZM160.55,203.07C161.13,202.96 161.74,202.88 161.96,203.66C162.18,204.41 161.92,205.16 161.49,205.83C160.98,206.63 160.32,207.05 159.58,207.2C156.71,207.78 153.83,208.35 150.96,208.92C150.45,209.02 149.92,209.05 149.69,208.43C149.45,207.77 149.61,207.05 149.99,206.38C150.54,205.4 151.29,204.92 152.14,204.75C153.56,204.46 154.98,204.18 156.4,203.9C157.78,203.62 159.17,203.34 160.55,203.07ZM153.69,198.26C155.67,197.87 157.66,197.47 159.65,197.07C161.67,196.67 163.7,196.26 165.72,195.86C166.26,195.76 166.84,195.64 167.08,196.34C167.35,197.11 167.12,197.89 166.65,198.62C166.11,199.46 165.4,199.86 164.63,200.01C160.59,200.83 156.54,201.64 152.5,202.43C151.96,202.54 151.38,202.6 151.16,201.88C150.95,201.2 151.13,200.48 151.51,199.82C152.07,198.85 152.83,198.42 153.69,198.26ZM203.78,189.74V189.93L203.39,190.01V191.3L203.21,191.33V190.04L202.82,190.12V189.93L203.78,189.74ZM204.13,189.67L204.54,190.77L204.95,189.51L205.16,189.46V190.95L204.98,190.98V189.92L204.97,189.92L204.61,191.05L204.46,191.09L204.1,190.1L204.1,190.1V191.16L203.92,191.19V189.71L204.13,189.67Z"
      android:fillColor="#C7C7C7"
      android:fillType="evenOdd"/>
</vector>
