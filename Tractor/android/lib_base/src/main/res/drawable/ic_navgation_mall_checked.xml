<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="100mm"
    android:height="100mm"
    android:viewportWidth="49"
    android:viewportHeight="49">
  <path
      android:pathData="M28.1164,5.893C30.8289,5.893 33.1041,7.8365 33.1775,10.3657L33.1786,10.5721L33.138,12.1937C35.222,12.5721 37.2982,13.0835 39.3606,13.7253L39.3606,13.7253L39.4869,13.7709C40.7708,14.3005 41.7663,15.3444 42.2512,16.7497C44.4593,24.7902 44.5004,31.043 42.2479,38.7906L42.2479,38.7906L42.212,38.8985C41.7455,40.1424 40.7927,41.1331 39.5692,41.6537L39.5692,41.6537L39.4295,41.7051C34.4507,43.2618 29.3892,44.0578 24.3286,44.0578C19.2681,44.0578 14.2076,43.2621 9.2288,41.7061L9.2288,41.7061L9.0893,41.6548C7.8617,41.1328 6.9064,40.1379 6.4413,38.889L6.4413,38.889L6.4066,38.7841C4.1566,31.0414 4.1973,24.7913 6.4315,16.6612C6.8869,15.3472 7.8851,14.2998 9.1728,13.7703L9.1728,13.7703L9.2975,13.7253C11.2818,13.1077 13.2786,12.6109 15.2829,12.2371L15.3261,10.5072C15.3724,7.9823 17.61,6.0026 20.2783,5.8974L20.5016,5.893L28.1164,5.893ZM28.1164,8.893L20.5016,8.893C19.3447,8.893 18.432,9.626 18.3339,10.4482L18.3254,10.5721L18.2956,11.7689C20.3033,11.5191 22.3159,11.3931 24.3286,11.3931C26.2696,11.3931 28.211,11.5104 30.1481,11.7429L30.1793,10.5072C30.1939,9.7104 29.3949,8.9672 28.2851,8.8982L28.1164,8.893Z"
      android:strokeWidth="1"
      android:fillType="nonZero"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startY="6.8736267"
          android:startX="10.735205"
          android:endY="35.362263"
          android:endX="44.5004"
          android:type="linear">
        <item android:offset="0" android:color="#FF77DB60"/>
        <item android:offset="1" android:color="#FF6AB708"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M24.3281,24.9754L24.3281,28.9754"
      android:strokeWidth="9"
      android:fillColor="#00000000"
      android:strokeColor="#FFFFFF"
      android:fillType="evenOdd"
      android:strokeLineCap="round"/>
</vector>
