<?xml version="1.0" encoding="UTF-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:id="@android:id/background">
        <shape>
            <stroke android:width="0.5dp" android:color="#E3E3E3" />
            <solid android:color="#FFFFFFFF" />
            <corners android:radius="2dp" />
        </shape>
    </item>


    <item android:id="@android:id/secondaryProgress">
        <scale android:scaleWidth="100%">
            <shape>
                <stroke android:width="0.5dp" android:color="#E3E3E3" />
                <corners android:radius="15dip" />

            </shape>
        </scale>
    </item>
    <item android:id="@android:id/progress">
        <scale android:scaleWidth="100%">
            <shape>
                <corners android:radius="2mm" />
                <gradient
                    android:angle="0"

                    android:centerY="0.75"
                    android:endColor="#E3E3E3"
                    android:startColor="#E3E3E3"
                    android:type="linear"/>
            </shape>
        </scale>
    </item>

</layer-list>
