<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>

        <import type="com.chervon.libBase.utils.mlang.LanguageStrings" />
    </data>
    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="300mm"
            android:layout_height="0mm"
            app:layout_constraintDimensionRatio="H,1:1"
            android:background="@drawable/shape_fillet_000000_20"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent">

            <ImageView
                android:id="@+id/imageView"
                android:layout_width="60mm"
                android:layout_height="0mm"
                app:layout_constraintDimensionRatio="H,1:1"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintVertical_bias="0.356"
                android:src="@drawable/ic_success" />

            <TextView
                android:id="@+id/toastContent"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:layout_marginHorizontal="30mm"
                android:layout_marginTop="28mm"
                android:text="@{LanguageStrings.app_setting_success_textview_text()}"
                android:textColor="@color/white"
                android:textSize="28mm"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/imageView"
                app:layout_constraintVertical_bias="0"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>