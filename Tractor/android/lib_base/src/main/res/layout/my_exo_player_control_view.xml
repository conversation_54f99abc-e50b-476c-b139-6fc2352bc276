<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto"
  android:id="@+id/myExoPlayer"
  android:layout_width="match_parent"
  android:layout_height="wrap_content"
  android:layout_gravity="bottom"
  android:layout_marginLeft="5mm"
  android:layout_marginRight="5mm"
  android:background="?attr/colorControlHighlight">

  <ImageButton
    android:id="@id/exo_prev"
    style="@style/ExoMediaButton.Previous"
    android:layout_width="wrap_content"
    android:layout_height="30mm"
    app:layout_constraintBottom_toTopOf="@id/exo_position"
    app:layout_constraintEnd_toStartOf="@+id/exo_rew"
    app:layout_constraintStart_toStartOf="parent" />

  <ImageButton
    android:id="@id/exo_rew"
    style="@style/ExoMediaButton.Rewind"
    android:layout_width="wrap_content"
    android:layout_height="30mm"
    app:layout_constraintBottom_toTopOf="@id/exo_position"
    app:layout_constraintEnd_toStartOf="@+id/exo_play"
    app:layout_constraintStart_toEndOf="@id/exo_prev" />

  <ImageButton
    android:id="@id/exo_play"
    style="@style/ExoMediaButton.Play"
    android:layout_width="wrap_content"
    android:layout_height="30mm"
    app:layout_constraintBottom_toTopOf="@id/exo_position"
    app:layout_constraintEnd_toStartOf="@+id/exo_pause"
    app:layout_constraintStart_toEndOf="@id/exo_rew" />

  <ImageButton
    android:id="@id/exo_pause"
    style="@style/ExoMediaButton.Pause"
    android:layout_width="wrap_content"
    android:layout_height="30mm"
    app:layout_constraintBottom_toTopOf="@id/exo_position"
    app:layout_constraintEnd_toStartOf="@+id/exo_ffwd"
    app:layout_constraintStart_toEndOf="@id/exo_play" />

  <ImageButton
    android:id="@id/exo_ffwd"
    style="@style/ExoMediaButton.FastForward"
    android:layout_width="wrap_content"
    android:layout_height="30mm"
    app:layout_constraintBottom_toTopOf="@id/exo_position"
    app:layout_constraintEnd_toStartOf="@+id/exo_next"
    app:layout_constraintStart_toEndOf="@id/exo_pause" />

  <ImageButton
    android:id="@id/exo_next"
    style="@style/ExoMediaButton.Next"
    android:layout_width="wrap_content"
    android:layout_height="30mm"
    app:layout_constraintBottom_toTopOf="@id/exo_position"
    app:layout_constraintEnd_toEndOf="@id/exo_fullscreen_button"
    app:layout_constraintStart_toEndOf="@+id/exo_ffwd" />

  <ImageButton
    style="@style/ExoMediaButton.Next"
    android:id="@+id/exo_fullscreen_button"
    android:layout_width="wrap_content"
    android:layout_height="40mm"
    android:scaleType="centerInside"
    android:src="@drawable/exo_icon_fullscreen_enter"
    app:layout_constraintTop_toTopOf="@id/exo_next"
    app:layout_constraintBottom_toBottomOf="@id/exo_next"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toEndOf="@id/exo_next" />


  <ImageButton
    style="@style/ExoMediaButton.Next"
    android:id="@+id/exo_fullscreen_ext"
    android:layout_width="wrap_content"
    android:layout_height="40mm"
    android:scaleType="centerInside"
    android:visibility="gone"
    android:src="@drawable/exo_icon_fullscreen_exit"
    app:layout_constraintTop_toTopOf="@id/exo_next"
    app:layout_constraintBottom_toBottomOf="@id/exo_next"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toEndOf="@id/exo_next" />
  <TextView
    android:id="@+id/exo_position"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    app:layout_constraintBottom_toBottomOf="parent"
    app:layout_constraintLeft_toLeftOf="parent" />

  <com.google.android.exoplayer2.ui.DefaultTimeBar
    android:id="@+id/exo_progress"
    android:layout_width="0dp"
    android:layout_height="20mm"
    android:paddingVertical="2mm"
    app:touch_target_height="10dp"
    app:bar_height="2dp"
    app:buffered_color="@color/white"
    app:played_color = "@color/colorButtonNormal"
    app:layout_constraintBottom_toBottomOf="@id/exo_position"
    app:layout_constraintTop_toTopOf="@id/exo_position"
    app:layout_constraintLeft_toRightOf="@id/exo_position"
    app:layout_constraintRight_toLeftOf="@+id/exo_duration"
    app:unplayed_color="@android:color/black" />

  <TextView
    android:id="@+id/exo_duration"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    app:layout_constraintBottom_toBottomOf="parent"
    app:layout_constraintRight_toRightOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>

