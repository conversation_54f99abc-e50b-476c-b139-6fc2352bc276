<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>


    </data>
    <androidx.constraintlayout.widget.ConstraintLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="540mm"
            android:layout_height="0mm"
            android:background="@drawable/shape_fillet_ffffff_20"
            app:layout_constraintDimensionRatio="H,1:0.66"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            >

            <View
                android:id="@+id/dialog_confirm_alert_line1"
                android:layout_width="0mm"
                android:layout_height="1mm"
                android:background="@color/colorFrameLine"
                app:layout_constraintVertical_bias="0.72"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                />

            <View
                android:id="@+id/dialog_confirm_alert_line2"
                android:layout_width="1mm"
                android:layout_height="0mm"
                android:background="@color/colorFrameLine"
                app:layout_constraintTop_toBottomOf="@id/dialog_confirm_alert_line1"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                />

            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:textSize="32mm"
                android:layout_marginTop="20mm"
                android:textStyle="bold"
                app:layout_constraintTop_toTopOf="parent"
                android:textColor="@color/colorNavItemNormal"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                />

            <!--            content-->
            <TextView
                android:id="@+id/tvContent"
                android:layout_width="0mm"
                android:layout_height="wrap_content"
                android:textSize="28mm"
                android:gravity="center"
                android:layout_marginStart="60mm"
                android:layout_marginEnd="60mm"
                android:textColor="@color/dialog_confirm_alert_content_color"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvTitle"
                />

            <!-- left btn-->
            <TextView
                android:id="@+id/tvCancel"
                android:layout_width="0mm"
                android:layout_height="0mm"
                android:gravity="center"
                android:textSize="32mm"
                android:textColor="@color/colorNavItemNormal"
                app:layout_constraintTop_toBottomOf="@id/dialog_confirm_alert_line1"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toStartOf="@id/dialog_confirm_alert_line2"
                app:layout_constraintBottom_toBottomOf="parent"
                />

            <!--            right btn-->
            <TextView
                android:id="@+id/tvConfirm"
                android:layout_width="0mm"
                android:layout_height="0mm"
                android:gravity="center"
                android:textSize="32mm"
                android:textColor="@color/device_upgrade_latest_version_color"
                app:layout_constraintTop_toBottomOf="@id/dialog_confirm_alert_line1"
                app:layout_constraintStart_toEndOf="@id/dialog_confirm_alert_line2"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                />



        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>
