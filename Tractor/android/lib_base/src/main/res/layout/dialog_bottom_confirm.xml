<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <variable
            name="bottomAlertDialog"
            type="com.chervon.libBase.ui.dialog.BottomConfirmDialog" />
    </data>
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

<!--        card-->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0mm"
            android:layout_height="wrap_content"
            android:paddingBottom="40mm"
            android:paddingTop="84mm"
            android:background="@drawable/shape_fillet_ffffff_left_right_20"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"

            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="1">

            <com.luck.picture.lib.widget.MediumBoldTextView
                android:id="@+id/dialog_bottom_alert_img"
                android:layout_width="wrap_content"
                android:layout_height="0mm"
                app:layout_constraintBottom_toBottomOf="parent"
                android:textSize="34mm"
                android:textColor="@color/colorTitle"
                android:text="@{bottomAlertDialog.title}"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_marginBottom="53mm"
                app:layout_constraintBottom_toTopOf="@id/dialog_bottom_alert_content"
           />

            <TextView
                android:id="@+id/dialog_bottom_alert_content"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="50mm"
                android:layout_marginEnd="50mm"
                android:gravity="center"
                android:text="@{bottomAlertDialog.content}"
                android:textSize="30mm"
                android:textColor="@color/colorText"
                app:layout_constrainedWidth="true"

                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                android:layout_marginBottom="8mm"
                app:layout_constraintBottom_toTopOf="@id/tvleftButton"
                />

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/dialog_bottom_alert_guide"
                android:layout_width="wrap_content"
                android:layout_height="0mm"
                android:orientation="vertical"
                app:layout_constraintGuide_percent="0.5"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvleftButton"
                android:layout_width="0mm"
                android:layout_height="0mm"
                app:layout_constraintDimensionRatio="H,1:0.287"
                android:layout_marginStart="30mm"
                android:layout_marginEnd="10mm"
                android:textColor="@color/color_dialog_bottom_alert_text"
                android:textSize="36mm"
                android:text="@{bottomAlertDialog.lText}"
                android:gravity="center"
                android:onClick="@{() -> bottomAlertDialog.lCLick()}"
                android:background="@drawable/shape_fillet_77bc1f_ffffff_4"
                app:layout_constraintVertical_bias="1"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="@id/dialog_bottom_alert_guide"
                app:layout_constraintStart_toStartOf="parent"
                />

            <TextView
                android:layout_width="0mm"
                android:layout_height="0mm"
                app:layout_constraintDimensionRatio="H,1:0.287"
                android:layout_marginStart="30mm"
                android:layout_marginEnd="10mm"
                android:textColor="@color/white"
                android:textSize="36mm"
                android:text="@{bottomAlertDialog.rText}"
                app:layout_constraintVertical_bias="1"
                android:gravity="center"
                android:onClick="@{() -> bottomAlertDialog.rCLick()}"
                android:background="@drawable/shape_fillet_77bc1f_4"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="@id/dialog_bottom_alert_guide"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/dialog_bottom_alert_content"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>