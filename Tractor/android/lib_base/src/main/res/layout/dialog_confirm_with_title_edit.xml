<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="confirmAlertDialog"
            type="com.chervon.libBase.ui.dialog.ConfirmAlertWithTitleDialog" />
        <import type="android.view.View" />
    </data>
    <androidx.constraintlayout.widget.ConstraintLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
      android:background="@color/colorTransparent"

        android:layout_width="540mm"
        android:layout_height="360mm">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="540mm"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_fillet_ffffff_20"

            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            >

            <View
                android:id="@+id/dialog_confirm_alert_line1"
                android:layout_width="0mm"
                android:layout_height="1mm"
                android:layout_marginBottom="101mm"
                android:background="@color/colorFrameLine"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                />

            <View
                android:id="@+id/dialog_confirm_alert_line2"
                android:layout_width="1mm"
                android:layout_height="0mm"
                android:background="@color/colorFrameLine"
                app:layout_constraintTop_toBottomOf="@id/dialog_confirm_alert_line1"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                />

<!--            left btn-->
            <TextView
              android:id="@+id/btnCancel"
                android:layout_width="0mm"
                android:layout_height="101mm"
                android:gravity="center"
                android:text="@{confirmAlertDialog.lText}"
                android:textSize="32mm"
                android:textColor="@color/colorNavItemNormal"
                android:onClick="@{() -> confirmAlertDialog.lCLick()}"
                app:layout_constraintTop_toBottomOf="@id/dialog_confirm_alert_line1"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toStartOf="@id/dialog_confirm_alert_line2"
                app:layout_constraintBottom_toBottomOf="parent"
                />

            <!--            right btn-->
            <TextView
                android:id="@+id/btnConfirm"
                android:layout_width="0mm"
                android:layout_height="101mm"
                android:gravity="center"
                android:text="@{confirmAlertDialog.rText}"
                android:textSize="32mm"
                android:textColor="@color/colorButtonNormal"
                android:onClick="@{() -> confirmAlertDialog.rCLick()}"
                app:layout_constraintTop_toBottomOf="@id/dialog_confirm_alert_line1"
                app:layout_constraintStart_toEndOf="@id/dialog_confirm_alert_line2"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                />

            <!--            title-->

            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="0mm"
                android:layout_height="wrap_content"
                android:text="@{confirmAlertDialog.title}"
                android:textColor="@color/black"
                android:layout_marginHorizontal="100mm"
                android:layout_marginTop="52mm"
                android:textSize="32mm"
                android:gravity="center"
                android:visibility="@{confirmAlertDialog.title!=null?View.VISIBLE:View.GONE}"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toTopOf="@id/et_dialog"

                />


            <!--            content-->
            <EditText
              android:id="@+id/et_dialog"
                style="@style/et_login"
                android:layout_width="0mm"
                android:layout_height="90mm"
              android:background="@drawable/base_et_dialog_shape_normal"
                android:text="@{confirmAlertDialog.content}"
                android:textSize="28mm"
              android:maxLength="20"
              android:layout_marginTop="32mm"
                android:layout_marginStart="30mm"
                android:layout_marginEnd="30mm"
                android:layout_marginBottom="68mm"
                android:textColor="@color/colorsecond"
                app:layout_constraintTop_toBottomOf="@id/tvTitle"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toTopOf="@id/dialog_confirm_alert_line1"
                />




        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>
