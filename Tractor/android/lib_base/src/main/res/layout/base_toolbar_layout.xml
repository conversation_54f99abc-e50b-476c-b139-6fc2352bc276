<?xml version="1.0" encoding="utf-8"?>
<layout>

  <data>

    <variable
      name="toolbar"
      type="com.chervon.libBase.model.ToolbarData" />

  </data>

  <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/constraint_toolbar"
    android:layout_width="match_parent"
    android:layout_height="@dimen/base_toolbar_height"
    android:background="@color/white">

    <!--返回-->
    <androidx.appcompat.widget.AppCompatImageView
      android:id="@+id/backImageBtn"
      android:layout_width="100mm"
      android:layout_height="40mm"
      android:onClick="@{(view) -> toolbar.click.onClick(view)}"
      android:paddingStart="@dimen/base_back_btn_margin_start"
      android:paddingEnd="@dimen/base_back_btn_margin_start"
      android:scaleType="fitCenter"
      android:src="@drawable/ic_btn_back"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintHorizontal_bias="0"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent" />

    <TextView
      android:id="@+id/tvBarTitle"
      android:layout_width="0mm"
      android:layout_height="wrap_content"
      android:layout_marginRight="100mm"
      android:ellipsize="end"
      android:gravity="center"
      android:singleLine="true"
      android:text="@{toolbar.title}"
      android:textColor="@color/colorTitle"
      android:textSize="36mm"
      android:textStyle="bold"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toEndOf="@id/backImageBtn"
      app:layout_constraintTop_toTopOf="parent" />

    <View
      android:id="@+id/toolbarDividie"
      android:layout_width="0mm"
      android:layout_height="1mm"
      android:background="@color/colorDivider"
      android:visibility="gone"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintTop_toTopOf="parent"
      app:layout_constraintVertical_bias="1" />

  </androidx.constraintlayout.widget.ConstraintLayout>

</layout>
