<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto"
  android:layout_width="wrap_content"
  android:layout_height="match_parent"
  android:layout_gravity="center_horizontal|bottom">

  <ProgressBar
    android:id="@+id/pb2"
    style="@android:style/Widget.ProgressBar"
    android:layout_width="50mm"
    android:layout_height="50mm"
    android:background="@null"
    android:indeterminateDrawable="@drawable/progress"
    app:layout_constraintBottom_toTopOf="@+id/tv_loading"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent" />


  <TextView
    android:id="@+id/tv_loading"
    android:layout_width="66dp"
    android:layout_height="17dp"
    android:gravity="center"
    android:textColor="@color/colorthird"
    android:textSize="24px"
    app:layout_constraintBottom_toBottomOf="parent"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
