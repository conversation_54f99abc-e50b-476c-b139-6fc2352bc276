<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <import type="com.chervon.libBase.utils.mlang.LanguageStrings" />
  </data>

  <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
      android:layout_width="540mm"
      android:layout_height="0mm"
      android:background="@drawable/shape_fillet_dialog_bg"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintDimensionRatio="H,1:0.66"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent">

      <ImageView
        android:id="@+id/img_evaluate_logo"
        android:layout_width="102mm"
        android:layout_height="102mm"
        android:layout_marginTop="50mm"
        android:src="@mipmap/icon_logo_round2_na"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

      <ImageView
        android:id="@+id/img_evaluate_dimiss"
        android:layout_width="40mm"
        android:layout_height="40mm"
        android:layout_marginTop="30mm"
        android:src="@drawable/icon_evaluate_dimiss"
        android:layout_marginRight="30mm"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

      <!--            content-->
      <TextView
        android:id="@+id/tvContent"
        android:layout_width="0mm"
        android:layout_height="wrap_content"
        android:layout_marginStart="60mm"
        android:layout_marginEnd="60mm"
        android:layout_marginTop="20mm"
        android:gravity="center"
        android:textColor="@color/dialog_confirm_alert_content_color"
        android:textSize="28mm"
        android:text="@{LanguageStrings.app_upgradedialog_content_textview_text()}"
        app:layout_constraintBottom_toTopOf="@id/dialog_confirm_alert_line1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginBottom="20mm"
        app:layout_constraintTop_toBottomOf="@+id/img_evaluate_logo" />

      <View
        android:id="@+id/dialog_confirm_alert_line1"
        android:layout_width="0mm"
        android:layout_height="1mm"
        android:background="@color/colorFrameLine"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.72" />

      <View
        android:id="@+id/dialog_confirm_alert_line2"
        android:layout_width="1mm"
        android:layout_height="0mm"
        android:background="@color/colorFrameLine"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/dialog_confirm_alert_line1" />

      <!--            left btn-->
      <TextView
        android:id="@+id/tvCancel"
        android:layout_width="0mm"
        android:layout_height="0mm"
        android:gravity="center"
        android:textColor="@color/colorNavItemNormal"
        android:textSize="32mm"
        android:text="@{LanguageStrings.app_upgradedialog_later_textview_text()}"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/dialog_confirm_alert_line2"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/dialog_confirm_alert_line1" />

      <!--            right btn-->
      <TextView
        android:id="@+id/tvConfirm"
        android:layout_width="0mm"
        android:layout_height="0mm"
        android:gravity="center"
        android:textSize="32mm"
        android:textColor="@color/device_upgrade_latest_version_color"
        android:text="@{LanguageStrings.app_upgradedialog_upgrade_textview_text()}"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/dialog_confirm_alert_line2"
        app:layout_constraintTop_toBottomOf="@id/dialog_confirm_alert_line1" />


    </androidx.constraintlayout.widget.ConstraintLayout>

  </androidx.constraintlayout.widget.ConstraintLayout>

</layout>
