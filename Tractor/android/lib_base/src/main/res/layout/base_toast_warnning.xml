<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="300mm"
    android:layout_height="300mm"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:alpha="0.8"
    android:background="@drawable/toast_shape">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivRight"
        android:layout_width="60mm"
        android:layout_height="60mm"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="77mm"
        android:src="@drawable/ic_toast_warnning"
        />


    <TextView
        android:id="@+id/tvContent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="#ffffffff"
        android:layout_marginHorizontal="17mm"
        android:gravity="center"
        android:textSize="30mm"
        android:layout_marginTop="28mm"
        app:layout_constraintTop_toBottomOf="@id/ivRight"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        />


</androidx.constraintlayout.widget.ConstraintLayout>
