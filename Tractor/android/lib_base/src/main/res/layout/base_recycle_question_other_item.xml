<?xml version="1.0" encoding="utf-8"?>

<layout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto"
  xmlns:tools="http://schemas.android.com/tools">

  <data>

    <variable
      name="showDelState"
      type="Boolean" />

    <variable
      name="uiState"
      type="com.chervon.libDB.entities.DeviceInfo" />

    <variable
      name="ActionUpdate"
      type="String"
      android:value="@string/action_update" />

    <variable
      name="ActionControl"
      type="String"
      android:value="@string/action_control" />

    <variable
      name="ActionDel"
      type="String"
      android:value="@string/action_del" />

    <variable
      name="ActionDeviceDetail"
      type="String"
      android:value="@string/action_device_detail" />

    <variable
      name="presenter"
      type="com.chervon.libBase.ui.ItemClick" />

    <import type="android.view.View" />

    <import type="com.chervon.libBase.utils.mlang.LanguageStrings" />

  </data>


  <androidx.constraintlayout.widget.ConstraintLayout
    android:id="@+id/clContainer"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.appcompat.widget.LinearLayoutCompat
      android:id="@+id/llother"
      android:layout_width="0mm"
      android:layout_height="96mm"
      android:orientation="horizontal"
      app:layout_constraintLeft_toLeftOf="parent"
      app:layout_constraintRight_toRightOf="parent"
      app:layout_constraintTop_toTopOf="parent">

      <CheckBox
        android:id="@+id/ivItemSelected"
        android:layout_width="wrap_content"
        android:layout_height="34mm"
        android:layout_gravity="center_vertical"
        android:clickable="false"
        android:focusable="false"
        android:focusableInTouchMode="false"
        android:theme="@style/MyCheckBox" />

      <androidx.appcompat.widget.AppCompatCheckBox
        android:id="@+id/radioItemSelected"
        android:layout_width="34mm"
        android:layout_height="34mm"
        android:layout_gravity="center_vertical"
        android:background="@drawable/radio_selector"
        android:button="@null"
        android:checked="false"
        android:clickable="false"
        android:focusable="false"
        android:focusableInTouchMode="false"
        android:visibility="gone" />

      <TextView
        android:id="@+id/tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginLeft="22mm"
        android:layout_weight="1"
        android:gravity="center_vertical"
        android:text="@{LanguageStrings.app_base_pictureselect_other_text()}"
        android:textColor="@color/colorTitle"
        android:textColorHint="@color/colorDisabled"
        android:textSize="32mm" />


    </androidx.appcompat.widget.LinearLayoutCompat>

    <EditText
      android:id="@+id/etOther"
      style="@style/et_login"
      android:layout_width="0mm"
      android:layout_marginTop="15mm"
      android:background="@drawable/module_login_et_shape"
      android:text="@{LanguageStrings.app_base_pictureselect_other_text()}"
      android:textColorHint="@color/colorDisabled"
      android:visibility="gone"
      app:layout_constraintLeft_toLeftOf="parent"
      app:layout_constraintRight_toRightOf="parent"
      app:layout_constraintTop_toBottomOf="@+id/llother" />


  </androidx.constraintlayout.widget.ConstraintLayout>


</layout>
