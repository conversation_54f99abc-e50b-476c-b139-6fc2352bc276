<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">


    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="540mm"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_fillet_ffffff_20"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">


            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="50mm"
                android:gravity="center"
                android:paddingLeft="40mm"
                android:paddingRight="40mm"
                android:textColor="@color/colorNavItemNormal"
                android:textSize="32mm"
                android:textStyle="bold"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <!--            content-->
            <TextView
                android:id="@+id/tvContent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8mm"
                android:gravity="center"
                android:paddingLeft="40mm"
                android:paddingRight="40mm"
                android:textColor="@color/colorsecond"
                android:textSize="28mm"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvTitle" />


            <LinearLayout
                android:layout_width="match_parent"
                android:orientation="vertical"
                app:layout_constraintTop_toBottomOf="@+id/tvContent"
                app:layout_constraintBottom_toBottomOf="parent"
                android:layout_marginTop="10dp"
                android:layout_height="wrap_content">
                <View
                    android:id="@+id/dialog_confirm_hoz"
                    android:layout_width="match_parent"
                    android:background="@color/colorFrameLine"
                    android:layout_height="1px" />
                <LinearLayout
                    android:layout_width="match_parent"
                    android:orientation="horizontal"
                    android:layout_height="wrap_content">
                    <!-- left btn-->
                    <TextView
                        android:id="@+id/tvCancel"
                        android:layout_width="0dp"
                        android:layout_weight="2"
                        android:layout_height="wrap_content"
                        android:padding="10dp"
                        android:gravity="center"
                        android:textColor="@color/colorNavItemNormal"
                        android:textSize="32mm" />





                    <View
                        android:id="@+id/dialog_confirm_alert_ver"
                        android:layout_width="1px"
                        android:layout_height="match_parent"
                        android:background="@color/colorFrameLine"/>

                    <!-- right btn-->
                    <TextView
                        android:id="@+id/tvConfirm"
                        android:layout_width="0dp"
                        android:layout_weight="2"
                        android:padding="10dp"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:textColor="@color/colorInlineText"
                        android:textSize="32mm" />
                </LinearLayout>

            </LinearLayout>




        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>
