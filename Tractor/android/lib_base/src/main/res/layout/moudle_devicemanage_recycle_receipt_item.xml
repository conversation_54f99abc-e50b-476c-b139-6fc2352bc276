<?xml version="1.0" encoding="utf-8"?>

<layout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto"
  xmlns:tools="http://schemas.android.com/tools">

  <data>

    <variable
      name="showDelState"
      type="Boolean" />

    <variable
      name="uiState"
      type="com.chervon.libDB.entities.DeviceInfo" />

    <variable
      name="ActionUpdate"
      type="String"
      android:value="@string/action_update" />

    <variable
      name="ActionControl"
      type="String"
      android:value="@string/action_control" />

    <variable
      name="ActionDel"
      type="String"
      android:value="@string/action_del" />

    <variable
      name="ActionDeviceDetail"
      type="String"
      android:value="@string/action_device_detail" />

    <variable
      name="presenter"
      type="com.chervon.libBase.ui.ItemClick" />

    <import type="android.view.View" />
  </data>


  <androidx.constraintlayout.widget.ConstraintLayout
    android:id="@+id/clDeviceCard"
    android:layout_width="160mm"
    android:layout_height="160mm"

    >

    <androidx.constraintlayout.widget.ConstraintLayout
      android:id="@+id/clContainer"
      android:layout_width="match_parent"
      android:layout_height="match_parent"
      android:layout_margin="12mm"
      android:background="@drawable/base_iv_receipt_shape"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintTop_toTopOf="parent">


      <ImageView
        android:id="@+id/ivDeviceIcon"
        android:layout_width="match_parent"
        android:layout_height="0mm"
        android:clickable="true"
        android:scaleType="centerCrop"
        android:src="@drawable/ic_device_default"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    </androidx.constraintlayout.widget.ConstraintLayout>

    <ImageButton
      android:id="@+id/ibDeviceDel"
      android:layout_width="44mm"
      android:layout_height="44mm"
      android:background="@null"
      android:clickable="true"
      android:onClick="@{() -> presenter.onItemClick(ActionDel,uiState)}"
      android:scaleType="fitCenter"
      android:src="@drawable/ic_receipt_del"

      app:layout_constraintRight_toRightOf="parent"
      app:layout_constraintTop_toTopOf="parent" />
  </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
