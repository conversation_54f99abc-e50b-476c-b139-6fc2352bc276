<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto"
  xmlns:tools="http://schemas.android.com/tools"
  tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">


  <androidx.appcompat.widget.LinearLayoutCompat
    android:id="@+id/linear_edit"
    android:layout_width="match_parent"
    android:layout_height="96mm"
    android:background="@drawable/module_login_et_shape"
    android:orientation="horizontal">

    <EditText
      style="@style/et_login"
      android:layout_gravity="center_vertical"
      android:layout_weight="1"
      android:letterSpacing = "0"
      android:gravity="center_vertical"
      android:imeOptions="flagNoExtractUi"
      android:textColorHint="@color/colorDisabled" />

    <ImageButton
      android:id="@+id/ibPassword"
      android:layout_width="40mm"
      android:layout_height="40mm"
      android:layout_gravity="center"
      android:layout_marginRight="20mm"
      android:background="@null"
      android:scaleType="fitCenter"
      android:src="@drawable/ic_password_hide"
      android:visibility="gone" />


  </androidx.appcompat.widget.LinearLayoutCompat>


  <androidx.constraintlayout.widget.ConstraintLayout
    android:id = "@+id/consCharAndSpaces"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="20mm"
    android:paddingRight="22mm"
    android:visibility="gone"
    app:layout_constraintTop_toBottomOf="@+id/linear_edit">

    <ImageView
      android:id="@+id/imgResultCharAndSpaces"
      android:layout_width="30mm"
      android:layout_height="30mm"
      android:layout_marginTop="2mm"
      android:src = "@drawable/ic_oobe_register_pwd_text_holder"
      app:layout_constraintLeft_toLeftOf="parent"
      app:layout_constraintTop_toTopOf="parent"
      />

    <TextView
      android:id="@+id/tvWarningTipCharAndSpaces"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:layout_gravity="left"
      app:layout_constraintTop_toTopOf="parent"
      app:layout_constraintBottom_toBottomOf="parent"
      android:textSize="24mm"
      android:textColor = "@color/color_oobe_sign_up_right"
      app:layout_constraintLeft_toRightOf="@+id/imgResultCharAndSpaces"
      />
  </androidx.constraintlayout.widget.ConstraintLayout>


  <androidx.constraintlayout.widget.ConstraintLayout
    android:id = "@+id/consNumberAndLetters"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="8mm"
    android:paddingRight="22mm"
    android:visibility="gone"
    app:layout_constraintTop_toBottomOf="@+id/consCharAndSpaces">

    <ImageView
      android:id="@+id/imgResultNumberAndLetters"
      android:layout_width="30mm"
      android:layout_height="30mm"
      android:layout_marginTop="2mm"
      android:src = "@drawable/ic_oobe_register_pwd_text_holder"
      app:layout_constraintLeft_toLeftOf="parent"
      app:layout_constraintTop_toTopOf="parent"
      />

    <TextView
      android:id="@+id/tvWarningTipNumberAndLetters"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:layout_gravity="left"
      app:layout_constraintTop_toTopOf="parent"
      app:layout_constraintBottom_toBottomOf="parent"
      android:textSize="24mm"
      android:textColor = "@color/color_oobe_sign_up_right"
      app:layout_constraintLeft_toRightOf="@+id/imgResultNumberAndLetters"
      />

  </androidx.constraintlayout.widget.ConstraintLayout>

  <androidx.constraintlayout.widget.ConstraintLayout
    android:id = "@+id/consMailFormat"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="8mm"
    android:paddingRight="22mm"
    android:visibility="gone"
    app:layout_constraintTop_toBottomOf="@+id/consNumberAndLetters">

    <ImageView
      android:id="@+id/imgResultMailFormat"
      android:layout_width="30mm"
      android:layout_height="30mm"
      android:layout_marginTop="2mm"
      android:src = "@drawable/ic_oobe_register_pwd_text_holder"
      app:layout_constraintLeft_toLeftOf="parent"
      app:layout_constraintTop_toTopOf="parent"
      />

    <TextView
      android:id="@+id/tvWarningTipMailFormat"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:layout_gravity="left"
      app:layout_constraintTop_toTopOf="parent"
      app:layout_constraintBottom_toBottomOf="parent"
      android:textSize="24mm"
      android:textColor = "@color/color_oobe_sign_up_right"
      app:layout_constraintLeft_toRightOf="@+id/imgResultMailFormat"
      />

  </androidx.constraintlayout.widget.ConstraintLayout>

  <TextView
    android:id="@+id/tvWarningTip"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:textColor="@color/colorInlineText"
    android:background="@drawable/base_et_inline_shape"
    android:paddingLeft="28mm"
    android:paddingRight="22mm"
    android:textSize="24mm"
    android:paddingTop="16mm"
    android:paddingBottom="20mm"
    app:layout_constraintTop_toBottomOf="@id/linear_edit"
    android:layout_marginTop="9mm"
    android:visibility="gone" />


</merge>
