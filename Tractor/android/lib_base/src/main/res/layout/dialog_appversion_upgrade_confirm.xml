<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="com.chervon.libBase.utils.mlang.LanguageStrings" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="540mm"
            android:layout_height="0mm"
            android:background="@drawable/shape_fillet_dialog_bg"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="H,1:0.75"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/img_logo"
                android:layout_width="102mm"
                android:layout_height="102mm"
                android:layout_marginTop="50mm"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/img_appversion_dismiss"
                android:layout_width="40mm"
                android:layout_height="40mm"
                android:visibility="gone"
                android:layout_marginTop="30mm"
                android:layout_marginRight="30mm"
                android:src="@drawable/icon_evaluate_dimiss"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <!-- tvVersionName-->
            <TextView
                android:id="@+id/tvVersionName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10mm"

                android:layout_marginBottom="10mm"
                android:gravity="center"
                android:textColor="@color/dialog_confirm_alert_content_color"
                android:textSize="28mm"
                app:layout_constraintTop_toBottomOf="@+id/img_logo"
                />

            <!--            content-->
            <TextView
                android:id="@+id/tvContent"
                android:layout_width="0mm"
                android:layout_height="wrap_content"
                android:layout_marginStart="30mm"
                android:layout_marginTop="10mm"
                android:layout_marginEnd="30mm"
                android:layout_marginBottom="10mm"
                android:gravity="center"
                android:text="@{LanguageStrings.app_appversiondialog_content_textview_text()}"
                android:textColor="@color/dialog_confirm_alert_content_color"
                android:textSize="28mm"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvVersionName" />

            <View
                android:id="@+id/dialog_confirm_alert_line1"
                android:layout_width="match_parent"
                android:layout_marginTop="30mm"
                android:layout_height="1mm"
                android:background="@color/colorFrameLine"
                app:layout_constraintTop_toBottomOf="@+id/tvContent" />

            <View
                android:id="@+id/dialog_confirm_alert_line2"
                android:layout_width="1mm"
                android:layout_height="0mm"
                android:background="@color/colorFrameLine"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/dialog_confirm_alert_line1" />

            <!--            left btn-->
            <TextView
                android:id="@+id/tvCancel"
                android:layout_width="0mm"
                android:layout_height="0mm"
                android:gravity="center"
                android:text="@{LanguageStrings.app_appversiondialog_later_textview_text()}"
                android:textColor="@color/colorNavItemNormal"
                android:textSize="32mm"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/dialog_confirm_alert_line2"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/dialog_confirm_alert_line1" />

            <!--            right btn-->
            <TextView
                android:id="@+id/tvConfirm"
                android:layout_width="0mm"
                android:layout_height="0mm"
                android:gravity="center"
                android:text="@{LanguageStrings.app_appversiondialog_upgrade_textview_text()}"
                android:textColor="@color/device_upgrade_latest_version_color"
                android:textSize="32mm"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/dialog_confirm_alert_line2"
                app:layout_constraintTop_toBottomOf="@id/dialog_confirm_alert_line1" />


        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>
