<?xml version="1.0" encoding="utf-8"?>

    <androidx.appcompat.widget.LinearLayoutCompat
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
        android:id="@+id/llPremiumProduct"
        android:layout_width="match_parent"
        android:layout_height="96mm"
        android:background="@color/white"
        android:orientation="horizontal"
        app:layout_constraintLeft_toRightOf="parent"
        app:layout_constraintRight_toLeftOf="parent"
        >
        <CheckBox
            android:id="@+id/ivItemSelected"
            android:layout_width="wrap_content"
            android:layout_height="34mm"
            android:clickable="false"
            android:focusableInTouchMode="false"
            android:focusable="false"
            android:theme="@style/MyCheckBox"
            android:layout_gravity="center_vertical"

            />

  <androidx.appcompat.widget.AppCompatCheckBox
    android:id="@+id/radioItemSelected"
    android:layout_width="34mm"
    android:layout_height="34mm"
    android:layout_gravity="center_vertical"
    android:background="@drawable/radio_selector"
    android:button="@null"
    android:checked="false"
    android:focusable="false"
    android:clickable="false"
    android:focusableInTouchMode="false"
    android:visibility="gone"
    />

        <TextView
            android:id="@+id/tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="22mm"
            android:textSize="32mm"
            android:layout_weight="1"
            android:text="Premium product design"
            android:textColor="@color/colorTitle"
            android:gravity="center_vertical"
            android:layout_gravity="center"
            />


    </androidx.appcompat.widget.LinearLayoutCompat>
