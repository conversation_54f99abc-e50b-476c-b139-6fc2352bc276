<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <variable
      name="bootomSelectListDialog"
      type="com.chervon.libBase.ui.dialog.BottomSelectListDialog" />

    <import type="com.chervon.libBase.utils.mlang.LanguageStrings" />
  </data>

  <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_fillet_ffffff_left_right_20"
    android:paddingBottom="80mm">



    <ImageView
      android:id="@+id/dialog_bottom_select_list_close"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:layout_marginStart="40mm"
      android:onClick="@{() -> bootomSelectListDialog.dismiss()}"
      android:src="@drawable/ic_close"
      app:layout_constraintLeft_toLeftOf="parent"
      app:layout_constraintRight_toLeftOf="@+id/dialog_bottom_select_list_title"
      app:layout_constraintTop_toTopOf="@id/dialog_bottom_select_list_title"
      app:layout_constraintBottom_toBottomOf="@+id/dialog_bottom_select_list_title"/>

    <TextView
      android:id="@+id/dialog_bottom_select_list_title"
      android:layout_width="0mm"
      android:layout_height="wrap_content"
      android:layout_marginTop="42mm"
      android:gravity="center"
      android:text="@{bootomSelectListDialog.titleData}"
      android:textColor="@color/colorText"
      android:textSize="34mm"
      android:textStyle="bold"
      android:layout_marginLeft="22mm"
      android:layout_marginRight="22mm"
      app:layout_constraintLeft_toRightOf="@+id/dialog_bottom_select_list_close"
      app:layout_constraintRight_toLeftOf="@+id/dialog_bottom_select_list_save"
      app:layout_constraintTop_toTopOf="parent"
      app:layout_constraintBottom_toTopOf="@+id/dialog_bottom_select_list_recyclerview"
       />


    <TextView
      android:id="@+id/dialog_bottom_select_list_save"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:layout_marginEnd="40mm"
      android:text="@{LanguageStrings.app_base_datapickerok_button_text()}"
      android:textColor="@color/colorButtonNormal"
      android:textSize="30mm"
      app:layout_constraintTop_toTopOf="@+id/dialog_bottom_select_list_title"
      app:layout_constraintBottom_toBottomOf="@+id/dialog_bottom_select_list_title"
      app:layout_constraintLeft_toRightOf="@+id/dialog_bottom_select_list_title"
      app:layout_constraintRight_toRightOf="parent"/>

    <androidx.recyclerview.widget.RecyclerView
      android:id="@+id/dialog_bottom_select_list_recyclerview"
      android:layout_width="0mm"
      android:layout_height="wrap_content"
      android:layout_marginStart="40mm"
      android:layout_marginTop="40mm"
      android:layout_marginEnd="40mm"
      android:overScrollMode="never"
      android:scrollbars="none"
      app:layout_constrainedHeight="true"
      app:layout_constrainedWidth="true"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintHeight_max="450mm"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toBottomOf="@id/dialog_bottom_select_list_title" />

  </androidx.constraintlayout.widget.ConstraintLayout>

</layout>
