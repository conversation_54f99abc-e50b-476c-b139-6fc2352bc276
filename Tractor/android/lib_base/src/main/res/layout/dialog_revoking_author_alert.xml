<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="confirmAlertDialog"
            type="com.chervon.libBase.ui.dialog.ConfirmAlertWithTitleDialog" />

        <import type="android.view.View" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="540mm"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_fillet_ffffff_20"

            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <View
                android:id="@+id/dialog_confirm_alert_line1"
                android:layout_width="0mm"
                android:layout_height="1mm"
                android:layout_marginBottom="101mm"
                android:background="@color/colorFrameLine"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent" />

            <View
                android:id="@+id/dialog_confirm_alert_line2"
                android:layout_width="1mm"
                android:layout_height="0mm"
                android:background="@color/colorFrameLine"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/dialog_confirm_alert_line1" />

            <!--            left btn-->
            <TextView
                android:layout_width="0mm"
                android:layout_height="101mm"
                android:gravity="center"
                android:onClick="@{() -> confirmAlertDialog.lCLick()}"
                android:text="@{confirmAlertDialog.lText}"
                android:textColor="@color/colorNavItemNormal"
                android:textSize="32mm"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/dialog_confirm_alert_line2"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/dialog_confirm_alert_line1" />

            <!--            right btn-->
            <TextView
                android:id="@+id/tvRight"
                android:layout_width="0mm"
                android:layout_height="101mm"
                android:gravity="center"
                android:onClick="@{() -> confirmAlertDialog.rCLick()}"
                android:text="@{confirmAlertDialog.rText}"
                android:textColor="@color/colorButtonNormal"
                android:textSize="32mm"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/dialog_confirm_alert_line2"
                app:layout_constraintTop_toBottomOf="@id/dialog_confirm_alert_line1" />

            <!--            title-->

            <TextView
                android:id="@+id/title"
                android:layout_width="0mm"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="100mm"
                android:layout_marginTop="52mm"
                android:gravity="center"
                android:text="@{confirmAlertDialog.title}"
                android:textColor="@color/black"
                android:textSize="34mm"
                android:visibility="@{confirmAlertDialog.title!=null?View.VISIBLE:View.GONE}"
                app:layout_constraintBottom_toTopOf="@id/content"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"

                />


            <!--            content-->
            <TextView
                android:id="@+id/content"
                android:layout_width="0mm"
                android:layout_height="wrap_content"
                android:layout_marginStart="30mm"
                android:layout_marginTop="30mm"
                android:layout_marginEnd="30mm"
                android:layout_marginBottom="68mm"
                android:text="@{confirmAlertDialog.content}"
                android:textColor="@color/colorTitle"
                android:textSize="30mm"
                app:layout_constraintBottom_toTopOf="@id/dialog_confirm_alert_line1"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/title" />


        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>