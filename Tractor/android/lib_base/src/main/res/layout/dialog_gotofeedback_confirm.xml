<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">


    <data>

        <import type="com.chervon.libBase.utils.mlang.LanguageStrings" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="540mm"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_fillet_ffffff_20"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">


            <!--content-->
            <TextView
                android:id="@+id/tvContent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_horizontal"
                android:padding="40mm"
                android:text="@{LanguageStrings.app_intentFeedbackAlertContent_textview_text()}"
                android:textColor="@color/black"
                android:textSize="28mm"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvContent">

                <View
                    android:id="@+id/dialog_confirm_hoz"
                    android:layout_width="match_parent"
                    android:layout_height="1px"
                    android:background="@color/colorFrameLine" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">
                    <!-- left btn-->
                    <TextView
                        android:id="@+id/tvCancel"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="2"
                        android:gravity="center"
                        android:paddingLeft="10mm"
                        android:paddingRight="10mm"
                        android:paddingTop="30mm"
                        android:paddingBottom="30mm"
                        android:text="@{LanguageStrings.app_intentFeedbackAlertCancel_textview_text()}"
                        android:textColor="@color/colorNavItemNormal"
                        android:textSize="32mm" />

                    <View
                        android:id="@+id/dialog_confirm_alert_ver"
                        android:layout_width="1px"
                        android:layout_height="match_parent"
                        android:background="@color/colorFrameLine" />

                    <!-- right btn-->
                    <TextView
                        android:id="@+id/tvConfirm"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="2"
                        android:gravity="center"
                        android:paddingLeft="10mm"
                        android:paddingRight="10mm"
                        android:paddingTop="30mm"
                        android:paddingBottom="30mm"
                        android:text="@{LanguageStrings.app_intentFeedbackAlertContinue_textview_text()}"
                        android:textColor="@color/color_dialog_bottom_alert_text"
                        android:textSize="32mm" />
                </LinearLayout>

            </LinearLayout>


        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>
