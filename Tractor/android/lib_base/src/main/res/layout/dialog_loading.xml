<?xml version="1.0" encoding="utf-8"?>
<layout>


    <data>
        <variable
            name="loadingdialog"
            type="com.chervon.libBase.ui.dialog.LoadingDialog" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="300mm"
            android:layout_height="0mm"
            app:layout_constraintDimensionRatio="H,1:1"
            android:background="@drawable/shape_fillet_000000_20"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent">

            <ImageView
                android:id="@+id/dialog_loading_img"
                android:layout_width="60mm"
                android:layout_height="0mm"
                app:layout_constraintDimensionRatio="H,1:1"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintVertical_bias="0.356" />

            <TextView
                android:id="@+id/dialog_loading_content"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:maxWidth="230mm"
                android:layout_marginHorizontal="30mm"
                android:layout_marginTop="28mm"
                android:text="@{loadingdialog.content}"
                android:textColor="@color/white"
                android:textSize="28mm"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/dialog_loading_img"
                app:layout_constraintVertical_bias="0"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>