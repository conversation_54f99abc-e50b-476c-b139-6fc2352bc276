<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="dialogBottomSelecetListData"
            type="com.chervon.libBase.model.BottomSelectListData" />
    </data>
    <androidx.constraintlayout.widget.ConstraintLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="100mm">

<!--        item text-->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/colorText"
            android:textSize="30mm"
            android:text="@{dialogBottomSelecetListData.dataString}"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHorizontal_bias="0"
            />

        <!--        item chcekbox-->
        <CheckBox
            android:id="@+id/dialog_bottom_select_list_item_checkbox"
            android:layout_width="42mm"
            android:layout_height="42mm"
            app:layout_constraintDimensionRatio="H,1:1"
            android:checked="@={dialogBottomSelecetListData.checked}"
            android:background="@drawable/checkbox_selector"
            android:button="@null"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            />

        <View
            android:layout_width="0mm"
            android:layout_height="1mm"
            android:background="@color/colorDivider"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintVertical_bias="1"
            />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>