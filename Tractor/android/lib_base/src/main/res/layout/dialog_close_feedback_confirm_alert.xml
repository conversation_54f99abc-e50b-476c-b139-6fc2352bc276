<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="confirmAlertDialog"
            type="com.chervon.libBase.ui.dialog.CloseFeedBackConfirmAlertDialog" />

        <import type="com.chervon.libBase.utils.mlang.LanguageStrings" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="540mm"
            android:layout_height="0mm"
            android:background="@drawable/shape_fillet_ffffff_20"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="H,1:0.66"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <View
                android:id="@+id/dialog_confirm_alert_line1"
                android:layout_width="0mm"
                android:layout_height="1mm"
                android:background="@color/colorFrameLine"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.72" />

            <View
                android:id="@+id/dialog_confirm_alert_line2"
                android:layout_width="1mm"
                android:layout_height="0mm"
                android:background="@color/colorFrameLine"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/dialog_confirm_alert_line1" />


            <!-- title-->
            <TextView
                android:id="@+id/title"
                android:layout_width="0mm"
                android:layout_height="wrap_content"
                android:text="@{LanguageStrings.app_closefeedbackdialog_title_textview_text()}"
                android:textColor="@color/black"
                android:layout_marginHorizontal="100mm"
                android:layout_marginTop="42mm"
                android:textSize="32mm"
                android:gravity="center"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent" />

            <!--content-->
            <TextView
                android:layout_width="0mm"
                android:layout_height="wrap_content"
                android:layout_marginStart="60mm"
                android:layout_marginEnd="60mm"
                android:gravity="center"
                android:text="@{LanguageStrings.app_closefeedbackdialog_content_textview_text()}"
                android:textColor="@color/color_wifi_input_tips"
                android:textSize="28mm"
                android:layout_marginBottom="21mm"
                app:layout_constraintBottom_toTopOf="@id/dialog_confirm_alert_line1"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/title"/>

            <!--left btn-->
            <TextView
                android:layout_width="0mm"
                android:layout_height="0mm"
                android:gravity="center"
                android:onClick="@{() -> confirmAlertDialog.lCLick()}"
                android:text="@{LanguageStrings.app_closefeedbackdialog_cancel_textview_text()}"
                android:textColor="@color/colorNavItemNormal"
                android:textSize="32mm"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/dialog_confirm_alert_line2"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/dialog_confirm_alert_line1" />

            <!-- right btn-->
            <TextView
                android:layout_width="0mm"
                android:layout_height="0mm"
                android:gravity="center"
                android:onClick="@{() -> confirmAlertDialog.rCLick()}"
                android:text="@{LanguageStrings.app_closefeedbackdialog_close_textview_text()}"
                android:textColor="@color/dialog_confirm_alert_right_btn_color"
                android:textSize="32mm"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/dialog_confirm_alert_line2"
                app:layout_constraintTop_toBottomOf="@id/dialog_confirm_alert_line1" />





        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>
