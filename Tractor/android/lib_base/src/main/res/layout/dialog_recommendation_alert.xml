<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="bottomAlertDialog"
            type="com.chervon.libBase.ui.dialog.RecommendationDialog" />

        <import type="com.chervon.libBase.utils.mlang.LanguageStrings" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:onClick="@{() -> bottomAlertDialog.closeDialog()}">

        <!--        card-->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0mm"
            android:layout_height="0mm"
            android:background="@drawable/shape_fillet_ffffff_left_right_20"
            android:paddingBottom="10mm"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHeight_percent="0.5"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="1">

            <ImageView
                android:layout_width="35mm"
                android:layout_height="35mm"
                android:layout_marginTop="40mm"
                android:layout_marginRight="20mm"
                android:onClick="@{() -> bottomAlertDialog.closeDialog()}"
                android:src="@drawable/ic_close"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="match_parent"
                android:layout_height="40mm"
                android:layout_marginStart="50mm"
                android:layout_marginTop="22mm"
                android:layout_marginEnd="50mm"
                android:gravity="center"
                android:onClick="@{() -> bottomAlertDialog.bottomClick()}"
                android:text="@{LanguageStrings.app_deviceregistpromote_title_textview_text()}"
                android:textColor="@color/black"
                android:textSize="34mm"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/dialog_bottom_alert_img"
                android:layout_width="300mm"
                android:layout_height="300mm"
                android:layout_marginTop="40mm"
                android:src="@drawable/ic_device_default"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvTitle" />

            <TextView
                android:id="@+id/dialog_bottom_alert_content"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="50mm"
                android:layout_marginEnd="50mm"
                android:gravity="center"
                android:text="@{bottomAlertDialog.content}"
                android:textColor="@color/colorText"
                android:textSize="30mm"
                app:layout_constrainedWidth="true"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/dialog_bottom_alert_img"
                app:layout_constraintVertical_bias="0.173" />


            <TextView
                android:layout_width="match_parent"
                android:layout_height="96mm"
                android:layout_marginStart="50mm"
                android:layout_marginTop="40mm"
                android:layout_marginEnd="50mm"
                android:background="@drawable/shape_fillet_77bc1f_4"
                android:gravity="center"
                android:onClick="@{() -> bottomAlertDialog.bottomClick()}"
                android:text="@{LanguageStrings.app_deviceregistpromote_shopnow_textview_text}"
                android:textColor="@color/white"
                android:textSize="36mm"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/dialog_bottom_alert_content" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
