<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>

    <variable
      name="messageAlertDialog"
      type="com.chervon.libBase.ui.dialog.MessageAlertDialog" />
  </data>

  <androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
      android:layout_width="540mm"
      android:layout_height="wrap_content"
      android:minHeight="360mm"
      android:background="@drawable/shape_fillet_ffffff_20"
      app:layout_constraintBottom_toBottomOf="parent"
      app:layout_constraintDimensionRatio="H,1:1.018"
      app:layout_constraintEnd_toEndOf="parent"
      app:layout_constraintStart_toStartOf="parent"
      app:layout_constraintTop_toTopOf="parent">

      <androidx.constraintlayout.widget.Guideline
        android:id="@+id/bottomLine"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_end="100mm" />

      <View
        android:id="@+id/dialog_message_alert_line1"
        android:layout_width="0mm"
        android:layout_height="1mm"
        android:background="@color/colorFrameLine"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/bottomLine" />


      <!-- title-->
      <TextView
        android:id="@+id/dialog_message_alert_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="56mm"
        android:gravity="center"
        android:text="@{messageAlertDialog.title}"
        android:textColor="@color/colorText"
        android:layout_marginLeft="32mm"
        android:layout_marginRight="32mm"
        android:textSize="34mm"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0"
        />

      <!--content-->
      <TextView
        android:id="@+id/tv_content"
        android:layout_width="0mm"
        android:layout_height="wrap_content"
        android:layout_marginTop="15mm"
        android:fadeScrollbars="true"
        android:gravity="center"
        android:layout_marginLeft="40mm"
        android:layout_marginRight="40mm"
        android:paddingBottom="26mm"
        android:text="@{messageAlertDialog.content}"
        android:textColor="@color/colorsecond"
        android:textSize="28mm"
        app:layout_constraintBottom_toTopOf="@id/dialog_message_alert_line1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/dialog_message_alert_title"
        app:layout_constraintVertical_bias="0" />

      <!--button_left-->
      <TextView
        android:id="@+id/tv_left"
        android:layout_width="0mm"
        android:layout_height="99mm"
        android:gravity="center"
        android:onClick="@{() -> messageAlertDialog.close()}"
        android:text="@{messageAlertDialog.btLeftText}"
        android:textColor="@color/colorNavItemNormal"
        android:textSize="32mm"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/dialog_message_alert_line1"
        app:layout_constraintWidth_percent="0.5" />

      <!--button_right-->
      <TextView
        android:id="@+id/tv_right"
        android:layout_width="0mm"
        android:layout_height="99mm"
        android:gravity="center"
        android:onClick="@{() -> messageAlertDialog.next()}"
        android:text="@{messageAlertDialog.btRightText}"
        android:textColor="@color/colorButtonNormal"
        android:textSize="32mm"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/dialog_message_alert_line1"
        app:layout_constraintVertical_bias="0"
        app:layout_constraintWidth_percent="0.5" />

      <View
        android:id="@+id/dialog_message_alert_bottom_ver_line"
        android:layout_width="1mm"
        android:layout_height="99mm"

        android:background="@color/colorFrameLine"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="@+id/tv_left"
        app:layout_constraintRight_toRightOf="@+id/tv_right"
        app:layout_constraintTop_toBottomOf="@+id/dialog_message_alert_line1" />


    </androidx.constraintlayout.widget.ConstraintLayout>

  </androidx.constraintlayout.widget.ConstraintLayout>

</layout>
