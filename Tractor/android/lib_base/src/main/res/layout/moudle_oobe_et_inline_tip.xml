<?xml version="1.0" encoding="utf-8"?>
<merge  xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto"
  xmlns:tools="http://schemas.android.com/tools"
  tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">



  <TextView
    android:id="@+id/tvWarningTip"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:textColor="@color/colorInlineText"
    android:background="@color/colorInlineBackground"
    android:paddingLeft="28mm"
    android:paddingRight="22mm"
    android:textSize="24mm"
    android:paddingTop="16mm"
    android:paddingBottom="20mm"
    app:layout_constraintTop_toBottomOf="@id/lllPassword"
    android:layout_marginTop="9mm"
    android:visibility="gone"
    />

  <androidx.appcompat.widget.LinearLayoutCompat
    android:id="@+id/lllPassword"
    android:layout_width="match_parent"
    android:layout_height="96mm"
    android:orientation="horizontal"
    android:background="@drawable/module_login_et_shape"
    >
    <EditText
      style="@style/et_login"
      android:gravity="center_vertical"
      android:textColorHint="@color/colorDisabled"
      android:layout_weight="1"
      android:letterSpacing="0"
      android:layout_gravity="center_vertical"
      />
    <ImageButton
      android:id="@+id/ibPassword"
      android:layout_width="40mm"
      android:layout_height="40mm"
      android:src="@drawable/ic_password_hide"
      android:background="@null"
      android:scaleType="fitCenter"
      android:layout_marginRight="20mm"
      android:visibility="gone"
      android:layout_gravity="center"

      />
  </androidx.appcompat.widget.LinearLayoutCompat>

</merge>
