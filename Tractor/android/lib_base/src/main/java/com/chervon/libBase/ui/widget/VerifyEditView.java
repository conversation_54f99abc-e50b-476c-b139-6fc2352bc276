package com.chervon.libBase.ui.widget;

import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.ContextMenu;
import android.view.Menu;
import android.view.inputmethod.EditorInfo;
import android.view.inputmethod.InputConnection;
import android.widget.EditText;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.blankj.utilcode.util.ClipboardUtils;
import com.blankj.utilcode.util.LogUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Author: 184862
 * @desc 验证码输入框定制
 * @CreateDate: 2023/9/26
 * @UpdateDate: 2023/9/26
 */
public class VerifyEditView extends androidx.appcompat.widget.AppCompatEditText {

  private OnPasteCallback onPasteCallback;


  public OnPasteCallback getOnPasteCallback() {
    return onPasteCallback;
  }

  public void setOnPasteCallback(OnPasteCallback onPasteCallback) {
    this.onPasteCallback = onPasteCallback;
  }

  public VerifyEditView(Context context) {
    super(context);
  }

  public VerifyEditView(Context context, AttributeSet attrs) {
    super(context, attrs);
  }

  public VerifyEditView(Context context, AttributeSet attrs, int defStyleAttr) {
    super(context, attrs, defStyleAttr);
  }

  @Override
  public void onCreateContextMenu(ContextMenu menu) {

    super.onCreateContextMenu(menu);
  }

  @Override
  public boolean onTextContextMenuItem(int id) {

    if (id == android.R.id.paste) {
      getClipboardText();
      return false;
    }


    return super.onTextContextMenuItem(id);

  }


  /**
   * copy text from clipboard
   */
  protected void getClipboardText() {

    String regex = "\\d{6}";

    String clipString = ClipboardUtils.getText().toString();

    if (TextUtils.isEmpty(clipString)) {
      return;
    }

    Pattern pattern = Pattern.compile(regex);
    Matcher matcher = pattern.matcher(clipString);
    if (matcher.find()) {
      if (null != onPasteCallback) {
        onPasteCallback.onPause(clipString);
      }
    }
  }

  public interface OnPasteCallback {
    void onPause(String clipboardResult);
  }
}
