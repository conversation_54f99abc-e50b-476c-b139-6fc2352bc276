package com.chervon.libBase.ui.widget;


import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.drawable.Drawable;
import android.text.Editable;
import android.text.InputFilter;
import android.text.InputType;
import android.text.TextWatcher;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.databinding.BindingAdapter;
import androidx.databinding.InverseBindingAdapter;
import androidx.databinding.InverseBindingListener;

import com.chervon.libBase.R;
import com.chervon.libBase.utils.UiHelper;


import me.jessyan.autosize.AutoSizeCompat;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleOobe.ui.widget.supertooltip
 * @ClassName: InlineTipEditText
 * @Description: the InlineTip   of EditText
 * @Author: wangheng
 * @CreateDate: 2022/6/18 下午7:02
 * @UpdateUser: wangheng
 * @UpdateDate: 2022/6/18 下午7:02
 * @UpdateRemark: new
 * @Version: 1.0
 */
public class InlineTipEditText extends ConstraintLayout {
    private final ViewGroup editLayout;
    private TextView mWarningTip = null;
    private EditText mEditText;
    private Drawable etShapeNormal;
    private Drawable etShapeFocus;
    private Drawable etShapeError;

    private TextChangedListenner mTextChangedListenner;
    private OnFocusChangeListener mOnFocusChangeListener;

    public InlineTipEditText(@NonNull Context context) {
        this(context, null);
    }

    public InlineTipEditText(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public InlineTipEditText(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);

        ViewGroup view = (ViewGroup) LayoutInflater.from(context).inflate(R.layout.moudle_oobe_et_inline_tip, this);
        @SuppressLint("CustomViewStyleable") TypedArray obtainStyledAttributes =
                getContext().obtainStyledAttributes(attrs, com.chervon.libBase.R.styleable.inline_tip_EditText);
        int iconPassword = (int) obtainStyledAttributes.getInt(com.chervon.libBase.R.styleable.inline_tip_EditText_icon_password, 0);
        String hint = obtainStyledAttributes.getString(com.chervon.libBase.R.styleable.inline_tip_EditText_hint_edit);
        // mEditText = (EditText) view.findViewById(R.id.etPassword);
        editLayout = (ViewGroup) findViewById(R.id.lllPassword);
        int count = editLayout.getChildCount();

        for (int i = 0; i < count; i++) {
            View child = editLayout.getChildAt(i);
            if (child instanceof EditText) {
                mEditText = (EditText) child;
            }
        }

        int maxCharLength = (int) obtainStyledAttributes.getInt(com.chervon.libBase.R.styleable.inline_tip_EditText_max_length, 0);
        if (maxCharLength != 0) {
            mEditText.setFilters(new InputFilter[]{new InputFilter.LengthFilter(maxCharLength)});
        }


        etShapeFocus = getResources().getDrawable(R.drawable.base_et_shape_focus);
        etShapeNormal = getResources().getDrawable(R.drawable.module_login_et_shape);
        etShapeError = getResources().getDrawable(com.chervon.libBase.R.drawable.base_et_shape_error);
        mEditText.setOnFocusChangeListener(new OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (hasFocus) {
                    editLayout.setBackground(etShapeFocus);
                } else {
                    editLayout.setBackground(etShapeNormal);
                }

                if (mOnFocusChangeListener != null) {
                    mOnFocusChangeListener.onFocusChange(v, hasFocus);
                }
            }
        });


        mWarningTip = findViewById(R.id.tvWarningTip);
        mEditText.setHint(hint);
        ImageButton imageButton = findViewById(R.id.ibPassword);
        if (iconPassword == 0) {
            mEditText.setInputType(InputType.TYPE_CLASS_TEXT);
            imageButton.setVisibility(GONE);
        } else {
            mEditText.setInputType(InputType.TYPE_CLASS_TEXT | InputType.TYPE_TEXT_VARIATION_PASSWORD);
            imageButton.setVisibility(VISIBLE);
        }

        imageButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                UiHelper.switchPasswordVisibility(imageButton, mEditText, view);
            }
        });
        obtainStyledAttributes.recycle();
    }

    public void showInlineTip(String tipStr) {
        if (tipStr != null) {
            mWarningTip.setText(tipStr);
            mWarningTip.setVisibility(VISIBLE);
            editLayout.setBackground(etShapeError);
        } else {
            mWarningTip.setVisibility(GONE);
        }
    }


    @BindingAdapter(value = "text_edit", requireAll = false)
    public static void setInlineTipEditText(InlineTipEditText inlineTipEditText, String str) {
        inlineTipEditText.setText(str);

    }

    @BindingAdapter(value = "hint_edit", requireAll = false)
    public static void setInlineTipHint(InlineTipEditText inlineTipEditText, String str) {
        inlineTipEditText.setHint(str);
    }

    private void setHint(String str) {
        mEditText.setHint(str);
    }

    public void setText(String str) {
        mEditText.setText(str);
    }


    @InverseBindingAdapter(attribute = "text_edit")
    public static String getInlineTipEditText(InlineTipEditText inlineTipEditText) {
        return inlineTipEditText.getInlineTipEditTextString();
    }

    public String getInlineTipEditTextString() {
        return mEditText.getText().toString();
    }


    public void addTextChangedListener(TextWatcher watcher) {
        mEditText.addTextChangedListener(watcher);
    }


    @InverseBindingAdapter(attribute = "text_edit", event = "philprogressAttrChanged")
    public static String getPhilProgress(InlineTipEditText seekBar) {
        return seekBar.getInlineTipEditTextString();
    }

    private static InverseBindingListener mInverseBindingListener;

    @BindingAdapter(value = {"philprogressAttrChanged"}, requireAll = false)
    public static void setPhilProgressAttrChanged(InlineTipEditText seekBar, InverseBindingListener inverseBindingListener) {
        if (inverseBindingListener == null) {

        } else {
            mInverseBindingListener = inverseBindingListener;
        }
    }
  public   void clear() {
    mInverseBindingListener=null;
  }

    public interface TextChangedListenner {
        /**
         * when text changed
         *
         * @param str text
         */
        public void textChanged(String str);
    }

    public TextChangedListenner getmTextChangedListenner() {
        return mTextChangedListenner;
    }

    public void setmTextChangedListenner(TextChangedListenner mTextChangedListenner) {
        this.mTextChangedListenner = mTextChangedListenner;


        mEditText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {


            }

            @Override
            public void afterTextChanged(Editable s) {
                if (mTextChangedListenner != null) {
                    mTextChangedListenner.textChanged(s.toString());
                }

            }
        });

    }

    public void setEditTextOnFocusChangeListener(OnFocusChangeListener l) {
        mOnFocusChangeListener = l;

    }

    public void setPassWordType() {
        mEditText.setInputType(InputType.TYPE_CLASS_TEXT | InputType.TYPE_TEXT_VARIATION_PASSWORD);
    }


    @Override
    public LayoutParams generateLayoutParams(AttributeSet attrs) {
        AutoSizeCompat.autoConvertDensityOfGlobal(getResources());
        AutoSizeCompat.autoConvertDensity(getResources(), 1334, false);
        return super.generateLayoutParams(attrs);
    }
}
