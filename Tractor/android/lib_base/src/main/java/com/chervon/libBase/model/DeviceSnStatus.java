package com.chervon.libBase.model;

import androidx.databinding.BaseObservable;

import java.io.Serializable;

/**
 * @ProjectName: app
 * @Package: com.chervon.libBase.model
 * @ClassName: DeviceSnStatus
 * @Description: 设备SN校验状态
 * @Author: wuxd
 * @CreateDate: 2024/6/21
 * @UpdateUser:
 * @UpdateDate:
 * @UpdateRemark:
 * @Version: 1.0
 */
public class DeviceSnStatus extends BaseObservable implements Serializable {
  /**
   * SN
   */
  private String sn;
  /**
   * 能否注册
   */
  private boolean canRegister;
  /**
   * 校验返回信息
   */
  private String message;

  public void setSn(String sn) {
    this.sn = sn;
  }

  public String getSn() {
    return sn;
  }

  public void setCanRegister(boolean canRegister) {
    this.canRegister = canRegister;
  }

  public boolean isCanRegister() {
    return canRegister;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public String getMessage() {
    return message;
  }
}
