package com.chervon.libBase.ui.view;

import android.content.Context;
import android.graphics.Rect;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.VelocityTracker;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewGroup;
import android.widget.Scroller;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.LogUtils;
/**
 * @ProjectName: app
 * @Package: com.chervon.libBase.ui.view
 * @ClassName: XSlideRecycleView
 * @Description:  the RecycleView
 * @Author: LangMeng
 * @CreateDate: 2022/4/22 19:34
 * @UpdateUser: LangMeng
 * @UpdateDate: 2022/4/22 19:34
 * @UpdateRemark: new class
 * @Version: 1.0
 */
public class XSlideRecycleView extends RecyclerView {

    private static final String TAG = "XSlideRecyclerView";
    private static final int INVALID_POSITION = -1;
    private static final int INVALID_CHILD_WIDTH = -1;
    private static final int SNAP_VELOCITY = 600;

    private VelocityTracker mVelocityTracker;
    private int mTouchSlop;
    private Rect mTouchFrame;
    private Scroller mScroller;
    private float mLastX;
    private float mFirstX, mFirstY;
    private boolean mIsSlide;
    private ViewGroup mFlingView;
    private int mPosition;
    private int mMenuViewWidth;

    public XSlideRecycleView(@NonNull Context context) {
        super(context);
    }

    public XSlideRecycleView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        mTouchSlop = ViewConfiguration.get(getContext()).getScaledTouchSlop();
        mScroller = new Scroller(getContext());
    }

    public XSlideRecycleView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
    }

    @Override
    public void onViewAdded(View child) {
        super.onViewAdded(child);
    }

    /**
     * Determine whether it meets the side-slip scene
     *     1. If the speed in the x direction is greater than the speed in the y direction, and is greater than the minimum speed limit;
     *     2. If the sideslip distance in the x direction is greater than the sliding distance in the y direction, and the x direction reaches
     *        the minimum sliding distance;
     * @param e
     * @return
     */
    @Override
    public boolean onInterceptTouchEvent(MotionEvent e) {
        int x = (int) e.getX();
        int y = (int) e.getY();
        obtainVelocityTracker(e);
        LogUtils.iTag(TAG,"onInterceptTouchEvent", x, y, e.getAction());
        switch (e.getAction()) {
            case MotionEvent.ACTION_DOWN:
                if (!mScroller.isFinished()) {
                    mScroller.abortAnimation();
                }
                mFirstX = mLastX = x;
                mFirstY = y;
                mPosition = pointToPosition(x, y);
                LogUtils.iTag(TAG,"onInterceptTouchEvent", "mPosition = " + mPosition);
                if (mPosition != INVALID_POSITION) {
                    View view = mFlingView;
                    mFlingView = (ViewGroup) getChildAt(mPosition - ((LinearLayoutManager) getLayoutManager()).findFirstVisibleItemPosition());
                    if (view != null && mFlingView != view && view.getScrollX() != 0) {
                        view.scrollTo(0, 0);
                    }
                    LogUtils.iTag(TAG,"onInterceptTouchEvent", "mFlingView.getChildCount() = " + mFlingView.getChildCount());
                    if (mFlingView.getChildCount() == 2) {
                        mMenuViewWidth = mFlingView.getChildAt(1).getWidth();
                    } else {
                        mMenuViewWidth = INVALID_CHILD_WIDTH;
                    }
                }
                break;
            case MotionEvent.ACTION_MOVE:
                mVelocityTracker.computeCurrentVelocity(1000);
                float xVelocity = mVelocityTracker.getXVelocity();
                float yVelocity = mVelocityTracker.getYVelocity();
                boolean conditionXVelocity = Math.abs(xVelocity) > SNAP_VELOCITY && Math.abs(xVelocity) > Math.abs(yVelocity);
                boolean conditionXDistance = Math.abs(x - mFirstX) >= mTouchSlop && Math.abs(x - mFirstX) > Math.abs(y - mFirstY);
                LogUtils.iTag(TAG,"onInterceptTouchEvent", "conditionXVelocity = " + conditionXVelocity, "conditionXDistance = " + conditionXDistance);
                if (conditionXVelocity || conditionXDistance) {
                    mIsSlide = true;
                    return true;
                }
                break;
            case MotionEvent.ACTION_UP:
                releaseVelocityTracker();
                break;
        }
        boolean b = super.onInterceptTouchEvent(e);
        LogUtils.iTag(TAG, "onInterceptTouchEvent:" + b);
        return b;
    }

    /**
     * item sliding,
     * Conditions for opening the right menu:
     *      1. The width of the menu being pulled out is greater than half the width of the menu;
     *      2. The lateral sliding speed is greater than the minimum sliding speed;
     * Note: The reason why it is smaller than the negative value is because the speed is negative when sliding to the left
     * @param e
     * @return
     */
    @Override
    public boolean onTouchEvent(MotionEvent e) {
        LogUtils.iTag(TAG,"onTouchEvent", mIsSlide, mPosition, e.getAction());
        if (mIsSlide && mPosition != INVALID_POSITION) {
            float x = e.getX();
            obtainVelocityTracker(e);
            switch (e.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    LogUtils.iTag(TAG, "onTouchEvent" + e.getAction());
                    break;
                case MotionEvent.ACTION_MOVE:
                    if (mMenuViewWidth != INVALID_CHILD_WIDTH) {
                        float dx = mLastX - x;
                        if (mFlingView.getScrollX() + dx <= mMenuViewWidth
                                && mFlingView.getScrollX() + dx > 0) {
                            mFlingView.scrollBy((int) dx, 0);
                        }
                        mLastX = x;
                    }
                    break;
                case MotionEvent.ACTION_UP:
                    if (mMenuViewWidth != INVALID_CHILD_WIDTH) {
                        int scrollX = mFlingView.getScrollX();
                        mVelocityTracker.computeCurrentVelocity(1000);
                        LogUtils.iTag(TAG, "ACTION_UP: mVelocityTracker.getXVelocity() = " + mVelocityTracker.getXVelocity(),
                                "scrollX = " + scrollX,
                                "mMenuViewWidth = " + mMenuViewWidth,
                                "mVelocityTracker.getXVelocity() < -SNAP_VELOCITY = " + (mVelocityTracker.getXVelocity() < -SNAP_VELOCITY));
                        if (mVelocityTracker.getXVelocity() < -SNAP_VELOCITY) {
                            mScroller.startScroll(scrollX, 0, mMenuViewWidth - scrollX, 0, Math.abs(mMenuViewWidth - scrollX));
                        } else if (mVelocityTracker.getXVelocity() >= SNAP_VELOCITY) {
                            mScroller.startScroll(scrollX, 0, -scrollX, 0, Math.abs(scrollX));
                        } else if (scrollX >= mMenuViewWidth / 2) {
                            mScroller.startScroll(scrollX, 0, mMenuViewWidth - scrollX, 0, Math.abs(mMenuViewWidth - scrollX));
                        } else {
                            mScroller.startScroll(scrollX, 0, -scrollX, 0, Math.abs(scrollX));
                        }
                        invalidate();
                    }
                    mMenuViewWidth = INVALID_CHILD_WIDTH;
                    mIsSlide = false;
                    mPosition = INVALID_POSITION;
                    releaseVelocityTracker();
                    break;
            }
            return true;
        } else {
            closeMenu();
            // Velocity, the release here is to prevent the RecyclerView from being intercepted normally, but it is not released in onTouchEvent;
            // There are three situations:
            //     1. The onInterceptTouchEvent is not intercepted. In the onInterceptTouchEvent method, a pair of DOWN and UP are acquired and released;
            //     2. OnInterceptTouchEvent is intercepted and DOWN is acquired, but the event is not handled by the side slip and needs to be released here;
            //     3. OnInterceptTouchEvent is intercepted, DOWN is acquired, and the event is handled by the side slip, then it is released in the UP of onTouchEvent.
            releaseVelocityTracker();
        }
        boolean b = super.onTouchEvent(e);
        LogUtils.iTag(TAG, "super.onTouchEvent(e)=" + b);
        return b;
    }

    @Override
    public void computeScroll() {
        if (mScroller.computeScrollOffset()) {
            mFlingView.scrollTo(mScroller.getCurrX(), mScroller.getCurrY());
            invalidate();
        }
    }

    /**
     * obtain velocity tracker
     * @param event
     */
    private void obtainVelocityTracker(MotionEvent event) {
        if (mVelocityTracker == null) {
            mVelocityTracker = VelocityTracker.obtain();
        }
        mVelocityTracker.addMovement(event);
    }

    /**
     * Return the position of the corresponding item according to the position of the touch point
     * @param x
     * @param y
     * @return
     */
    private int pointToPosition(int x, int y) {
        int firstPosition = ((LinearLayoutManager) getLayoutManager()).findFirstVisibleItemPosition();
        Rect frame = mTouchFrame;
        if (frame == null) {
            mTouchFrame = new Rect();
            frame = mTouchFrame;
        }

        final int count = getChildCount();
        for (int i = count - 1; i >= 0; i--) {
            final View child = getChildAt(i);
            if (child.getVisibility() == View.VISIBLE) {
                child.getHitRect(frame);
                if (frame.contains(x, y)) {
                    return firstPosition + i;
                }
            }
        }
        return INVALID_POSITION;
    }

    /**
     * release velocity tracker
     */
    private void releaseVelocityTracker() {
        if (mVelocityTracker != null) {
            mVelocityTracker.clear();
            mVelocityTracker.recycle();
            mVelocityTracker = null;
        }
    }

    /**
     * Close the subview that displays the submenu
     */
    public void closeMenu() {
        if (mFlingView != null && mFlingView.getScrollX() != 0) {
//            mFlingView.scrollTo(0, 0);
            int scrollX = mFlingView.getScrollX();
            mScroller.startScroll(scrollX, 0, -scrollX, 0, Math.abs(scrollX));
            invalidate();
        }
    }

    /**
     * Close the subview that displays the submenu
     */
    public void closeMenuWithoutDuration() {
        if (mFlingView != null && mFlingView.getScrollX() != 0) {
//            mFlingView.scrollTo(0, 0);
            int scrollX = mFlingView.getScrollX();
            mScroller.startScroll(scrollX, 0, -scrollX, 0, 0);
            invalidate();
        }
    }
}
