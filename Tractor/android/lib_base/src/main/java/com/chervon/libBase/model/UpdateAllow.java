package com.chervon.libBase.model;

/**
 * @ProjectName: app
 * @Package: com.chervon.libOTA.data
 * @ClassName: FirmwareVersionData
 * @Description: 总成版本数据类
 * @Author: langmeng
 * @CreateDate: 2022/7/19 14:57
 * @UpdateUser: langmeng
 * @UpdateDate: 2022/7/19 14:57
 * @UpdateRemark: new class
 * @Version: 1.0
 */
public class UpdateAllow {
    private boolean allow;

  public UpdateAllow() {
  }

  public UpdateAllow(boolean allow) {
    this.allow = allow;
  }

  public boolean isAllow() {
    return allow;
  }

  public void setAllow(boolean allow) {
    this.allow = allow;
  }
}
