package com.chervon.libBase.ui.widget;

import android.app.Activity;
import android.content.Context;
import android.util.AttributeSet;
import android.view.MenuItem;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.navigation.NavController;
import androidx.navigation.Navigation;
import androidx.navigation.ui.AppBarConfiguration;
import androidx.navigation.ui.NavigationUI;

import com.chervon.libBase.R;
import com.blankj.utilcode.util.LogUtils;
import com.google.android.material.bottomnavigation.BottomNavigationView;

/**
 * @ProjectName: app
 * @Package: com.chervon.libBase.ui.widget
 * @ClassName: QFBottomNavigationView
 * @Description: the custom of BottomNavigation
 * @Author: wangheng
 * @CreateDate: 2022/4/19 下午7:02
 * @UpdateUser: wangheng
 * @UpdateDate: 2022/4/19 下午7:02
 * @UpdateRemark: new
 * @Version: 1.0
 */
public class QFBottomNavigationView extends com.google.android.material.bottomnavigation.BottomNavigationView implements BottomNavigationView.OnNavigationItemReselectedListener {
    public QFBottomNavigationView(@NonNull Context context) {
        this(context, null);
    }

    public QFBottomNavigationView(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public QFBottomNavigationView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        setLabelVisibilityMode(LABEL_VISIBILITY_LABELED);
        setItemIconTintList(null);
        setOnNavigationItemReselectedListener(this);
        
        // 延迟初始化导航控制器，确保Activity已经完全初始化
        post(() -> {
            try {
                NavController navController = Navigation.findNavController((Activity) getContext(), R.id.nav_host_fragment_content_main);
                NavigationUI.setupWithNavController(this, navController);
            } catch (Exception e) {
                LogUtils.e("QFBottomNavigationView", "Failed to setup navigation: " + e.getMessage());
            }
        });
    }

    @Override
    public void onNavigationItemReselected(@NonNull MenuItem item) {

    }
}
