package com.chervon.libBase.ui.adapter;

import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RadioButton;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatCheckBox;
import androidx.recyclerview.widget.RecyclerView;

import com.chervon.libBase.R;
import com.chervon.libBase.model.DictNode;

import org.w3c.dom.Text;


public class DialogBottomListAdapter extends RecyclerView.Adapter<DialogBottomListAdapter.ViewHolder> {
    private Object[] dataList;
    private boolean[] itemSelected;
    private OnItemClickListener onItemClickListener;
    private int selectPosition=-1;
    public DialogBottomListAdapter(Object[] dataList) {
        this.dataList = dataList;
        if (dataList != null) {
            itemSelected = new boolean[dataList.length];
        }

    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.base_dialog_bottom_item, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
         String content;
        if(dataList[position] instanceof DictNode){
              content = TextUtils.isEmpty(((DictNode)dataList[position]).getDescription())?"":((DictNode)dataList[position]).getDescription();
//              if(TextUtils.isEmpty(content)){
//                  content  =((DictNode)dataList[position]).getLabel();
//              }
        }else{
            content = TextUtils.isEmpty((String)dataList[position])?"":(String)dataList[position];

        }

        final int currentPostion = position;
        holder.tv.setText(content);
        if (itemSelected!=null&&itemSelected.length>0&&itemSelected[currentPostion]) {
            holder.iv.setChecked(true);
        } else {
            holder.iv.setChecked(false);
        }

        holder.itemView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
              try{
                if (onItemClickListener != null) {
                  for (int i = 0; i < itemSelected.length; i++) {
                    itemSelected[i] = false;
                  }
                  itemSelected[currentPostion] = true;
                  view.setTag(currentPostion);
                  notifyDataSetChanged();
                  selectPosition=currentPostion;
                  // onItemClickListener.onClick(view, content);
                }
              }catch (Exception e){

              }

            }
        });
    }

    @Override
    public int getItemCount() {
        return dataList == null ? 0 : dataList.length;
    }

    static class ViewHolder extends RecyclerView.ViewHolder {
        private TextView tv;
        private AppCompatCheckBox iv;

        ViewHolder(@NonNull View itemView) {
            super(itemView);
            tv = itemView.findViewById(R.id.tv);
            iv = itemView.findViewById(R.id.ivItemSelected);
        }
    }

    public OnItemClickListener getOnItemClickListener() {
        return onItemClickListener;
    }

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }


    public String getContent() {
        if(dataList==null||dataList.length==0||selectPosition==-1){
            return "";
        }
        if( dataList[selectPosition] instanceof DictNode){
//            if(TextUtils.isEmpty(((DictNode)dataList[selectPosition]).getDescription())){
//                return ((DictNode)dataList[selectPosition]).getLabel();
//            }
            return TextUtils.isEmpty(((DictNode)dataList[selectPosition]).getDescription())?"":((DictNode)dataList[selectPosition]).getDescription();
        }else{
            return (String)dataList[selectPosition];
        }
    }
    public int getPosition() {
        return selectPosition;
    }

    public interface OnItemClickListener {
        void onClick(View var1, String itemName);
    }

    public Object[] getDataList() {
        return dataList;
    }

    public void setDataList(Object[] dataList) {
        this.dataList = dataList;
    }
}
