package com.chervon.libBase.utils;

/**
 * @ProjectName: app
 * @Package: com.chervon.libBase.utils
 * @ClassName: APPConstants
 * @Description: 应用全局常量
 * @Author: <PERSON><PERSON><PERSON>
 * @CreateDate: 2022/7/1 17:58
 * @UpdateUser: <PERSON><PERSON><PERSON>
 * @UpdateDate: 2022/7/1 17:58
 * @UpdateRemark: new class
 * @Version: 1.0
 */
public class APPConstants {

    public static final String R_DEVICE = "R";
    //S3图片桶
    public static final String PHOTO_BUCKETNAME = "chervon-iot-dev-picture";
    public static final int RESPONSE_FAIL = 2;
    public static final int RESPONSE_SN_FAIL = 21;
    public static final int EDIT_CONTENT_IS_NULL = 100110;//输入内容未空
    public static final String IS_RESET_WIFI_CONNECTION = "isResetWifiConnection";

    public static String wifi_device_topic_on_line_topic;
    //TODU 72002
    public static final String PUBLISH_ERROR = "publish_error";

  public static final String HTML_CONTAINTER = "<!DOCTYPE html><html>\n" +
    "\t<head>\n" +
    "\t\t<title></title>\n" +
    "\t</head>\n" +
    "\t<body>\n" +
    "\t\t\t<div   >#{content}</div>\n" +
    "\t</body>\n" +
    "</html>";


    //消息类型，0系统消息，
    public static final String MESSAGE_TYPE_SYSTEM = "0";
    //消息类型， 1营销消息,
    public static final String MESSAGE_TYPE_OFFER = "1";
    //消息类型，2设备消息
    public static final String MESSAGE_TYPE_DEVICE = "2";
    //消息类型，3反馈消息
    public static final String MESSAGE_TYPE_FEEDBACK = "3";
    //消息类型，4设备分享
    public static final String MESSAGE_TYPE_SHARE_DEVICE = "4";

    public static final String MESSAGE_RUTE_PATH = "rutePath";

    public static final String MESSAGE_DATA_JSON = "messageDataJson";

    public static final String MESSAGE_KEY_CREATETIME = "createTime";
    public static final String MESSAGE_KEY_UUID = "uuid";
    public static final String MESSAGE_KEY_MESSAGE_TYPE = "messageType";
    /**
     * 主用户分享设备
     */
    public static final String SHARE_OPERATE_TYPE_MAIN_SHARE = "0";
    /**
     * 子用户接受分享
     */
    public static final String SHARE_OPERATE_TYPE_SUB_ACCEPT ="1";
    /**
     * 子用户删除设备
     */
    public static final String SHARE_OPERATE_TYPE_SUB_DELETE ="2";
    /**
     * 子用户删除分享
     */
    public static final String SHARE_OPERATE_TYPE_SUB_REMOVE ="3";
    /**
     * 主用户删除设备/设备分享
     */
    public static final String SHARE_OPERATE_TYPE_MAIN_REMOVE="4";
    /**
     * 设备分享 0:share 1:accept
     */
    public static final int DEVICE_SHARE_TYPE_SHARE = 0;
    public static final int DEVICE_SHARE_TYPE_ACCEPT = 1;

    /**
     * App升级相关
     */
    public static final String VERSION_NAME_KEY = "lastVersionName";
    public static final String VERSION_CODE_KEY = "lastVersionCode";
    public static final String FORCE_DIALOG_KEY = "forceDialog";
}
