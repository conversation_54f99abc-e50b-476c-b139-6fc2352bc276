package com.chervon.libBase.model;

import android.text.TextUtils;

import java.io.Serializable;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleDeviceManage.ui.state
 * @ClassName: DeviceListUistate
 * @Description: 类描述
 * @Author: name
 * @CreateDate: 2022/5/17 下午6:18
 * @UpdateUser: 更新者
 * @UpdateDate: 2022/5/17 下午6:18
 * @UpdateRemark: 更新说明
 * @Version: 1.0
 */
public class QuestionPageData implements Serializable {
  private String deviceId;
  private String firstName;
  private String lastName;
  private String address;
  private String city;
  private String zipCode;
  private String country;
  private String province;
  private String phone;
  private String email;
  private int isParticipateResearch;


  public void setIsParticipateResearch(int isParticipateResearch) {
    this.isParticipateResearch = isParticipateResearch;
  }


  private Integer[] questionOneAnswers;


  private String questionOneOther;

  private Integer[] questionTwoAnswers;


  private String questionTwoOther;

  private Integer[] questionThreeAnswers;


  private String questionThreeOther;

  private Integer[] questionFourAnswers;


  private String questionFourOther;


  public String getFirstName() {
    return firstName;
  }

  public void setFirstName(String firstName) {
    this.firstName = firstName;
  }

  public String getLastName() {
    return lastName;
  }

  public void setLastName(String lastName) {
    this.lastName = lastName;
  }

  public String getAddress() {
    return address;
  }

  public void setAddress(String address) {
    this.address = address;
  }

  public String getCity() {
    return city;
  }

  public void setCity(String city) {
    this.city = city;
  }

  public String getZipCode() {
    return zipCode;
  }

  public void setZipCode(String zipCode) {
    this.zipCode = zipCode;
  }

  public String getProvince() {
    return province;
  }

  public void setProvince(String province) {
    this.province = province;
  }

  public String getPhone() {
    return phone;
  }

  public void setPhone(String phone) {
    this.phone = phone;
  }

  public String getEmail() {
    return email;
  }

  public void setEmail(String email) {
    this.email = email;
  }


  public String getDeviceId() {
    return deviceId;
  }

  public void setDeviceId(String deviceId) {
    this.deviceId = deviceId;
  }

  public Integer[] getQuestionOneAnswers() {
    return questionOneAnswers;
  }

  public void setQuestionOneAnswers(Integer[] questionOneAnswers) {
    this.questionOneAnswers = questionOneAnswers;
  }

  public String getQuestionOneOther() {
    return questionOneOther;
  }

  public void setQuestionOneOther(String questionOneOther) {
    this.questionOneOther = questionOneOther;
  }

  public Integer[] getQuestionTwoAnswers() {
    return questionTwoAnswers;
  }

  public void setQuestionTwoAnswers(Integer[] questionTwoAnswers) {
    this.questionTwoAnswers = questionTwoAnswers;
  }

  public String getQuestionTwoOther() {
    return questionTwoOther;
  }

  public void setQuestionTwoOther(String questionTwoOther) {
    this.questionTwoOther = questionTwoOther;
  }

  public Integer[] getQuestionThreeAnswers() {
    return questionThreeAnswers;
  }

  public void setQuestionThreeAnswers(Integer[] questionThreeAnswers) {
    this.questionThreeAnswers = questionThreeAnswers;
  }

  public String getQuestionThreeOther() {
    return questionThreeOther;
  }

  public void setQuestionThreeOther(String questionThreeOther) {
    this.questionThreeOther = questionThreeOther;
  }

  public Integer[] getQuestionFourAnswers() {
    return questionFourAnswers;
  }

  public void setQuestionFourAnswers(Integer[] questionFourAnswers) {
    this.questionFourAnswers = questionFourAnswers;
  }

  public String getQuestionFourOther() {
    return questionFourOther;
  }

  public void setQuestionFourOther(String questionFourOther) {
    this.questionFourOther = questionFourOther;
  }

  public int getIsParticipateResearch() {
    return isParticipateResearch;
  }

  public String getCountry() {
    return country;
  }

  public void setCountry(String country) {
    this.country = country;
  }


}
