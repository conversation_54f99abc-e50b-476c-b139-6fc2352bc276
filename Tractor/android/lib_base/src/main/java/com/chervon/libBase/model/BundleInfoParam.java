package com.chervon.libBase.model;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleContainer.panel.data.entity
 * @ClassName: BundleInfoEntry
 * @Description: 类描述
 * @Author: name
 * @CreateDate: 2022/9/5 下午6:00
 * @UpdateUser: 更新者
 * @UpdateDate: 2022/9/5 下午6:00
 * @UpdateRemark: 更新说明
 * @Version: 1.0
 */
public class BundleInfoParam {
    private String   appType;
    private String   appVersion ;
    private Long       productId;
    private String     rnVersion="";

    public BundleInfoParam(String appType, String appVersion, Long productId, String rnVersion) {
        this.appType = appType;
        this.appVersion = appVersion;
        this.productId = productId;
        this.rnVersion = rnVersion;
    }

    public String getAppType() {
        return appType;
    }

    public void setAppType(String appType) {
        this.appType = appType;
    }

    public String getAppVersion() {
        return appVersion;
    }

    public void setAppVersion(String appVersion) {
        this.appVersion = appVersion;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public String getRnVersion() {
        return rnVersion;
    }

    public void setRnVersion(String rnVersion) {
        this.rnVersion = rnVersion;
    }
}
