package com.chervon.libBase.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.text.SpannableString;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.blankj.utilcode.util.FragmentUtils;
import com.bumptech.glide.Glide;
import com.chervon.libBase.R;
import com.chervon.libBase.databinding.DialogBottomAlertBinding;
import com.chervon.libBase.databinding.DialogConfirmAlertBinding;

import java.lang.reflect.Field;

import io.reactivex.functions.Consumer;

/**
 * @ProjectName: app
 * @Package: com.chervon.libBase.ui.dialog
 * @ClassName: BottomAlertDialog
 * @Description: 类描述
 * @Author: langmeng
 * @CreateDate: 2022/7/14 17:03
 * @UpdateUser: langmeng
 * @UpdateDate: 2022/7/14 17:03
 * @UpdateRemark: new class
 * @Version: 1.0
 */
public class BottomAlertDialog extends DialogFragment {

    //content text
    public SpannableString content;
    //image url
    public String imgUrl;
    //left btn text
    public String lText;
    //right btn text
    public String rText;
    //data observer
    private Consumer<Boolean> consumer;

    public BottomAlertDialog() {
      super();

    }

    /**
     * @param fragmentManager 用于显示dialog,conten 内容文本, lText 左按钮文本, rText 右按钮文本, callback 回调接口
     * @return void
     * @method show
     * @description show dialog and get update info.
     * @date: 2022/6/22 11:36
     * @author: LangMeng
     */
    public static void show(@NonNull FragmentManager fragmentManager, @NonNull SpannableString content, String imgUrl, @NonNull String lText, @NonNull String rText, Consumer<Boolean> callback) {

        Fragment fragment = FragmentUtils.findFragment(fragmentManager, BottomAlertDialog.class.getName());
        if (fragment == null) {
            BottomAlertDialog bottomAlertDialog = new BottomAlertDialog();


            bottomAlertDialog.setData(content, imgUrl, lText, rText, callback);
            bottomAlertDialog.showFragmentDialog(fragmentManager, BottomAlertDialog.class.getName());
        }
    }


    //处理 Can not perform this action after onSaveInstanceState
    private void showFragmentDialog(FragmentManager manager,String tag){
      try {
        //由于父类方法中mDismissed，mShownByMe不可直接访问，所以此处采用反射修改他们的值
        Class dialogFragmentClass = DialogFragment.class;
        Field mDismissed = dialogFragmentClass.getDeclaredField("mDismissed");
        mDismissed.setAccessible(true);
        mDismissed.set(this, false);

        Field mShownByMe = dialogFragmentClass.getDeclaredField("mShownByMe");
        mShownByMe.setAccessible(true);
        mShownByMe.set(this, true);

        FragmentTransaction ft = manager.beginTransaction();
        ft.add(this, tag);
        ft.commitNowAllowingStateLoss();
      } catch (Exception e)  {
        e.printStackTrace();
      }
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStyle(DialogFragment.STYLE_NO_FRAME, R.style.NoBackgroundDialog);
    }

    @SuppressLint("NotifyDataSetChanged")
    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {

        getDialog().requestWindowFeature(Window.FEATURE_NO_TITLE);

        DialogBottomAlertBinding inflate = DataBindingUtil.inflate(getLayoutInflater(),
                R.layout.dialog_bottom_alert, container, false);

        inflate.setBottomAlertDialog(this);

        Glide.with(this).load(imgUrl)
                .error(new ColorDrawable(getResources().getColor(R.color.colorBackground)))
                .placeholder(R.drawable.ic_device_default)
                .into(inflate.dialogBottomAlertImg);

        return inflate.getRoot();
    }

    @Override
    public void onStart() {
        super.onStart();

        //set dialog window
        Dialog dialog = getDialog();
        assert dialog != null;
        dialog.getWindow().getDecorView().setPadding(0, 0, 0, 0);
        dialog.getWindow().setGravity(Gravity.BOTTOM);
        WindowManager.LayoutParams attributes = dialog.getWindow().getAttributes();
        attributes.width = WindowManager.LayoutParams.MATCH_PARENT;
        attributes.height = WindowManager.LayoutParams.MATCH_PARENT;
        dialog.getWindow().setAttributes(attributes);
        dialog.getWindow().setWindowAnimations(R.style.dialog_bottom);

        getDialog().setCancelable(false);
    }


    /**
     * @param content 内容文本, lText 左按钮文本, rText 右按钮文本, callback 回调接口
     * @return void
     * @method setData
     * @description 设置弹窗数据
     * @date: 2022/6/22 11:35
     * @author: LangMeng
     */
    public void setData(@NonNull SpannableString content, @NonNull String imgUrl, @NonNull String lText, @NonNull String rText, Consumer<Boolean> callback) {
        this.content = content;
        this.imgUrl = imgUrl;
        this.lText = lText;
        this.rText = rText;
        this.consumer = callback;
    }

    /**
     * @method lCLick
     * @description 左按钮点击事件
     * @date: 2022/6/22 11:39
     * @author: LangMeng
     * @return void
     */
    public void lCLick() {
        if (consumer != null) {
            try {
                consumer.accept(false);
                dismiss();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
    /**
     * @method rCLick
     * @description 右按钮点击事件
     * @date: 2022/6/22 11:39
     * @author: LangMeng
     * @return void
     */
    public void rCLick() {
        if (consumer != null) {
            try {
                consumer.accept(true);
                dismiss();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }


  @Override
  public void dismiss() {
    if (getFragmentManager()==null){
      //   Log.w(TAG, "dismiss: "+this+" not associated with a fragment manager." );
    }else {
      super.dismiss();
    }

  }

  @Override
  public void onDestroyView() {
      getDialog().setOnCancelListener(null);
    super.onDestroyView();
  }
}

