package com.chervon.libBase.ui;

import static android.content.pm.ActivityInfo.SCREEN_ORIENTATION_PORTRAIT;

import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libBase.utils.Utils.sendDurationTrace;
import static com.chervon.libBase.utils.Utils.sendExposure;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.content.pm.ActivityInfo;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.content.res.TypedArray;
import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.os.IBinder;
import android.os.Looper;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.databinding.DataBindingUtil;
import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.ViewModelProvider;
import androidx.navigation.NavController;
import androidx.navigation.NavGraph;
import androidx.navigation.Navigation;
import androidx.window.layout.FoldingFeature;

import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.ServiceUtils;
import com.chervon.libBase.BaseApplication;
import com.chervon.libBase.R;
import com.chervon.libBase.ui.viewmodel.BaseViewModel;
import com.chervon.libBase.ui.widget.ProgressHelper;
import com.chervon.libBase.ui.widget.QFToast;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Locale;
import java.util.Set;

import me.jessyan.autosize.AutoSizeCompat;
import me.jessyan.autosize.AutoSizeConfig;


/**
 * @ProjectName: app
 * @Package: com.chervon.libBase.ui
 * @ClassName: BaseActivity
 * @Description: the base  Activity of app
 * @Author: wangheng
 * @CreateDate: 2022/4/19 下午7:02
 * @UpdateUser: wangheng
 * @UpdateDate: 2022/4/19 下午7:02
 * @UpdateRemark: new
 * @Version: 1.0
 */
public abstract class BaseActivity<M extends BaseViewModel> extends AppCompatActivity {
  protected M mViewModel;
  protected NavGraph navGraph;
  protected BaseEnhanceFragment.OnkeyBackListener mkeyBackListener;
  protected NavController navController;
  protected int navId;
  public long enterTime = System.currentTimeMillis();
  public String mouduleId;
  public String pageId;
  public String pageResouce;
  public String stayEleId;
  public String nextButtoneleid;
  public  HashMap<String,String> expandMap = new HashMap<>();
  //消息中心Service
  ServiceConnection mServiceConnection;

//  private WindowInfoTrackerCallbackAdapter  windowInfoTracker;
//  private final   LayoutStateChangeCallback layoutStateChangeCallback = new LayoutStateChangeCallback();
//  private ScreenType screenType;//0初始化为竖屏 1折叠屏幕


  @Override
  protected void onCreate(@Nullable Bundle savedInstanceState) {
    if (null != savedInstanceState) {
      AutoSizeCompat.autoConvertDensityBaseOnHeight(super.getResources(), 1334);
      AutoSizeCompat.autoConvertDensityBaseOnWidth(super.getResources(), 750);
      savedInstanceState.remove("android:support:request_fragment_who");
      savedInstanceState.remove("android:support:fragments");
      savedInstanceState.remove("android:support:request_indicies");
      savedInstanceState.remove("android:support:next_request_index");
      if (savedInstanceState != null) {
        mViewModel = new ViewModelProvider(this).get(getViewModelClass());
        navGraph = mViewModel.getNavGraph();
        navId = mViewModel.getNavId();

      }
      savedInstanceState = null;
    }


    setScreenOrientationPortrait();

    super.onCreate(savedInstanceState);


    mServiceConnection = new ServiceConnection() {
      @Override
      public void onServiceConnected(ComponentName componentName, IBinder iBinder) {
      }

      @Override
      public void onServiceDisconnected(ComponentName componentName) {
      }
    };
    hideStatusBar();
    if (getViewModelClass() != null) {
      mViewModel = new ViewModelProvider(this).get(getViewModelClass());
      mViewModel.context = this;
      try {
        mViewModel.mRegistry.handleLifecycleEvent(Lifecycle.Event.ON_CREATE);
      } catch (Exception e) {

      }
    }


    ViewDataBinding viewDataBinding = null;
    if (getLayoutId() != null) {
      viewDataBinding = DataBindingUtil.setContentView(this, getLayoutId());
      viewDataBinding.setLifecycleOwner(this);
    }


    if (navGraph != null) {
      navController = Navigation.findNavController(this, R.id.nav_host_fragment_content_main);
      navController.setGraph(navGraph);
      navController.navigate(navId);

    }
    initViews(viewDataBinding);
    initData(savedInstanceState);
  }

  private void setScreenOrientationPortrait(){
    if (Build.VERSION.SDK_INT == Build.VERSION_CODES.O && isTranslucentOrFloating()) {
      //解决Android8.0---Caused by java.lang.IllegalStateException: Only fullscreen opaque activities can request orientation
      fixOrientation();
    }else {
      setRequestedOrientation(SCREEN_ORIENTATION_PORTRAIT);
    }
  }

  private boolean isTranslucentOrFloating(){

    boolean isTranslucentOrFloating = false;

    try {

      int [] styleableRes = (int[]) Class.forName("com.android.internal.R$styleable").getField("Window").get(null);

      final TypedArray ta = obtainStyledAttributes(styleableRes);

      Method m = ActivityInfo.class.getMethod("isTranslucentOrFloating", TypedArray.class);

      m.setAccessible(true);

      isTranslucentOrFloating = (boolean)m.invoke(null, ta);

      m.setAccessible(false);

    } catch (Exception e) {

      e.printStackTrace();

    }

    return isTranslucentOrFloating;

  }

  @SuppressLint("WrongConstant")
  private boolean fixOrientation(){

    try {

      Field field = Activity.class.getDeclaredField("mActivityInfo");

      field.setAccessible(true);

      ActivityInfo o = (ActivityInfo)field.get(this);

      o.screenOrientation = -1;

      field.setAccessible(false);

      return true;

    } catch (Exception e) {

      e.printStackTrace();

    }

    return false;

  }


  public void setLanguage(Context context) {
    Resources resources = context.getResources();
    Configuration configuration = resources.getConfiguration();
    DisplayMetrics displayMetrics = resources.getDisplayMetrics();
    configuration.setLocale(Locale.ENGLISH);

    resources.updateConfiguration(configuration, displayMetrics);
  }

  private void initPageResouce() {
    if (!TextUtils.isEmpty(pageId)) {
      pageResouce = ((BaseApplication) this.getApplication()).getCurrentPageResouce();

      if (TextUtils.isEmpty(pageResouce)) {
        pageResouce = "1_" + pageId;
        ((BaseApplication) this.getApplication()).setCurrentPageResouce(pageResouce);
      } else {
        if (!pageResouce.endsWith(pageId)) {
          pageResouce = pageResouce + "_" + pageId;
          ((BaseApplication) this.getApplication()).setCurrentPageResouce(pageResouce);
        }
      }

    }
  }

  @Override
  public void onStart() {
    super.onStart();
    mViewModel.mRegistry.handleLifecycleEvent(Lifecycle.Event.ON_START);
    onRegister();
    initMessageService();
  }


  @Override
  protected void onResume() {
    super.onResume();

    initPageResouce();
    if (mouduleId != null) {
      if (expandMap.isEmpty()){
        sendExposure(this, mouduleId, pageId, pageResouce);
      }else {
        sendExposure(this, mouduleId, pageId, pageResouce,expandMap);
        expandMap.clear();
      }
    }
    mViewModel.mRegistry.handleLifecycleEvent(Lifecycle.Event.ON_RESUME);
  }

  @Override
  public void onStop() {
    super.onStop();
    mViewModel.mRegistry.handleLifecycleEvent(Lifecycle.Event.ON_STOP);
    onUnRegister();
    try {
      if (mServiceConnection != null) {
        ServiceUtils.unbindService(mServiceConnection);
      }
      mkeyBackListener = null;
    }catch (Exception e){
      LogUtils.i("BaseActivity onStop unbindService is Error ---"+e.getMessage()+"");
    }
  }


  @Override
  protected void onDestroy() {
    super.onDestroy();
    mServiceConnection = null;
    mViewModel.mRegistry.handleLifecycleEvent(Lifecycle.Event.ON_DESTROY);
  }

  @Override
  protected void onPause() {
    super.onPause();
    if (mouduleId != null) {
      sendStayDuration();
    }

    // delPageResouce();
  }

  private void delPageResouce() {
    String pageResourceDel = pageResouce.replace("_" + pageId, "");
    ((BaseApplication) this.getApplication()).setCurrentPageResouce(pageResourceDel);
  }

  protected abstract void onUnRegister();

  protected abstract void onRegister();

  protected abstract Integer getLayoutId();

  protected abstract void initViews(ViewDataBinding viewDataBinding);

  protected abstract void initData(Bundle savedInstanceState);

  protected abstract Class<? extends M> getViewModelClass();


  public void showToast(String tip) {
    QFToast.show(this.getApplicationContext(), tip, 1);
  }

  public void showLoading(int resId) {
    ProgressHelper.showLoadViewInPage(this, this.getString(resId));
  }

  private void hideStatusBar() {
//        WindowManager.LayoutParams attrs = getWindow().getAttributes();
//        attrs.flags |= WindowManager.LayoutParams.FLAG_FULLSCREEN;
//        getWindow().setAttributes(attrs);
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
      Window window = getWindow();

      window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS | WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION);

      window.getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION | View.SYSTEM_UI_FLAG_LAYOUT_STABLE);

      window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
//给状态栏设置颜色。我设置透明。
      int statusBarColor;
      window.setStatusBarColor(Color.TRANSPARENT);
      window.setNavigationBarColor(Color.TRANSPARENT);
      getWindow().getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR);


    }


  }

  public void setKeyBackListener(BaseEnhanceFragment.OnkeyBackListener onkeyBackListener) {
    mkeyBackListener = onkeyBackListener;
  }


  @Override
  public Resources getResources() {
    float designWidth = AutoSizeConfig.getInstance().getScreenWidth();
    float designHeight = AutoSizeConfig.getInstance().getScreenHeight();

    boolean isBaseOnWidth;
    int designData = 750;
    isBaseOnWidth = !(designWidth > designHeight);

    if (Looper.myLooper() == Looper.getMainLooper()) {


      //暂先适配
      if (designHeight/designWidth < 1.31){

        AutoSizeCompat.autoConvertDensity(super.getResources(), designWidth, false);

      }else {

        AutoSizeCompat.autoConvertDensity(super.getResources(), designData, isBaseOnWidth);
      }

    }

    return super.getResources();
  }


  public static boolean isFoldDisplay(Context context) {
    final String KEY = "config_lidControlsDisplayFold";
    int id = context.getResources().getIdentifier(KEY, "bool", "android");
    if (id > 0) {
      return context.getResources().getBoolean(id);
    }
    return false;
  }



  private boolean isTableTopPosture(FoldingFeature foldFeature) {
    return (foldFeature != null) &&
      (foldFeature.getState() == FoldingFeature.State.HALF_OPENED) &&
      (foldFeature.getOrientation() == FoldingFeature.Orientation.HORIZONTAL);
  }

  private boolean isBookPosture(FoldingFeature foldFeature) {
    return (foldFeature != null) &&
      (foldFeature.getState() == FoldingFeature.State.HALF_OPENED) &&
      (foldFeature.getOrientation() == FoldingFeature.Orientation.VERTICAL);
  }

  private boolean isSeparating(FoldingFeature foldFeature) {
    return (foldFeature != null) &&
      (foldFeature.getState() == FoldingFeature.State.FLAT) &&
      (foldFeature.isSeparating() == true);
  }

  private boolean isNormalPosture() {
    return true;
  }



  public void setTitleName(String string) {

  }

  @Override
  protected void onSaveInstanceState(Bundle outState) {
    super.onSaveInstanceState(outState);
    try {
      navController = Navigation.findNavController(this, R.id.nav_host_fragment_content_main);
      if (navController != null) {
        NavGraph navGraph = navController.getGraph();
        int id = navController.getCurrentDestination().getId();
        mViewModel.setNav(id);
        mViewModel.setnavGraph(navGraph);
      }
    } catch (Exception e) {

    }


  }

  @Override
  public void onBackPressed() {
    super.onBackPressed();
    sendBackTrace();
  }

  private void initMessageService() {
    Intent messageIntent = new Intent().setClassName(getBaseContext(), "com.chervon.moudleMessageCenter.service.MessageCenterService");
    try {
      startService(messageIntent);
      //启动消息服务
      ServiceUtils.bindService("com.chervon.moudleMessageCenter.service.MessageCenterService", mServiceConnection, Context.BIND_AUTO_CREATE);
    } catch (Exception e) {
      LogUtils.i("initMessageSevice is Error--->"+e.getMessage()+"");
    }

  }

  private void sendStayDuration() {
    if (mouduleId != null) {
      sendDurationTrace(this, mouduleId, pageId, pageResouce, "", stayEleId, System.currentTimeMillis() - enterTime);
    }

  }

  protected void sendBaseTraceClick(String eleid) {
    sendClickTrace(this, mouduleId, pageId, pageResouce, eleid, "1");
  }

  protected void sendBackTrace() {
    sendClickTrace(this, mouduleId, pageId, pageResouce, "1", "1");
  }


  public void setPageSource(String str) {
    this.pageResouce = str;
  }

}
