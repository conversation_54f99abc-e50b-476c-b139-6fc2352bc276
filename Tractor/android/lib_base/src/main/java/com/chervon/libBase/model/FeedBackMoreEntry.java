package com.chervon.libBase.model;

import java.io.Serializable;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleConfigNet.ui.state
 * @ClassName: ScanNearByDevicesUIState
 * @Description: 类描述
 * @Author: name
 * @CreateDate: 2022/5/10 下午6:15
 * @UpdateUser: 更新者
 * @UpdateDate: 2022/5/10 下午6:15
 * @UpdateRemark: 更新说明
 * @Version: 1.0
 */
public class FeedBackMoreEntry implements Serializable {
private  String iconUrl;
  private  String  questionTitle;
  private  String  replyPictures;
  private  String  category1;
  private  String  category2;

  public FeedBackMoreEntry(String questionTitle, String category1, String category2) {
    this.questionTitle = questionTitle;
    this.category1 = category1;
    this.category2 = category2;
  }

  public String getCategory2() {
    return category2;
  }

  public void setCategory2(String category2) {
    this.category2 = category2;
  }

  public String getCategory1() {
    return category1;
  }

  public void setCategory1(String category1) {
    this.category1 = category1;
  }

  public String getIconUrl() {
    return iconUrl;
  }

  public void setIconUrl(String iconUrl) {
    this.iconUrl = iconUrl;
  }

  public String getQuestionTitle() {
    return questionTitle;
  }

  public void setQuestionTitle(String questionTitle) {
    this.questionTitle = questionTitle;
  }

  public String getReplyPictures() {
    return replyPictures;
  }

  public void setReplyPictures(String replyPictures) {
    this.replyPictures = replyPictures;
  }
}
