package com.chervon.libBase;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.databinding.DataBindingUtil;
import androidx.databinding.ViewDataBinding;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.LogUtils;

import java.util.List;

/**
 * @ProjectName: app
 * @Package: com.chervon.libBase
 * @ClassName: BaseDataBindingAdapter
 * @Description: Adapter的databinding能力封装，可以更简单的创建Adapter
 * @Author: LangMeng
 * @CreateDate: 2022/6/20 14:49
 * @UpdateUser: LangMeng
 * @UpdateDate: 2022/6/20 14:49
 * @UpdateRemark: new class
 * @Version: 1.0
 */
public class BaseDataBindingAdapter<T extends List<Object>> extends RecyclerView.Adapter<BaseDataBindingAdapter.ViewHolder>{

    private Context context;
    private int layoutId;
    private T list;
    private int variableId;
    private itemCallBack callback = null;

    public BaseDataBindingAdapter(Context context, int layoutId, T list, int variableId, itemCallBack callback) {
        this.context = context;
        this.layoutId = layoutId;
        this.list = list;
        this.variableId = variableId;
        this.callback = callback;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        ViewDataBinding binding = DataBindingUtil.inflate(LayoutInflater.from(context), layoutId, parent, false);

        ViewHolder holder = new ViewHolder(binding.getRoot().getRootView());

        holder.viewDataBinding = binding;

        return holder;
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {

        callback.onBindViewHolder(holder, position, BaseDataBindingAdapter.this);

        holder.viewDataBinding.setVariable(variableId, list.get(position));

        holder.viewDataBinding.executePendingBindings();
    }

    @Override
    public int getItemCount() {
        return list.size();
    }

    public static interface itemCallBack {

        void onBindViewHolder(ViewHolder holder, int position, RecyclerView.Adapter<ViewHolder> adapter);

    }

    public static class ViewHolder extends RecyclerView.ViewHolder {

        ViewDataBinding viewDataBinding;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
        }
    }
}
