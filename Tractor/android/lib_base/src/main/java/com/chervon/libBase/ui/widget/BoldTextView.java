package com.chervon.libBase.ui.widget;

import android.content.Context;
import android.graphics.Typeface;
import android.util.AttributeSet;
import android.widget.TextView;

import androidx.annotation.Nullable;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleOobe.ui.widget.supertooltip
 * @ClassName: BoldTextView
 * @Description: 类描述
 * @Author: name
 * @CreateDate: 2022/6/18 下午6:04
 * @UpdateUser: 更新者
 * @UpdateDate: 2022/6/18 下午6:04
 * @UpdateRemark: 更新说明
 * @Version: 1.0
 */
public class BoldTextView extends androidx.appcompat.widget.AppCompatTextView {
    public BoldTextView(Context context) {
        this(context,null);
    }

    public BoldTextView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs,0);
    }

    public BoldTextView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        getPaint().setFakeBoldText(true);
    }

    @Override
    public void setText(CharSequence text, BufferType type) {
        super.setText(text, type);
         getPaint().setFakeBoldText(true);
       // getPaint().setTypeface(Typeface.DEFAULT_BOLD);
    }
}
