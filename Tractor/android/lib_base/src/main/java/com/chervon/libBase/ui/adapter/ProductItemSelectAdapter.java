package com.chervon.libBase.ui.adapter;

import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatCheckBox;
import androidx.recyclerview.widget.RecyclerView;

import com.chervon.libBase.R;

import java.util.ArrayList;


public class ProductItemSelectAdapter extends RecyclerView.Adapter<ProductItemSelectAdapter.ViewHolder> {
  private static final int TYPE_OTHER = 1;
  public static final int TYPE_ITEM = 2;
  private Boolean isCheckBox = true;
  public int otherPosition = 1;
  private String[] dataList;
  private boolean[] itemSelected;
  private OnItemClickListener onItemClickListener;
  private int selectType;
  private int selectCount;
  private String otherStr;
  private boolean otherSelected;

  public ProductItemSelectAdapter(String[] dataList) {
    this.dataList = dataList;
    if (dataList != null) {
      itemSelected = new boolean[dataList.length];
    }

  }

  public ProductItemSelectAdapter(String[] dataList, Boolean isCheckBox) {
    this.dataList = dataList;
    this.isCheckBox = isCheckBox;
    if (dataList != null) {
      itemSelected = new boolean[dataList.length];
    }
  }

  public void setDefaultCheckItem(Integer[] checkItem,String questionFourOther) {
    for (int i = 0; i < itemSelected.length; i++) {
      for (Integer j : checkItem) {
        if (i==j){
          itemSelected[i] = true;
        }
      }
    }
    if (!TextUtils.isEmpty(questionFourOther)){
      otherStr = questionFourOther;
    }

  }


  @NonNull
  @Override
  public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
    if (viewType == TYPE_OTHER) {
      View itemView = LayoutInflater.from(parent.getContext()).inflate(R.layout.base_recycle_question_other_item, parent, false);
      return new OtherViewHolder(itemView, isCheckBox);
    } else {
      View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.base_rv_cb_item, parent, false);
      return new ViewHolder(view, isCheckBox);

    }
  }

  @Override
  public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
    int viewType = getItemViewType(position);
    if (viewType == TYPE_OTHER) {
      ((OtherViewHolder) holder).iv.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
        @Override
        public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
          if (isChecked) {
            ((OtherViewHolder) holder).etOther.setVisibility(View.VISIBLE);
            otherSelected = true;


          } else {
            ((OtherViewHolder) holder).etOther.setVisibility(View.GONE);
            otherSelected = false;
          }

          if (onItemClickListener != null) {
            onItemClickListener.onItemCheckChange(buttonView, isChecked);
          }


          ((OtherViewHolder) holder).etOther.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
              otherStr = s.toString();


              if (onItemClickListener != null) {
                onItemClickListener.onItemCheckChange(buttonView, isChecked);
              }

            }
          });


          ((OtherViewHolder) holder).etOther.setOnEditorActionListener(new TextView.OnEditorActionListener() {
            @Override
            public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
              //可以根据需求获取下一个焦点还是上一个
//                      View nextView = v.focusSearch(View.FOCUS_DOWN);
//                      if (nextView != null) {
//                        nextView.requestFocus(View.FOCUS_DOWN);
//                      }
              //这里一定要返回true
              return true;
            }
          });

        }
      });
    }


    final String content = dataList[position];
    final int currentPostion = position;
    holder.tv.setText(content);
    if (itemSelected[currentPostion]) {
      holder.iv.setChecked(true);
    } else {
      holder.iv.setChecked(false);
    }

    holder.itemView.setOnClickListener(new View.OnClickListener() {
      @Override
      public void onClick(View view) {
        if (selectType == 0) {
          for (int i = 0; i < itemSelected.length; i++) {
            itemSelected[i] = false;
          }
          itemSelected[currentPostion] = true;
          view.setTag(currentPostion);
          notifyDataSetChanged();

        } else if (selectType == 1) {
          itemSelected[currentPostion] = !holder.iv.isChecked();
          view.setTag(currentPostion);
          notifyDataSetChanged();
        } else if (selectType == 2) {
          if (selectCount < 2) {
            if (!holder.iv.isChecked()) {
              selectCount++;
            } else {
              selectCount--;
            }
            itemSelected[currentPostion] = !holder.iv.isChecked();
            view.setTag(currentPostion);
            notifyDataSetChanged();
          } else {
            if (!holder.iv.isChecked()) {
            } else {
              selectCount--;
              itemSelected[currentPostion] = !holder.iv.isChecked();
              view.setTag(currentPostion);
              notifyDataSetChanged();
            }
          }

        }

        if (onItemClickListener != null) {
          onItemClickListener.onClick(view, content);
        }
      }

    });


  }

  @Override
  public int getItemCount() {
    return dataList == null ? 0 : dataList.length;
  }

  static class ViewHolder extends RecyclerView.ViewHolder {


    protected TextView tv;
    protected AppCompatCheckBox iv;

    ViewHolder(@NonNull View itemView) {
      super(itemView);
      tv = itemView.findViewById(R.id.tv);
      iv = itemView.findViewById(R.id.ivItemSelected);
    }

    ViewHolder(@NonNull View itemView, boolean isCheckBox) {
      super(itemView);
      tv = itemView.findViewById(R.id.tv);
      if (isCheckBox) {
        iv = itemView.findViewById(R.id.ivItemSelected);
        iv.setVisibility(View.VISIBLE);
        itemView.findViewById(R.id.radioItemSelected).setVisibility(View.GONE);
      } else {
        iv = itemView.findViewById(R.id.radioItemSelected);
        iv.setVisibility(View.VISIBLE);
        itemView.findViewById(R.id.ivItemSelected).setVisibility(View.GONE);
      }


    }
  }


  @Override
  public int getItemViewType(int position) {
    if (position == getItemCount() - otherPosition) {
      return TYPE_OTHER;
    } else {
      return TYPE_ITEM;
    }
  }

  public OnItemClickListener getOnItemClickListener() {
    return onItemClickListener;
  }

  public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
    this.onItemClickListener = onItemClickListener;
  }

  public void setSelectType(int selectType) {
    this.selectType = selectType;
  }

  public interface OnItemClickListener {
    void onClick(View var1, String itemName);

    void onItemCheckChange(View var1, boolean checked);
  }

  class OtherViewHolder extends ViewHolder {

    EditText etOther;

    public OtherViewHolder(@NonNull View itemView, boolean isCheckBox) {
      super(itemView, isCheckBox);
      etOther = itemView.findViewById(R.id.etOther);
      if (!TextUtils.isEmpty(otherStr)){
        etOther.setText(otherStr);
      }

    }

  }


  public Integer[] getSelectedData() {
    ArrayList<Integer> list = new ArrayList<Integer>();
    for (int i = 0; itemSelected != null && i < itemSelected.length; i++) {
      if (itemSelected[i]) {
        list.add(i);
      }
    }
    if (list.size() > 0) {
      Integer[] strs = new Integer[list.size()];
      list.toArray(strs);
      return strs;
    }
    return null;
  }


  public String getOtherEdit() {
    if (otherStr != null) {
      return otherStr.toString();
    }
    return "";
  }

  public int getOtherPosition() {
    return otherPosition;
  }

  public void setOtherPosition(int otherPosition) {
    this.otherPosition = otherPosition;
  }

  public boolean isOtherSelected() {
    return otherSelected;
  }

  public void setOtherSelected(boolean otherSelected) {
    this.otherSelected = otherSelected;
  }
}
