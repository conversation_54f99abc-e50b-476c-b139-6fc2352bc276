package com.chervon.libBase.model;

import java.util.List;

/**
 * @ProjectName: app
 * @Package: com.chervon.libNetwork.iot.data
 * @ClassName: OTACheckBean
 * @Description: 获取可用升级内容数据模型
 * @Author: langmeng
 * @CreateDate: 2022/9/6 17:12
 * @UpdateUser: langmeng
 * @UpdateDate: 2022/9/6 17:12
 * @UpdateRemark: new class
 * @Version: 1.0
 */
public class OTACheckBean {

  private List<String> jobIds;
  private int packageCount;
  private String remark;
  private String technologyVersion;
  private String customVersion;
  private int upgradeMode = 0;
  private List<PackagesDTO> packages;
  //是否有升级新升级任务--新增
  private boolean hasOtaJob;

  public boolean isHasOtaJob() {
    return hasOtaJob;
  }

  public void setHasOtaJob(boolean hasOtaJob) {
    this.hasOtaJob = hasOtaJob;
  }

  public List<String> getJobIds() {
    return jobIds;
  }

  public void setJobIds(List<String> jobIds) {
    this.jobIds = jobIds;
  }

  public int getPackageCount() {
    return packageCount;
  }

  public void setPackageCount(int packageCount) {
    this.packageCount = packageCount;
  }

  public String getRemark() {
    return remark;
  }

  public void setRemark(String remark) {
    this.remark = remark;
  }

  public String getTechnologyVersion() {
    return technologyVersion;
  }

  public void setTechnologyVersion(String technologyVersion) {
    this.technologyVersion = technologyVersion;
  }

  public String getCustomVersion() {
    return customVersion;
  }

  public void setCustomVersion(String customVersion) {
    this.customVersion = customVersion;
  }

  public int getUpgradeMode() {
    return upgradeMode;
  }

  public void setUpgradeMode(int upgradeMode) {
    this.upgradeMode = upgradeMode;
  }

  public List<PackagesDTO> getPackages() {
    return packages;
  }

  public void setPackages(List<PackagesDTO> packages) {
    this.packages = packages;
  }

  public static class PackagesDTO {
    private String componentType;
    private String packageId;
    private String packageName;
    private String packageVersion;
    private String componentNo;
    private String componentName;
    private String compatibleVersion;
    private int size;
    private String packageUrl;
    private String packageKey;
    private String hash;
    //自定义包序号，用于蓝牙传输排序
    private int packageSn;
    //自定义偏移指针，用于分片传输
    private int offset;
    //自定义包文件流
    private byte[] packageFile;

    public String getPackageId() {
      return packageId;
    }

    public void setPackageId(String packageId) {
      this.packageId = packageId;
    }

    public String getPackageName() {
      return packageName;
    }

    public void setPackageName(String packageName) {
      this.packageName = packageName;
    }

    public String getPackageVersion() {
      return packageVersion;
    }

    public void setPackageVersion(String packageVersion) {
      this.packageVersion = packageVersion;
    }

    public String getComponentNo() {
      return componentNo;
    }

    public void setComponentNo(String componentNo) {
      this.componentNo = componentNo;
    }

    public String getComponentName() {
      return componentName;
    }

    public void setComponentName(String componentName) {
      this.componentName = componentName;
    }

    public String getCompatibleVersion() {
      return compatibleVersion;
    }

    public void setCompatibleVersion(String compatibleVersion) {
      this.compatibleVersion = compatibleVersion;
    }

    public int getSize() {
      return size;
    }

    public void setSize(int size) {
      this.size = size;
    }

    public String getPackageUrl() {
      return packageUrl;
    }

    public void setPackageUrl(String packageUrl) {
      this.packageUrl = packageUrl;
    }

    public String getPackageKey() {
      return packageKey;
    }

    public void setPackageKey(String packageKey) {
      this.packageKey = packageKey;
    }

    public String getHash() {
      return hash;
    }

    public void setHash(String hash) {
      this.hash = hash;
    }

    public int getPackageSn() {
      return packageSn;
    }

    public void setPackageSn(int packageSn) {
      this.packageSn = packageSn;
    }

    public int getOffset() {
      return offset;
    }

    public void setOffset(int offset) {
      this.offset = offset;
    }

    public byte[] getPackageFile() {
      return packageFile;
    }

    public void setPackageFile(byte[] packageFile) {
      this.packageFile = packageFile;
    }

    public String getComponentType() {
      return componentType;
    }

    public void setComponentType(String componentType) {
      this.componentType = componentType;
    }
  }
}
