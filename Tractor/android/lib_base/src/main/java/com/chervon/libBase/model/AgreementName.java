package com.chervon.libBase.model;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleOobe.data.model
 * @ClassName: AgreementName
 * @Description: 类描述
 * @Author: name
 * @CreateDate: 2022/9/1 下午7:48
 * @UpdateUser: 更新者
 * @UpdateDate: 2022/9/1 下午7:48
 * @UpdateRemark: 更新说明
 * @Version: 1.0
 */
public class AgreementName {

  private String   langId;
    private String  message;

    public String getLangId() {
        return langId;
    }

    public void setLangId(String langId) {
        this.langId = langId;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
