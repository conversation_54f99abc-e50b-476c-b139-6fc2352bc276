package com.chervon.libBase.ui.adapter;

import android.graphics.Rect;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

public class GridSpaceItemDecoration extends RecyclerView.ItemDecoration {

    private final String TAG = "GridSpaceItemDecoration";
    //横条目数量
    private int mSpanCount;
    //行间距
    private int mRowSpacing;
    // 列间距
    private int mColumnSpacing;

    /**
     * @param spanCount     列数
     * @param rowSpacing    行间距
     * @param columnSpacing 列间距
     */
    public GridSpaceItemDecoration(int spanCount, int rowSpacing, int columnSpacing) {
        this.mSpanCount = spanCount;
        this.mRowSpacing = rowSpacing;
        this.mColumnSpacing = columnSpacing;
    }

    @Override
    public void getItemOffsets(@NonNull Rect outRect, @NonNull View view, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
        // 获取view 在adapter中的位置。
        int position = parent.getChildAdapterPosition(view);
        // view 所在的列
        int column = position % mSpanCount;
// column * (列间距 * (1f / 列数))
        outRect.left = column * mColumnSpacing / mSpanCount;
        // 列间距 - (column + 1) * (列间距 * (1f /列数))
        outRect.right = mColumnSpacing - (column + 1) * mColumnSpacing / mSpanCount;


        // 如果position > 行数，说明不是在第一行，则不指定行高，其他行的上间距为 top=mRowSpacing
        if (position >= mSpanCount) {
            // item top
            outRect.top = mRowSpacing;
        }
    }
}

