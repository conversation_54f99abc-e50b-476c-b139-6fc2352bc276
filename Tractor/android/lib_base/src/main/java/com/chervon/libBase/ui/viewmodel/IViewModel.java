package com.chervon.libBase.ui.viewmodel;

import androidx.lifecycle.LifecycleOwner;

import org.jetbrains.annotations.NotNull;

/**
 * @ProjectName: app
 * @Package: com.chervon.libBase.ui.viewmodel
 * @ClassName: IViewModel
 * @Description: Lifecycle interface of ViewModeL
 * @Author: wangheng
 * @CreateDate: 2022/4/19 下午7:02
 * @UpdateUser: wangheng
 * @UpdateDate: 2022/4/19 下午7:02
 * @UpdateRemark: new
 * @Version: 1.0
 */
interface IViewModel extends LifecycleOwner {

    void onCreate(@NotNull LifecycleOwner lifecycleOwner);

    void onStart(@NotNull LifecycleOwner lifecycleOwner);

    void onResume(@NotNull LifecycleOwner lifecycleOwner);

    void onPause(@NotNull LifecycleOwner lifecycleOwner);

    void onStop(@NotNull LifecycleOwner lifecycleOwner);

    void onDestroy(@NotNull LifecycleOwner lifecycleOwner);

}