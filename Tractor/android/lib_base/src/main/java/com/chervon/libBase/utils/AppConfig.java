package com.chervon.libBase.utils;

import com.chervon.libBase.BuildConfig;
import com.chervon.libBase.R;

/**
 * @Author: 184862
 * @CreateDate: 2025/1/9
 * @UpdateDate: 2025/1/9
 * App系统配置表公共整体控制控制显示模块
 */
public class AppConfig {

    /**
     * 获取图片Icon用来显示弹窗或者其他
     * @return
     */
    public static int getAppIconRound2(){
       if (BuildConfig.EVN.equalsIgnoreCase(Utils.NA_TAG)){
          return R.mipmap.icon_logo_round_na_min;
       }else if (BuildConfig.EVN.equalsIgnoreCase(Utils.EVN_EU)){
           return  R.mipmap.icon_logo_round2_eu;
       }else {
           return R.mipmap.icon_logo_round2_na;
       }
    }

    /**
     * NA&ANZ 公共属性
     * @return
     */
    public static boolean featureWithNaAndAnz() {
        return (BuildConfig.EVN.equalsIgnoreCase(Utils.EVN_NA) || BuildConfig.EVN.equalsIgnoreCase(Utils.EVN_ANZ));
    }

    /**
     *  ANZ属性
     * @return
     */
    public static boolean featureIsANZ(){
        return BuildConfig.EVN.equalsIgnoreCase(Utils.EVN_ANZ);
    }

    /**
     *  NA属性
     * @return
     */
    public static boolean featureIsNA(){
        return BuildConfig.EVN.equalsIgnoreCase(Utils.EVN_NA);
    }

    /**
     *  EU属性
     * @return
     */
    public static boolean featureIsEU(){
        return BuildConfig.EVN.equalsIgnoreCase(Utils.EVN_EU);
    }


    public static String getAppFeature(){
        return BuildConfig.EVN;
    }
}
