//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fern<PERSON>lower decompiler)
//

package com.chervon.libBase.ui.widget;

import android.app.Activity;
import android.app.Dialog;
import android.app.ProgressDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.DialogInterface.OnDismissListener;
import android.os.Looper;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager.LayoutParams;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.annotation.StringRes;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.blankj.utilcode.util.SizeUtils;
import com.chervon.libBase.R;

/**
 * @ProjectName: app
 * @Package: com.chervon.libBase.ui.widget
 * @ClassName: ProgressHelper
 * @Description: Progress dialog
 * @Author: wangheng
 * @CreateDate: 2022/4/19 下午7:02
 * @UpdateUser: wangheng
 * @UpdateDate: 2022/4/19 下午7:02
 * @UpdateRemark: new
 * @Version: 1.0
 */
public class ProgressHelper {

    private static Dialog mProgressLoading;

    private ProgressHelper() {
    }

    public static boolean isMainThread() {
        return Looper.getMainLooper().getThread() == Thread.currentThread();
    }


    public static synchronized void showLoadViewInPage(final Context context, final String text) {
        if (!(context instanceof Activity) || !((Activity) context).isFinishing()) {
            if (isMainThread()) {
                inPageLoadingShow(context, text);
            } else if (context instanceof Activity) {
                ((Activity) context).runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        ProgressHelper.inPageLoadingShow(context, text);
                    }
                });
            }

        }
    }


    public static synchronized void showProgressView(final Context context, final int progress) {
        if (!(context instanceof Activity) || !((Activity) context).isFinishing()) {
            if (isMainThread()) {
                progressViewShow(context, progress);
            } else if (context instanceof Activity) {
                ((Activity) context).runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        ProgressHelper.progressViewShow(context, progress);
                    }
                });
            }

        }
    }

    public static synchronized void showProgressView(final Context context, final int progress, final boolean touchCancel) {
        if (!(context instanceof Activity) || !((Activity) context).isFinishing()) {
            if (isMainThread()) {
                progressViewShow(context, progress);
            } else if (context instanceof Activity) {
                ((Activity) context).runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        ProgressHelper.progressViewShow(context, progress, touchCancel);
                    }
                });
            }

        }
    }

    private static void inPageLoadingShow(Context context, String text) {

    }


    public static void progressViewShow(Context context, int progress) {
        progressViewShow(context, progress, false);

    }


    public static Dialog progressViewRnShow(Activity context, int progress, boolean touchCancel) {


        Dialog mProgressLoadingRn = new Dialog(context, R.style.MyDialog);
        View view = LayoutInflater.from(context).inflate(R.layout.base_progress_loading, null);
        mProgressLoadingRn.setContentView(view, new ConstraintLayout.LayoutParams(300, 300));
        mProgressLoadingRn.setCanceledOnTouchOutside(touchCancel);
        mProgressLoadingRn.setOnDismissListener(new OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialog) {

            }
        });

        if (!mProgressLoadingRn.isShowing() && !context.isDestroyed()) {
            mProgressLoadingRn.show();

        }
        return mProgressLoadingRn;

    }


    public static void progressViewShow(Context context, int progress, boolean touchCancel) {

        if (mProgressLoading == null || mProgressLoading.getOwnerActivity() == null || !(context.equals(mProgressLoading.getOwnerActivity()))) {
            if (context == null) {
                return;
            }
            mProgressLoading = new Dialog(context, R.style.MyDialog);
            mProgressLoading.setOwnerActivity((Activity) context);
            View view = LayoutInflater.from(context).inflate(R.layout.base_progress_loading, null);
            mProgressLoading.setContentView(view, new ConstraintLayout.LayoutParams(300, 300));
            mProgressLoading.setCanceledOnTouchOutside(touchCancel);
            mProgressLoading.setOnDismissListener(new OnDismissListener() {
                @Override
                public void onDismiss(DialogInterface dialog) {
                    mProgressLoading = null;
                }
            });
        }

        if (!mProgressLoading.isShowing()) {
            mProgressLoading.show();

        }

    }


    public static synchronized void hideProgressView(Context context) {

        if (null == mProgressLoading) {
            return;
        }
        if (null == context) {
            return;
        }

        if (isMainThread() && !(context instanceof Activity)) {
            if (mProgressLoading != null) {
                if (!((Activity) context).isFinishing() && mProgressLoading != null && mProgressLoading.isShowing()) {
                    mProgressLoading.dismiss();
                }
            }

        } else if (context instanceof Activity) {
            if (!((Activity) context).isFinishing()) {
                ((Activity) context).runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        if (!((Activity) context).isFinishing() && mProgressLoading != null && mProgressLoading.isShowing()) {
                            try {
                                mProgressLoading.dismiss();
                            } catch (Exception e) {

                            }

                        }
                    }
                });
            }

        }

    }


}
