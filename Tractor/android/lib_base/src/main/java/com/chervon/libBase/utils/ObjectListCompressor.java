package com.chervon.libBase.utils;



import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;

/**
 * @Author: 184862
 * @CreateDate: 2024/5/11
 * @UpdateDate: 2024/5/11
 */

public class ObjectListCompressor {


  /**
   * 描述：GZIP压缩数据
   * @date 2024/4/26 10:54
   * @param data 需压缩数据
   * @return byte[]
   **/
  public static byte[] compress(String data) throws IOException {
    ByteArrayOutputStream obj = new ByteArrayOutputStream();
    GZIPOutputStream gzip = new GZIPOutputStream(obj);
    gzip.write(data.getBytes(StandardCharsets.UTF_8));
    gzip.close();
    return obj.toByteArray();
  }

  /**
   * 描述：GZIP解压数据
   * @date 2024/4/26 14:06
   * @param compressed 待解压数据
   * @return java.lang.String
   **/
  public static String decompress(byte[] compressed) throws IOException {
    ByteArrayInputStream bai = new ByteArrayInputStream(compressed);
    GZIPInputStream gzipInputStream = new GZIPInputStream(bai);
    ByteArrayOutputStream bao = new ByteArrayOutputStream();
    byte[] buffer = new byte[1024];
    int len;
    while ((len = gzipInputStream.read(buffer)) > 0) {
      bao.write(buffer, 0, len);
    }
    gzipInputStream.close();
    return new String(bao.toByteArray(), StandardCharsets.UTF_8);
  }


}
