package com.chervon.libBase.utils;

import static com.chervon.libBase.utils.Utils.sendDurationTrace;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.widget.Toast;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.chervon.libBase.BaseApplication;
import com.chervon.libBase.R;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.trace.bean.EventBean;

import java.util.HashMap;
import java.util.Map;

/**
 * @ProjectName: app
 * @Package: com.chervon.libBase.utils
 * @ClassName: AppExitHelper
 * @Description: 类描述
 * @Author: name
 * @CreateDate: 2022/8/19 下午2:04
 * @UpdateUser: 更新者
 * @UpdateDate: 2022/8/19 下午2:04
 * @UpdateRemark: 更新说明
 * @Version: 1.0
 */
public class AppExitHelper {
    private long exitTime = 0;
    private VideoPositionManager videoPositionManager;

    public void appExit(Activity activity) {
        if ((System.currentTimeMillis() - exitTime) > 2000) {
            ToastUtils.showLong(LanguageStrings.getAppCommomLogOut(), Toast.LENGTH_SHORT);
            exitTime = System.currentTimeMillis();
        } else {
          sendAppUsageDuration(activity);
          clearVideoPosition(activity);
          Intent i = new Intent(Intent.ACTION_MAIN);
          i.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
          i.addCategory(Intent.CATEGORY_HOME);
          ActivityUtils.startActivity(i,R.anim.zoom_out,R.anim.zoom_out);

        }
    }

    private void clearVideoPosition(Context context){
        videoPositionManager = new VideoPositionManager(context);
        if (videoPositionManager != null) {
            videoPositionManager.clearAllPositions();
        }
    }

  private void sendAppUsageDuration(Context context) {
   Long  appStartTime= ((BaseApplication)( (Activity)context).getApplication()).getAppStartTime();
    sendDurationTrace(context,"1","1","1","0","2",(System.currentTimeMillis()-appStartTime));
    ((BaseApplication)( (Activity)context).getApplication()).setCurrentPageResouce("1");
  }
}
