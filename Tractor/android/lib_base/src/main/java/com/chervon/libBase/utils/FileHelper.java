package com.chervon.libBase.utils;

/**
 * @desc 测试环境日志输出
 * @Author: 184862
 * @CreateDate: 2024/7/8
 * @UpdateDate: 2024/7/8
 */

import android.content.Context;
import android.os.Environment;
import android.text.TextUtils;
import android.util.Log;

import com.blankj.utilcode.util.LogUtils;
import com.chervon.libBase.BuildConfig;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;

public class FileHelper {

  private static final String TAG = "FileHelper";
  private static final String DEBUG_TAG = "debug";


  public static void appendToFile(Context context,String textToAppend) {
    if (!BuildConfig.BUILD_TYPE.equals(DEBUG_TAG)){
      return;
    }
    String filename = "wifi_device_upgrade.txt";
    FileOutputStream fos = null;

    try {

      File privateDir = context.getFilesDir();

      File file = new File(privateDir, filename);

      fos = new FileOutputStream(file, true);
      fos.write(textToAppend.getBytes());
      fos.write("\n".getBytes());
      LogUtils.d(TAG, "Text has been appended to file " + filename);
    } catch (IOException e) {
      LogUtils.e(TAG, "Error appending text to file " + filename, e);
    } finally {
      // 关闭文件输出流
      if (fos != null) {
        try {
          fos.close();
        } catch (IOException e) {
          LogUtils.e(TAG, "Error closing FileOutputStream", e);
        }
      }
    }
  }


  // 检查外部存储是否可写
  private static boolean isExternalStorageWritable() {
    String state = Environment.getExternalStorageState();
    return Environment.MEDIA_MOUNTED.equals(state);
  }


}

