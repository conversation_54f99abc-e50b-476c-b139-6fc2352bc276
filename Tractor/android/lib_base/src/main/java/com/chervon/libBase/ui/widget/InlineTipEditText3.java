package com.chervon.libBase.ui.widget;


import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.drawable.Drawable;
import android.text.Editable;
import android.text.InputFilter;
import android.text.InputType;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.databinding.BindingAdapter;
import androidx.databinding.InverseBindingAdapter;

import com.chervon.libBase.R;
import com.chervon.libBase.utils.UiHelper;

import me.jessyan.autosize.AutoSizeCompat;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleOobe.ui.widget.inlineTipEditText3
 * @ClassName: InlineTipEditText
 * @Description: the InlineTip   of EditText
 * @Author: wuxd
 * @CreateDate: 2024/6/21
 * @UpdateUser:
 * @UpdateDate:
 * @UpdateRemark:
 * @Version:
 */
public class InlineTipEditText3 extends ConstraintLayout {
  private final ViewGroup editLayout;

  private EditText mEditText;
  private Drawable etShapeNormal;
  private Drawable etShapeFocus;
  private Drawable etShapeError;

  private TextChangedListener mTextChangedListener;
  private OnFocusChangeListener mOnFocusChangeListener;
  private OnClickListener mIconOnClickListener;

  private TextView.OnEditorActionListener mOnEditorActionListener;

  //普通的提示框
  private TextView tvWarningTip = null;

  public InlineTipEditText3(@NonNull Context context) {
    this(context, null);
  }

  public InlineTipEditText3(@NonNull Context context, @Nullable AttributeSet attrs) {
    this(context, attrs, 0);
  }

  public InlineTipEditText3(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
    super(context, attrs, defStyleAttr);

    ViewGroup view = (ViewGroup) LayoutInflater.from(context).inflate(R.layout.moudle_oobe_et_inline_tip3, this);
    @SuppressLint("CustomViewStyleable")
    TypedArray obtainStyledAttributes = getContext().obtainStyledAttributes(attrs, R.styleable.inline_tip_EditText2);
    int icon = obtainStyledAttributes.getInt(R.styleable.inline_tip_EditText2_showIcon, 0);
    String hint = obtainStyledAttributes.getString(R.styleable.inline_tip_EditText2_hintEdit);
    editLayout = (ViewGroup) findViewById(R.id.linear_edit);
    int count = editLayout.getChildCount();

    for (int i = 0; i < count; i++) {
      View child = editLayout.getChildAt(i);
      if (child instanceof EditText) {
        mEditText = (EditText) child;
      }
    }

    int maxCharLength = (int) obtainStyledAttributes.getInt(R.styleable.inline_tip_EditText2_maxLength, 0);
    if (maxCharLength != 0) {
      mEditText.setFilters(new InputFilter[]{new InputFilter.LengthFilter(maxCharLength)});
    }
    etShapeFocus = getResources().getDrawable(obtainStyledAttributes.getResourceId(R.styleable.inline_tip_EditText2_focusBg, R.drawable.base_et_shape_focus));
    etShapeNormal = getResources().getDrawable(obtainStyledAttributes.getResourceId(R.styleable.inline_tip_EditText2_normalBg, R.drawable.module_login_et_shape));
    etShapeError = getResources().getDrawable(com.chervon.libBase.R.drawable.base_et_shape_error);

    mEditText.setTextColor(getResources().getColor(obtainStyledAttributes.getResourceId(R.styleable.inline_tip_EditText2_editTextColor, R.color.colorTitle)));
    mEditText.setHintTextColor(getResources().getColor(obtainStyledAttributes.getResourceId(R.styleable.inline_tip_EditText2_hintTextColor, R.color.colorEditTextHint)));
    mEditText.setOnFocusChangeListener((v, hasFocus) -> {
      if (hasFocus) {
        editLayout.setBackground(etShapeFocus);
      } else {
        editLayout.setBackground(etShapeNormal);
      }

      if (mOnFocusChangeListener != null) {
        mOnFocusChangeListener.onFocusChange(v, hasFocus);
      }
    });
    mEditText.setOnEditorActionListener((textView, i, keyEvent) -> {
      if (mOnEditorActionListener != null) {
        mOnEditorActionListener.onEditorAction(textView, i, keyEvent);
      }
      return false;
    });
    setHint(obtainStyledAttributes.getString(R.styleable.inline_tip_EditText2_hintEdit));
    setText(obtainStyledAttributes.getString(R.styleable.inline_tip_EditText2_textEdit));

    //普通提示框
    tvWarningTip = findViewById(R.id.tvWarningTip);
    mEditText.setHint(hint);
    ImageButton imageButton = findViewById(R.id.ib);
    imageButton.setImageResource(obtainStyledAttributes.getResourceId(R.styleable.inline_tip_EditText2_iconDrawable, R.drawable.ic_scan));
    if (icon== 0) {
      imageButton.setVisibility(GONE);
    } else {
      imageButton.setVisibility(VISIBLE);
    }
    imageButton.setOnClickListener(view1 -> {
        if(mIconOnClickListener != null) {
          mIconOnClickListener.onClick(view1);
        }
    });
    obtainStyledAttributes.recycle();
  }
  @BindingAdapter(value = "tipText", requireAll = false)
  public static void setInlineTip(InlineTipEditText3 inlineTipEditText, String str) {
    inlineTipEditText.showInlineTip(str);
  }
  public void showInlineTip(String tipCharSpaces) {
    if (!TextUtils.isEmpty(tipCharSpaces)) {
      showErrorMargin();
      tvWarningTip.setText(tipCharSpaces);
      tvWarningTip.setVisibility(VISIBLE);
    } else {
      showNormalMargin();
      tvWarningTip.setVisibility(GONE);
    }
  }

  @BindingAdapter(value = "hintEdit", requireAll = false)
  public static void setInlineTipHint(InlineTipEditText3 inlineTipEditText, String str) {
    inlineTipEditText.setHint(str);
  }
  private void setHint(String str) {
    mEditText.setHint(str);
  }
  @InverseBindingAdapter(attribute = "hintEdit")
  public static String getInlineTipHint(InlineTipEditText3 inlineTipEditText) {
    return inlineTipEditText.getHint();
  }
  private String getHint() {
    return mEditText.getHint().toString();
  }
  @BindingAdapter(value = "textEdit", requireAll = false)
  public static void setInlineTipEditText(InlineTipEditText3 inlineTipEditText, String str) {
    inlineTipEditText.setText(str);
  }
  public void setText(String str) {
    mEditText.setText(str);
  }
  @InverseBindingAdapter(attribute = "textEdit")
  public static String getInlineTipEditText(InlineTipEditText3 inlineTipEditText) {
    return inlineTipEditText.getInlineTipEditTextString();
  }

  public String getInlineTipEditTextString() {
    return mEditText.getText().toString();
  }
  public void addTextChangedListener(TextWatcher watcher) {
    mEditText.addTextChangedListener(watcher);
  }

  public interface TextChangedListener {
    /**
     * when text changed
     *
     * @param str text
     */
    public void textChanged(String str);
  }

  public TextChangedListener getTextChangedListener() {
    return mTextChangedListener;
  }

  public void setTextChangedListener(TextChangedListener mTextChangedListenner) {
    this.mTextChangedListener = mTextChangedListenner;

    mEditText.addTextChangedListener(new TextWatcher() {
      @Override
      public void beforeTextChanged(CharSequence s, int start, int count, int after) {
      }

      @Override
      public void onTextChanged(CharSequence s, int start, int before, int count) {}

      @Override
      public void afterTextChanged(Editable s) {
        if (mTextChangedListener != null) {
          mTextChangedListener.textChanged(s.toString());
        }
      }
    });

  }

  public void setEditTextOnFocusChangeListener(OnFocusChangeListener l) {
    mOnFocusChangeListener = l;
  }

  public void setIconOnClickListener(OnClickListener listener) {
    mIconOnClickListener = listener;
  }

  public void setOnEditorActionListener(TextView.OnEditorActionListener listener) {
    mOnEditorActionListener =listener;
  }
  public void showNormalMargin(){
    if (null!=editLayout){
      editLayout.setBackground(etShapeNormal);
    }
  }

  public void showErrorMargin(){
    if (null!=editLayout){
      editLayout.setBackground(etShapeError);
    }
  }


  @Override
  public LayoutParams generateLayoutParams(AttributeSet attrs) {
    AutoSizeCompat.autoConvertDensityOfGlobal(getResources());
    AutoSizeCompat.autoConvertDensity(getResources(), 1334, false);
    return super.generateLayoutParams(attrs);
  }
}
