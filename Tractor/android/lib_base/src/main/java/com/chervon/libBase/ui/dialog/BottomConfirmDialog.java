package com.chervon.libBase.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.os.Bundle;
import android.text.SpannableString;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;

import com.blankj.utilcode.util.FragmentUtils;
import com.chervon.libBase.R;
import com.chervon.libBase.databinding.DialogBottomAlertBinding;
import com.chervon.libBase.databinding.DialogBottomConfirmBinding;

import io.reactivex.functions.Consumer;

/**
 * @ProjectName: app
 * @Package: com.chervon.libBase.ui.dialog
 * @ClassName: BottomConfirmDialog
 * @Description: 类描述
 * @Author:
 * @CreateDate: 2022/7/14 17:03
 * @UpdateUser:
 * @UpdateDate: 2022/7/14 17:03
 * @UpdateRemark: new class
 * @Version: 1.0
 */
public class BottomConfirmDialog extends DialogFragment {

    //content text
    public SpannableString content;
    //image url
    public String title;
    //left btn text
    public String lText;
    //right btn text
    public String rText;
    //data observer
    private Consumer<Boolean> consumer;

    private BottomConfirmDialog() {
    }

    /**
     * @param fragmentManager 用于显示dialog,conten 内容文本, lText 左按钮文本, rText 右按钮文本, callback 回调接口
     * @return void
     * @method show
     * @description show dialog and get update info.
     * @date: 2022/6/22 11:36
     * @author: LangMeng
     */
    public static void show(@NonNull FragmentManager fragmentManager, @NonNull SpannableString content, String title, @NonNull String lText, @NonNull String rText, Consumer<Boolean> callback) {
        Fragment fragment = FragmentUtils.findFragment(fragmentManager, BottomConfirmDialog.class.getName());
        if (fragment == null) {
            BottomConfirmDialog bottomAlertDialog = new BottomConfirmDialog();
            bottomAlertDialog.setData(content, title, lText, rText, callback);
            bottomAlertDialog.show(fragmentManager, BottomConfirmDialog.class.getName());
        }
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStyle(DialogFragment.STYLE_NO_FRAME, R.style.NoBackgroundDialog);
    }

    @SuppressLint("NotifyDataSetChanged")
    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {

        getDialog().requestWindowFeature(Window.FEATURE_NO_TITLE);

        DialogBottomConfirmBinding inflate = DataBindingUtil.inflate(getLayoutInflater(),
                R.layout.dialog_bottom_confirm, container, false);

        inflate.setBottomAlertDialog(this);

        return inflate.getRoot();
    }

    @Override
    public void onStart() {
        super.onStart();

        //set dialog window
        Dialog dialog = getDialog();
        assert dialog != null;
        dialog.getWindow().getDecorView().setPadding(0, 0, 0, 0);
        dialog.getWindow().setGravity(Gravity.BOTTOM);
        WindowManager.LayoutParams attributes = dialog.getWindow().getAttributes();
        attributes.width = WindowManager.LayoutParams.MATCH_PARENT;
        attributes.height = WindowManager.LayoutParams.MATCH_PARENT;
        dialog.getWindow().setAttributes(attributes);
        dialog.getWindow().setWindowAnimations(R.style.dialog_bottom);

        getDialog().setCancelable(false);
    }


    /**
     * @param content 内容文本, lText 左按钮文本, rText 右按钮文本, callback 回调接口
     * @return void
     * @method setData
     * @description 设置弹窗数据
     * @date: 2022/6/22 11:35
     * @author: LangMeng
     */
    public void setData(@NonNull SpannableString content, @NonNull String title, @NonNull String lText, @NonNull String rText, Consumer<Boolean> callback) {
        this.content = content;
        this.title = title;
        this.lText = lText;
        this.rText = rText;
        this.consumer = callback;
    }

    /**
     * @method lCLick
     * @description 左按钮点击事件
     * @date: 2022/6/22 11:39
     * @author: LangMeng
     * @return void
     */
    public void lCLick() {
        if (consumer != null) {
            try {
                consumer.accept(false);
                dismiss();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
    /**
     * @method rCLick
     * @description 右按钮点击事件
     * @date: 2022/6/22 11:39
     * @author: LangMeng
     * @return void
     */
    public void rCLick() {
        if (consumer != null) {
            try {
                consumer.accept(true);
                dismiss();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }


  @Override
  public void dismiss() {
    if (getFragmentManager()==null){
      //   Log.w(TAG, "dismiss: "+this+" not associated with a fragment manager." );
    }else {
      super.dismiss();
    }

  }
}

