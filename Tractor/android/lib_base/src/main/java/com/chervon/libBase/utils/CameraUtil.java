package com.chervon.libBase.utils;


import android.Manifest;
import android.app.Activity;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.provider.MediaStore;
import android.provider.Settings;

import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.core.content.FileProvider;
import com.blankj.utilcode.util.LogUtils;


import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;


/**
 * Http parmes encode.
 */
public class CameraUtil {
  //Uri获取类型判断
  public static final int TYPE_TAKE_PHOTO = 1;
  //相机RequestCode
  public static final int CODE_TAKE_PHOTO = 1;
  //相机RequestCode
  public static final int CODE_GALLERY = 2;
  public static final int CODE_BROWSE = 3;
  static File tempFile;



  public static Uri getMediaFileUri(int type) {
    File mediaStorageDir = new File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES), "DCIM");
    if (!mediaStorageDir.exists()) {
      if (!mediaStorageDir.mkdirs()) {
        return null;
      }
    }
    //建立Media File
    String timeStamp = new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date());

    File mediaFile;

    if (type == TYPE_TAKE_PHOTO) {
      mediaFile = new File(mediaStorageDir.getPath() + File.separator + "IMG_" + timeStamp + ".jpg");
    } else {

      return null;

    }

    return Uri.fromFile(mediaFile);

  }


}
