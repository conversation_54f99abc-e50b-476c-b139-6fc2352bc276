package com.chervon.libBase.ui.adapter;

import android.annotation.SuppressLint;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatCheckBox;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.LogUtils;
import com.chervon.libBase.R;
import com.chervon.libBase.model.DictNode;

/**
 * 不支持反选
 */

public class DialogBottomListAdapter2 extends RecyclerView.Adapter<DialogBottomListAdapter2.ViewHolder> {
  private Object[] dataList;
  private OnItemClickListener onItemClickListener;
  private int selectPosition = -1;

  public DialogBottomListAdapter2(Object[] dataList) {
    this.dataList = dataList;


  }

  @NonNull
  @Override
  public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
    View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.base_dialog_bottom_item, parent, false);
    return new ViewHolder(view);
  }

  @Override
  public void onBindViewHolder(@NonNull ViewHolder holder, @SuppressLint("RecyclerView") int position) {
    String content;
    if (dataList[position] instanceof DictNode) {
      content = TextUtils.isEmpty(((DictNode) dataList[position]).getDescription()) ? "" : ((DictNode) dataList[position]).getDescription();
    } else {
      content = TextUtils.isEmpty((String) dataList[position]) ? "" : (String) dataList[position];
    }

    holder.tv.setText(content);
    if (selectPosition == position) {
      holder.iv.setChecked(true);
    } else {
      holder.iv.setChecked(false);
    }

    holder.itemView.setOnClickListener(new View.OnClickListener() {
      @Override
      public void onClick(View view) {
        try {
          if (onItemClickListener != null) {
            if (selectPosition != position){
              selectPosition = position;
              notifyDataSetChanged();
            }

          }
        } catch (Exception e) {

        }

      }
    });
  }

  @Override
  public int getItemCount() {
    return dataList == null ? 0 : dataList.length;
  }

  static class ViewHolder extends RecyclerView.ViewHolder {
    private TextView tv;
    private AppCompatCheckBox iv;

    ViewHolder(@NonNull View itemView) {
      super(itemView);
      tv = itemView.findViewById(R.id.tv);
      iv = itemView.findViewById(R.id.ivItemSelected);
    }
  }

  public OnItemClickListener getOnItemClickListener() {
    return onItemClickListener;
  }

  public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
    this.onItemClickListener = onItemClickListener;
  }


  public String getContent() {
    if (dataList == null || dataList.length == 0) {
      return "";
    }

    if (dataList[selectPosition] instanceof DictNode) {

      return TextUtils.isEmpty(((DictNode) dataList[selectPosition]).getDescription()) ? "" : ((DictNode) dataList[selectPosition]).getDescription();
    } else {
      return (String) dataList[selectPosition];
    }
  }

  public int getPosition() {
    return selectPosition;
  }

  public interface OnItemClickListener {
    void onClick(View var1, String itemName);
  }

  public Object[] getDataList() {
    return dataList;
  }

  public void setDataList(Object[] dataList) {
    selectPosition = -1;
    this.dataList = dataList;
  }

  public int getSelectPosition() {
    return selectPosition;
  }

  public void setSelectPosition(int selectPosition) {
    this.selectPosition = selectPosition;
  }
}
