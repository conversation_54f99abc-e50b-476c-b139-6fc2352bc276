package com.chervon.libBase.model;

import com.chervon.libBase.ui.BaseUistate;
import com.chervon.libDB.entities.DeviceInfo;
import com.chervon.libDB.entities.ProductInfo;

public class ManualSNUIState extends BaseUistate {
    private String sn;
    private ProductInfo productInfo;
    private DeviceInfo deviceInfo;
    private  boolean isDeviceInfoRegistered;
    private boolean isExist = false;
    private boolean formRegistMore;


  public boolean isFormRegistMore() {
    return formRegistMore;
  }

  public void setFormRegistMore(boolean formRegistMore) {
    this.formRegistMore = formRegistMore;
  }

  public boolean isExist() {
    return isExist;
  }

  public void setExist(boolean exist) {
    isExist = exist;
  }

  public boolean isDeviceInfoRegistered() {
        return isDeviceInfoRegistered;
    }

    public void setDeviceInfoRegistered(boolean deviceInfoRegistered) {
        isDeviceInfoRegistered = deviceInfoRegistered;
    }

    public DeviceInfo getDeviceInfo() {
        return deviceInfo;
    }

    public void setDeviceInfo(DeviceInfo deviceInfo) {
        this.deviceInfo = deviceInfo;
    }

    public ProductInfo getProductInfo() {
        return productInfo;
    }

    public void setProductInfo(ProductInfo productInfo) {
        this.productInfo = productInfo;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

}
