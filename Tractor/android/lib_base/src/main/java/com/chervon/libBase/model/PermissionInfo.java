package com.chervon.libBase.model;

import android.view.View;

/**
 * @ProjectName: app
 * @Package: com.chervon.libBase.model
 * @ClassName: PermissionInfo
 * @Description: Toolbar data class.
 * @Author: wh
 * @CreateDate: 2022/8/22 19:34
 * @UpdateUser:
 * @UpdateDate:
 * @UpdateRemark: new class
 * @Version: 1.0
 */
public class PermissionInfo {
    private String name;
    private String content;
    private String permission;

    public PermissionInfo(String name, String content, String permission) {
        this.name = name;
        this.content = content;
        this.permission = permission;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getPermission() {
        return permission;
    }

    public void setPermission(String permission) {
        this.permission = permission;
    }
}

