package com.chervon.libBase.ui;

import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libBase.utils.Utils.sendDurationTrace;
import static com.chervon.libBase.utils.Utils.sendExposure;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.databinding.ViewDataBinding;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.ViewModelProvider;

import com.blankj.utilcode.util.LogUtils;
import com.chervon.libBase.BaseApplication;
import com.chervon.libBase.ui.viewmodel.BaseViewModel;
/**
 * @ProjectName: app
 * @Package: com.chervon.libBase.ui
 * @ClassName: BaseFragment
 * @Description: the base  Fragment of app
 * @Author: wangheng
 * @CreateDate: 2022/4/19 下午7:02
 * @UpdateUser: wangheng
 * @UpdateDate: 2022/4/19 下午7:02
 * @UpdateRemark: new
 * @Version: 1.0
 */
public abstract class BaseFragment<M extends BaseViewModel> extends   Fragment {
    public M mViewModel;
  public long enterTime=System.currentTimeMillis();
  public String mouduleId;
  public String pageId;
  public String pageResouce;
  public String stayEleId;
  public String nextButtoneleid;
    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mViewModel = new ViewModelProvider(this).get(getViewModelClass());
        mViewModel.mRegistry.handleLifecycleEvent(Lifecycle.Event.ON_CREATE);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        ViewDataBinding viewDataBinding = DataBindingUtil.inflate(inflater, getLayoutId(), container, false);
        viewDataBinding.setLifecycleOwner(this);
        initViews(viewDataBinding);
        return viewDataBinding.getRoot();
    }


    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        initDatas(savedInstanceState);

    }

    protected abstract void initDatas(Bundle savedInstanceState);


    @Override
    public void onLowMemory() {
        super.onLowMemory();
        onLowMemoryProcessed();
    }

    @Override
    public void onStart() {
        super.onStart();
        mViewModel.mRegistry.handleLifecycleEvent(Lifecycle.Event.ON_START);
        onRegister();

    }

    @Override
    public void onResume() {
        super.onResume();
        mViewModel.mRegistry.handleLifecycleEvent(Lifecycle.Event.ON_RESUME);
      initPageResouce();
      sendBaseExposure();
    }

    @Override
    public void onStop() {
        super.onStop();
        mViewModel.mRegistry.handleLifecycleEvent(Lifecycle.Event.ON_STOP);
        onUnRegister();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        mViewModel.mRegistry.handleLifecycleEvent(Lifecycle.Event.ON_DESTROY);
    }

    @Override
    public void onPause() {
        super.onPause();
      sendStayDuration();
    }

    protected abstract int getLayoutId();

    protected abstract void initViews(ViewDataBinding viewDataBinding);

    protected abstract void onUnRegister();

    protected abstract void onRegister();

    protected abstract void onLowMemoryProcessed();

    protected abstract Class<? extends M> getViewModelClass();

  @Override
  public void onHiddenChanged(boolean hidden) {
    super.onHiddenChanged(hidden);

  }

  public void sendStayDuration() {
    sendDurationTrace(this.getContext(),mouduleId,pageId,pageResouce,"",stayEleId,System.currentTimeMillis()-enterTime);
  }
  protected void sendBackTrace() {
    sendClickTrace(this.getContext(), mouduleId, pageId, pageResouce, "1", "1");
  }
  protected void sendBaseTraceClick(String eleid){
    sendClickTrace(this.getContext(), mouduleId, pageId, pageResouce, eleid, "1");
  }

  public void initPageResouce() {
    pageResouce=  ((BaseApplication)(this.getContext().getApplicationContext())).getCurrentPageResouce();
    if(!TextUtils.isEmpty(pageResouce)&&pageId!=null&&!pageResouce.endsWith(pageId)) {
      pageResouce = pageResouce + "_" + pageId;
      ((BaseApplication) (this.getContext().getApplicationContext())).setCurrentPageResouce(pageResouce);
      LogUtils.d("PageResouce" + pageResouce);
    }else
      if(TextUtils.isEmpty(pageResouce)){
        pageResouce= "1_"+pageId;
        ((BaseApplication) (this.getContext().getApplicationContext())).setCurrentPageResouce(pageResouce);

    }
  }

protected void sendBaseExposure(){
  sendExposure(this.getContext(),mouduleId,pageId,pageResouce);
}

}
