package com.chervon.libBase.model;

import com.blankj.utilcode.util.LogUtils;

/**
 * @ProjectName: app
 * @Package: com.chervon.libBase.model
 * @ClassName: BottomSelectListData
 * @Description: 底部选择弹窗数据类
 * @Author: Lang<PERSON>eng
 * @CreateDate: 2022/6/20 16:03
 * @UpdateUser: LangMeng
 * @UpdateDate: 2022/6/20 16:03
 * @UpdateRemark: new class
 * @Version: 1.0
 */
public class BottomSelectListData {
    public String dataString;
    public boolean checked = false;

    public BottomSelectListData() {
    }

    public BottomSelectListData(String dataString, boolean checked) {
        this.dataString = dataString;
        this.checked = checked;
    }

    public String getDataString() {
        return dataString;
    }

    public void setDataString(String dataString) {
        this.dataString = dataString;
    }

    public boolean isChecked() {
        return checked;
    }

    public void setChecked(boolean checked) {
        this.checked = checked;
    }
}
