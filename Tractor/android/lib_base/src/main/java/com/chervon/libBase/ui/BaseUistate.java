package com.chervon.libBase.ui;

import androidx.databinding.BaseObservable;

import com.chervon.libDB.entities.DeviceInfo;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.List;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleDeviceManage.ui.state
 * @ClassName: DeviceListUistate
 * @Description: 类描述
 * @Author: name
 * @CreateDate: 2022/5/17 下午6:18
 * @UpdateUser: 更新者
 * @UpdateDate: 2022/5/17 下午6:18
 * @UpdateRemark: 更新说明
 * @Version: 1.0
 */
public class BaseUistate extends BaseObservable {
  @JsonIgnore
    private int state;
  @JsonIgnore
    private String message;

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

  @Override
  public String toString() {
    return "BaseUistate{" +
      "state=" + state +
      ", message='" + message + '\'' +
      '}';
  }
}
