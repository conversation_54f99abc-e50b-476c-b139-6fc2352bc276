package com.chervon.libBase.ui.dialog;

import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libBase.utils.Utils.sendClickTraceNew;
import static com.chervon.libBase.utils.Utils.sendDurationTrace;
import static com.chervon.libBase.utils.Utils.sendExposure;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Context;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.text.SpannableString;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.blankj.utilcode.util.FragmentUtils;
import com.bumptech.glide.Glide;
import com.chervon.libBase.R;
import com.chervon.libBase.databinding.DialogBottomAlertBinding;
import com.chervon.libBase.databinding.DialogRecommendationAlertBinding;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

import io.reactivex.functions.Consumer;

/**
 * @ProjectName: app
 * @Package: com.chervon.libBase.ui.dialog
 * @ClassName: BottomAlertDialog
 * @Description: 类描述
 * @Author: langmeng
 * @CreateDate: 2022/7/14 17:03
 * @UpdateUser: langmeng
 * @UpdateDate: 2022/7/14 17:03
 * @UpdateRemark: new class
 * @Version: 1.0
 */
public class RecommendationDialog extends DialogFragment {

    //content text
    public String content;
    //image url
    public String imgUrl;

    private String mLinkUrl;
    //data observer
    private RecommendClickListener listener;
    public static String mPageSource = "";
    private String pageId;
    private static long enterTime;
    private static String mouduleId;
    private static String eleId;
    private static String moderNumber;

    public RecommendationDialog() {
        super();
        mouduleId = "12";
        pageId = "550";
        eleId = "0";
    }


    /**
     * @param fragmentManager 用于显示dialog,conten 内容文本, bottomText 按钮, callback 回调接口
     * @return void
     * @method show
     * @description show dialog and get update info.
     * @date: 2022/6/22 11:36
     * @author: LangMeng
     */
    public static void show(@NonNull FragmentManager fragmentManager,
                            @NonNull String content,
                            String imgUrl,
                            @NonNull String linkUrl,
                            String mNumber,
                            RecommendClickListener callback, String pageSource) {

        Fragment fragment = FragmentUtils.findFragment(fragmentManager, RecommendationDialog.class.getName());
        if (fragment == null) {
            mPageSource = pageSource;
            enterTime = System.currentTimeMillis();
            moderNumber = mNumber;
            RecommendationDialog bottomAlertDialog = new RecommendationDialog();
            bottomAlertDialog.setData(content, imgUrl, linkUrl, callback);
            bottomAlertDialog.showFragmentDialog(fragmentManager, RecommendationDialog.class.getName());


        }
    }

    @Override
    public void onPause() {
        super.onPause();
        eleId = "3";
        sendDurationTrace(this.getContext(), mouduleId, pageId, mPageSource, "", eleId, System.currentTimeMillis() - enterTime);
    }

    //处理 Can not perform this action after onSaveInstanceState
    private void showFragmentDialog(FragmentManager manager, String tag) {
        try {
            //由于父类方法中mDismissed，mShownByMe不可直接访问，所以此处采用反射修改他们的值
            Class dialogFragmentClass = DialogFragment.class;
            Field mDismissed = dialogFragmentClass.getDeclaredField("mDismissed");
            mDismissed.setAccessible(true);
            mDismissed.set(this, false);

            Field mShownByMe = dialogFragmentClass.getDeclaredField("mShownByMe");
            mShownByMe.setAccessible(true);
            mShownByMe.set(this, true);

            FragmentTransaction ft = manager.beginTransaction();
            ft.add(this, tag);
            ft.commitNowAllowingStateLoss();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStyle(DialogFragment.STYLE_NO_FRAME, R.style.NoBackgroundDialog);
    }

    @SuppressLint("NotifyDataSetChanged")
    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {

        getDialog().requestWindowFeature(Window.FEATURE_NO_TITLE);

        DialogRecommendationAlertBinding inflate = DataBindingUtil.inflate(getLayoutInflater(), R.layout.dialog_recommendation_alert, container, false);

        inflate.setBottomAlertDialog(this);

        Glide.with(this).load(imgUrl).error(new ColorDrawable(getResources().getColor(R.color.colorBackground))).placeholder(R.drawable.ic_device_default).into(inflate.dialogBottomAlertImg);

        return inflate.getRoot();
    }

    @Override
    public void onStart() {
        super.onStart();
        //set dialog window
        Dialog dialog = getDialog();
        assert dialog != null;
        dialog.getWindow().getDecorView().setPadding(0, 0, 0, 0);
        dialog.getWindow().setGravity(Gravity.BOTTOM);
        WindowManager.LayoutParams attributes = dialog.getWindow().getAttributes();
        attributes.width = WindowManager.LayoutParams.MATCH_PARENT;
        attributes.height = WindowManager.LayoutParams.MATCH_PARENT;
        dialog.getWindow().setAttributes(attributes);
        dialog.getWindow().setWindowAnimations(R.style.dialog_bottom);
        sendExposureWithModelNumber();
        getDialog().setCancelable(false);
    }

    private void sendExposureWithModelNumber(){
        String MODEL_NUMBER_KEY = "model_number";

        HashMap<String, String> expandMap = new HashMap();
        expandMap.put(MODEL_NUMBER_KEY, moderNumber);

        sendExposure(this.getContext(), mouduleId, pageId, mPageSource,expandMap);

    }

    public void setData(@NonNull String content, @NonNull String imgUrl, String linkUrl, RecommendClickListener callback) {
        this.content = content;
        this.imgUrl = imgUrl;
        this.mLinkUrl = linkUrl;
        this.listener = callback;
    }

    public void bottomClick() {
        if (listener != null) {
            try {
                listener.shopNow();
                String model_number = "model_number";
                Map<String, String> expand = new HashMap();
                expand.put(model_number, moderNumber);
                sendClickTraceNew(this.getContext(), mouduleId, pageId, mPageSource, "1", expand);
                dismiss();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public void closeDialog() {
        if (listener != null) {
            try {
                listener.closeDialog();
                sendClickTrace(this.getContext(), mouduleId, pageId, mPageSource, "2", "1");
                dismiss();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }


    @Override
    public void dismiss() {
        if (getFragmentManager() == null) {
            //   Log.w(TAG, "dismiss: "+this+" not associated with a fragment manager." );
        } else {
            super.dismiss();
        }

    }

    public interface RecommendClickListener {
        public void closeDialog();

        public void shopNow();
    }

    @Override
    public void onDestroyView() {
        getDialog().setOnCancelListener(null);
        super.onDestroyView();
    }
}

