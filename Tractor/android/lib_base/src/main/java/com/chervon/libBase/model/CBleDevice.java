package com.chervon.libBase.model;

import android.bluetooth.BluetoothDevice;

import com.clj.fastble.data.BleDevice;

/**
 * @Author: 184862
 * @desc 对于BleDevice的包装
 * @CreateDate: 2023/6/25
 * @UpdateDate: 2023/6/25
 */
public class CBleDevice extends BleDevice {

  public String snCode;

  public BleDevice cBleDevice;



  public BleDevice getCBleDevice() {
    return cBleDevice;
  }

  public void setCBleDevice(BleDevice cBleDevice) {
    this.cBleDevice = cBleDevice;
  }

  public CBleDevice(BluetoothDevice device) {
    super(device);
  }

  public String getSnCode() {
    return snCode;
  }

  public void setSnCode(String snCode) {
    this.snCode = snCode;
  }
}
