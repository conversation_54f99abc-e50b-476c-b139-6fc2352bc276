package com.chervon.libBase.utils;

import android.content.Context;
import android.util.Log;

import com.google.android.exoplayer2.database.StandaloneDatabaseProvider;
import com.google.android.exoplayer2.source.MediaSource;
import com.google.android.exoplayer2.source.ProgressiveMediaSource;
import com.google.android.exoplayer2.upstream.DataSource;
import com.google.android.exoplayer2.upstream.DefaultDataSource;
import com.google.android.exoplayer2.upstream.DefaultHttpDataSource;
import com.google.android.exoplayer2.upstream.FileDataSource;
import com.google.android.exoplayer2.upstream.cache.Cache;
import com.google.android.exoplayer2.upstream.cache.CacheDataSource;
import com.google.android.exoplayer2.upstream.cache.CacheSpan;
import com.google.android.exoplayer2.upstream.cache.LeastRecentlyUsedCacheEvictor;
import com.google.android.exoplayer2.upstream.cache.SimpleCache;

import java.io.File;
import java.util.NavigableSet;

public class VideoCacheManager2 {
    private static final String TAG = "VideoCacheManager2";
    private static final long MAX_CACHE_SIZE = 1024 * 1024 * 1024; // 1GB cache
    private final Context context;
    private Cache cache;
    private DataSource.Factory cacheDataSourceFactory;
    private MediaSource.Factory mediaSourceFactory;

    public VideoCacheManager2(Context context) {
        this.context = context.getApplicationContext();
        initializeCache();
    }

    private void initializeCache() {
        if (cache == null) {
            File cacheDir = new File(context.getCacheDir(), "video_cache");
            if (!cacheDir.exists()) {
                cacheDir.mkdirs();
            }

            // 创建缓存实例
            cache = new SimpleCache(
                cacheDir,
                new LeastRecentlyUsedCacheEvictor(MAX_CACHE_SIZE),
                new StandaloneDatabaseProvider(context)
            );

            // 创建缓存数据源工厂
            cacheDataSourceFactory = buildCacheDataSourceFactory();
            
            // 创建媒体源工厂
            mediaSourceFactory = new ProgressiveMediaSource.Factory(cacheDataSourceFactory);
        }
    }

    private DataSource.Factory buildCacheDataSourceFactory() {
        // 上游数据源（用于在线播放）
        DefaultHttpDataSource.Factory upstreamFactory = new DefaultHttpDataSource.Factory()
            .setConnectTimeoutMs(15000)
            .setReadTimeoutMs(15000)
            .setAllowCrossProtocolRedirects(true);

        // 默认数据源工厂（用于本地文件）
        DefaultDataSource.Factory upstreamFactoryWithLocal = new DefaultDataSource.Factory(
            context,
            upstreamFactory
        );

        // 文件数据源工厂（用于缓存文件）
        DataSource.Factory fileDataSourceFactory = new FileDataSource.Factory();

        // 创建支持缓存的数据源工厂
        CacheDataSource.Factory cacheDataSourceFactory = new CacheDataSource.Factory();
        
        // 设置缓存和上游数据源
        cacheDataSourceFactory.setCache(cache);
        cacheDataSourceFactory.setUpstreamDataSourceFactory(upstreamFactoryWithLocal);
        
        // 设置缓存标志
        cacheDataSourceFactory.setFlags(
            CacheDataSource.FLAG_BLOCK_ON_CACHE |  // 如果缓存可用，优先使用缓存
            CacheDataSource.FLAG_IGNORE_CACHE_ON_ERROR    // 缓存出错时忽略缓存
        );
        
        // 设置缓存事件监听
        cacheDataSourceFactory.setEventListener(new CacheDataSource.EventListener() {
            @Override
            public void onCachedBytesRead(long cacheSizeBytes, long cachedBytesRead) {

            }

            @Override
            public void onCacheIgnored(int reason) {
                Log.d(TAG, "缓存被忽略, 原因: " + reason);
            }
        });
        
        return cacheDataSourceFactory;
    }

    public MediaSource.Factory getMediaSourceFactory() {
        return mediaSourceFactory;
    }

    public boolean isCached(String url) {
        if (cache != null && url != null) {
            try {
                String key = buildCacheKey(url);
                // 获取所有缓存块
                NavigableSet<CacheSpan> cachedSpans = cache.getCachedSpans(key);
                if (!cachedSpans.isEmpty()) {
                    // 检查是否有连续的缓存块
                    long lastEnd = 0;
                    for (CacheSpan span : cachedSpans) {
                        if (span.position != lastEnd) {
                            return false; // 发现缓存块不连续
                        }
                        lastEnd = span.position + span.length;
                    }
                    return true; // 所有缓存块都是连续的
                }
            } catch (Exception e) {
                Log.e(TAG, "检查缓存状态失败: " + e.getMessage());
            }
        }
        return false;
    }

    private String buildCacheKey(String url) {
        return url;
    }

    public void release() {
        if (cache != null) {
            try {
                cache.release();
                cache = null;
            } catch (Exception e) {
                Log.e(TAG, "释放缓存失败", e);
            }
        }
    }
}
