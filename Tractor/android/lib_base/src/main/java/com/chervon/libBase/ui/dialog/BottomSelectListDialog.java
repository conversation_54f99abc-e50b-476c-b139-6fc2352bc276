package com.chervon.libBase.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.os.Bundle;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.fragment.app.DialogFragment;
import androidx.lifecycle.MutableLiveData;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chervon.libBase.BR;
import com.chervon.libBase.BaseDataBindingAdapter;

import com.chervon.libBase.R;
import com.chervon.libBase.databinding.DialogBottomSelectListBinding;
import com.chervon.libBase.model.BottomSelectListData;

import java.util.ArrayList;
import java.util.List;

import io.reactivex.functions.Consumer;


/**
 * @ProjectName: app
 * @Package: com.chervon.moudleUserCenter.ui
 * @ClassName: BottomSelectListDialogFragment
 * @Description: 底部列表选择弹窗
 * @Author: LangMeng
 * @CreateDate: 2022/6/20 10:56
 * @UpdateUser: LangMeng
 * @UpdateDate: 2022/6/20 10:56
 * @UpdateRemark: new class
 * @Version: 1.0
 */
public class BottomSelectListDialog extends DialogFragment {

    //data
    private List<BottomSelectListData> dataList = new ArrayList<>();
    //title string
    public MutableLiveData<String> titleData = new MutableLiveData<>();
    //recyclerview adapter
    private BaseDataBindingAdapter bottomSelectListAdapter;
    //data observer
    private Consumer<Integer> consumer;
    private LinearLayoutManager linearLayoutManager;
    //是否滚动到选中条目
    private Boolean isScrollToChecked = false;


    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStyle(DialogFragment.STYLE_NO_FRAME, R.style.NoBackgroundDialog);
    }

    @SuppressLint("NotifyDataSetChanged")
    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {

        getDialog().requestWindowFeature(Window.FEATURE_NO_TITLE);


        DialogBottomSelectListBinding inflate = DataBindingUtil.inflate(LayoutInflater.from(getContext()),
                R.layout.dialog_bottom_select_list, container, false);

        inflate.setBootomSelectListDialog(this);

        //init data
        initAdapter();
        linearLayoutManager = new LinearLayoutManager(getContext());
        //init recyclerview
        inflate.dialogBottomSelectListRecyclerview.setLayoutManager(linearLayoutManager);
        inflate.dialogBottomSelectListRecyclerview.setAdapter(bottomSelectListAdapter);
        bottomSelectListAdapter.notifyDataSetChanged();

        if (isScrollToChecked) {
            //滚动列表至选中项
            for (int i = 0; i < dataList.size(); i++) {
                BottomSelectListData listData = dataList.get(i);
                if (listData.isChecked()) {
                    MoveToPosition(linearLayoutManager,inflate.dialogBottomSelectListRecyclerview,i);
                }
            }
        }

        inflate.dialogBottomSelectListSave.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                for (int i = 0; i < dataList.size(); i++) {
                    BottomSelectListData bottomSelectListData = dataList.get(i);
                    if (bottomSelectListData.checked) {
                        try {
                            consumer.accept(i);
                            getDialog().dismiss();
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }

            }
        });
        return inflate.getRoot();
    }

    /**
     * @method initAdapter
     * @description init adapter
     * @date: 2022/6/20 15:45
     * @author: LangMeng
     * @return void
     */
    private void initAdapter() {
        bottomSelectListAdapter = new BaseDataBindingAdapter(
                getContext(),
                R.layout.dialog_bottom_select_list_item,
                dataList,
                BR.dialogBottomSelecetListData,
                new BaseDataBindingAdapter.itemCallBack() {
                    @Override
                    public void onBindViewHolder(BaseDataBindingAdapter.ViewHolder holder,
                                                 int position,
                                                 RecyclerView.Adapter<BaseDataBindingAdapter.ViewHolder> adapter) {

                        holder.itemView.findViewById(R.id.dialog_bottom_select_list_item_checkbox)
                                .setOnClickListener(new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                //数据单选
                                for (int i = 0; i < dataList.size(); i++) {
                                    dataList.get(i).setChecked(false);
                                }
                                dataList.get(position).setChecked(true);

                                adapter.notifyDataSetChanged();
                            }
                        });
                    }
                });
    }

    /**
     * @method MoveToPosition
     * @description RecyclerView 移动到当前位置，
     * @date: 2022/7/2 17:49
     * @author: LangMeng
     * @param manager       设置RecyclerView对应的manager
     * @param mRecyclerView 当前的RecyclerView
     * @param n             要跳转的位置
     * @return void
     */
    public void MoveToPosition(LinearLayoutManager manager, RecyclerView mRecyclerView, int n) {
        int firstItem = manager.findFirstVisibleItemPosition();
        int lastItem = manager.findLastVisibleItemPosition();
        if (n <= firstItem) {
            mRecyclerView.scrollToPosition(n);
        } else if (n <= lastItem) {
            int top = mRecyclerView.getChildAt(n - firstItem).getTop();
            mRecyclerView.scrollBy(0, top);
        } else {
            mRecyclerView.scrollToPosition(n);
        }
    }

    @Override
    public void onStart() {
        super.onStart();

        //set dialog window
        Dialog dialog = getDialog();
        assert dialog != null;
        dialog.getWindow().getDecorView().setPadding(0, 0, 0, 0);
        dialog.getWindow().setGravity(Gravity.BOTTOM);
        WindowManager.LayoutParams attributes = dialog.getWindow().getAttributes();
        attributes.width = WindowManager.LayoutParams.MATCH_PARENT;
        attributes.height = WindowManager.LayoutParams.WRAP_CONTENT;
        dialog.getWindow().setAttributes(attributes);

        getDialog().setCancelable(true);
    }

    public void setDataList(List<BottomSelectListData> dataList) {
        this.dataList = dataList;


    }

    public void setDataObserver(Consumer<Integer> consumer) {
        this.consumer = consumer;
    }

    public void setTitle(String title) {
        titleData.setValue(title);
    }

    public void setScrollToChecked(Boolean scrollToChecked) {
        isScrollToChecked = scrollToChecked;
    }


  @Override
  public void dismiss() {
    if (getFragmentManager()==null){
      //   Log.w(TAG, "dismiss: "+this+" not associated with a fragment manager." );
    }else {
      super.dismiss();
    }

  }
}
