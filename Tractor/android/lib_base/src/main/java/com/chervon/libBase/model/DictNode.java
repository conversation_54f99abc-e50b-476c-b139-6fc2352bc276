package com.chervon.libBase.model;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleDeviceManage.ui.state
 * @ClassName: DeviceListUistate
 * @Description: 类描述
 * @Author: name
 * @CreateDate: 2022/5/17 下午6:18
 * @UpdateUser: 更新者
 * @UpdateDate: 2022/5/17 下午6:18
 * @UpdateRemark: 更新说明
 * @Version: 1.0
 */
public class DictNode {


   private  String dictId;

    private  String label;

    private  String  description;

    private  String dictNodeId;

    private  String langId;

    private  String  langCode;

    public DictNode(String description) {
        this.description = description;
    }
    public DictNode() {
    }

    public String getDictId() {
        return dictId;
    }

    public void setDictId(String dictId) {
        this.dictId = dictId;
    }

    public String getDictNodeId() {
        return dictNodeId;
    }

    public void setDictNodeId(String dictNodeId) {
        this.dictNodeId = dictNodeId;
    }

    public String getLangId() {
        return langId;
    }

    public void setLangId(String langId) {
        this.langId = langId;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getLangCode() {
        return langCode;
    }

    public void setLangCode(String langCode) {
        this.langCode = langCode;
    }
}
