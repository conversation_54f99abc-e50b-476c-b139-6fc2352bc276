package com.chervon.libBase.utils;

import static com.chervon.libBase.utils.Utils.sendClickTraceNew;
import static com.chervon.libRouter.RouterConstants.KEY_FROM;
import static com.chervon.libRouter.RouterConstants.KEY_FROM_REVIEW_MANAGER;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_DATA;

import android.app.Activity;
import android.app.Dialog;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.databinding.DataBindingUtil;

import com.alibaba.android.arouter.launcher.ARouter;
import com.blankj.utilcode.util.LogUtils;
import com.bumptech.glide.Glide;
import com.chervon.libBase.BaseApplication;
import com.chervon.libBase.BuildConfig;
import com.chervon.libBase.R;
import com.chervon.libBase.databinding.DialogEvaluateConfirmBinding;
import com.chervon.libBase.model.FeedBackMoreEntry;
import com.chervon.libBase.model.UserInfo;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libDB.entities.User;
import com.chervon.libRouter.RouterConstants;
import com.google.android.gms.tasks.Task;
import com.google.android.play.core.review.ReviewInfo;
import com.google.android.play.core.review.ReviewManager;
import com.google.android.play.core.review.ReviewManagerFactory;

import java.io.Serializable;

/**
 * @Author: 184862
 * @CreateDate: 2024/5/7
 * @UpdateDate: 2024/5/7
 * @desc Google 评分系统
 */
public class ReviewManagerService {

  private final String TAG = "ReviewManagerService";
  private static ReviewManagerService instance = null;
  private Dialog evaluateDialog;
  private Activity parentActivity;
  private String mPageResouce;
  public static ReviewManagerService getInstance() {
    if (null == instance) {
      synchronized (ReviewManagerService.class) {
        if (null == instance) {
          instance = new ReviewManagerService();
        } else {
          return instance;
        }
      }
    }
    return instance;
  }

  /**
   * 弹出条件
   * 1、用户添加过一款设备
   * 2、用户注册完成
   * 3、用户打开RN面板
   */
  public void intentEvaluateView(Activity activity,String pageResouce) {
    this.mPageResouce = pageResouce;
    User userInfo = UserInfo.get();
   long a_Month = 30L * 24 * 60 * 60 * 1000;
    // long a_Month = 1 * 60 * 1000;

    if (null == userInfo) {
      return;
    }

    if (!TextUtils.isEmpty(userInfo.getHasFeedBack())){
      return;
    }

    //如果上次dimiss时间小于或者等于30天，不弹窗
    String dimissTime = userInfo.getDimissReView();

    if (!TextUtils.isEmpty(dimissTime)) {
      long time = Long.parseLong(userInfo.getDimissReView());
      if (System.currentTimeMillis() - time <= a_Month) {
        return;
      }
    }

    String isAddDevice = userInfo.getIsAddDevice();
    String isRegistedSuccess = userInfo.getIsRegistedSuccess();
    String hasOpenRnContainer = userInfo.getHasOpenRnContainer();
    this.parentActivity = activity;
    if (BuildConfig.EVN.equals(Utils.NA_TAG)){
      if (!TextUtils.isEmpty(isAddDevice) && !TextUtils.isEmpty(isRegistedSuccess) && !TextUtils.isEmpty(hasOpenRnContainer)) {
        simpleConfirmDialog();
      }
    }else {
      //TODO  待定需求欧洲评分系统满足 添加设备&打开面板
//      if (!TextUtils.isEmpty(isAddDevice) && !TextUtils.isEmpty(hasOpenRnContainer)) {
//        simpleConfirmDialog();
//      }
    }

  }


  private void simpleConfirmDialog() {

    if (null == parentActivity) {
      return;
    }
    if (parentActivity.isFinishing()) {
      return;
    }

    DialogEvaluateConfirmBinding simpleConfirmDialogBinding = null;

    try {
      if (evaluateDialog == null || simpleConfirmDialogBinding == null) {

        if (null!=evaluateDialog){
          if (evaluateDialog.isShowing()){
            return;
          }
        }

        evaluateDialog = new Dialog(parentActivity);

        simpleConfirmDialogBinding = DataBindingUtil.inflate(LayoutInflater.from(parentActivity), R.layout.dialog_evaluate_confirm, null, false);

        evaluateDialog.setContentView(simpleConfirmDialogBinding.getRoot());
        evaluateDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        evaluateDialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        Glide.with(BaseApplication.getInstance()).load(AppConfig.getAppIconRound2()).into(simpleConfirmDialogBinding.imgEvaluateLogo);
        simpleConfirmDialogBinding.getRoot().findViewById(R.id.img_evaluate_dimiss).setOnClickListener(new View.OnClickListener() {
          @Override
          public void onClick(View v) {
            if (null==evaluateDialog){
              return;
            }
            evaluateDialog.dismiss();
            evaluateDialog = null;
            User user = UserInfo.get();
            if (null == user) {
              return;
            }
            user.setDimissReView(System.currentTimeMillis() + "");
            UserInfo.set(user);
            closeReviewManager();
          }
        });
      }

      simpleConfirmDialogBinding.tvCancel.setOnClickListener(new View.OnClickListener() {
        @Override
        public void onClick(View v) {
          if (null==evaluateDialog){
            return;
          }
          evaluateDialog.dismiss();
          evaluateDialog = null;
          intentFeedBack();
          traceNegativeComment();
        }
      });
      simpleConfirmDialogBinding.tvConfirm.setOnClickListener(new View.OnClickListener() {
        @Override
        public void onClick(View v) {
          if (null != evaluateDialog) {
            traceGoodReputation();
            evaluateDialog.dismiss();
            evaluateDialog = null;
            launchGooglePlay(parentActivity, new GooglePlayFlowListener() {
              @Override
              public void OnCompleteListener() {

              }

              @Override
              public void OnErrorListener() {

              }
            });

            User user = UserInfo.get();
            if (null == user) {
              return;
            }
            user.setDimissReView(System.currentTimeMillis() + "");
            UserInfo.set(user);

          }

        }
      });
      evaluateDialog.show();
    } catch (Exception e) {
      LogUtils.d(e.getMessage());
    }

  }


  public interface GooglePlayFlowListener {
    void OnCompleteListener();

    void OnErrorListener();
  }

  public static void launchGooglePlay(@NonNull Activity context, GooglePlayFlowListener listener) {
    ReviewManager manager = ReviewManagerFactory.create(context);
    Task<ReviewInfo> request = manager.requestReviewFlow();
    request.addOnCompleteListener(task -> {
      if (task.isSuccessful()) {
        ReviewInfo reviewInfo = task.getResult();
        Task<Void> flow = manager.launchReviewFlow(context, reviewInfo);
        flow.addOnCompleteListener(task1 -> {
          if (listener != null) {
            listener.OnCompleteListener();
          }
        });
      } else {
        // There was some problem, log or handle the error code.
        //@ReviewErrorCode int reviewErrorCode = ((TaskException) task.getException()).getErrorCode();
        if (listener != null) {
          listener.OnErrorListener();
        }
      }
    });
  }


  /**
   * 跳转FeedBack
   */
  private void intentFeedBack() {
    upgradeReViewManager();
    FeedBackMoreEntry uiState = new FeedBackMoreEntry(LanguageStrings.getFeedbackAppissues(), "more", "app");
    ARouter.getInstance()
      .build(RouterConstants.ACTIVITY_FEEDBACK_QUESTION)
      .withSerializable(KEY_PREV_DATA, (Serializable) uiState)
      .withString(KEY_FROM,KEY_FROM_REVIEW_MANAGER)
      .navigation();

  }

  private void upgradeReViewManager(){
    User user = UserInfo.get();
    user.setDimissReView(System.currentTimeMillis()+"");
    UserInfo.set(user);
  }

  /**
   * 点击好评埋点
   */
  private void traceGoodReputation(){
    String module_id = "5";
    String page_id = "9";
    String ele_id = "7";
    String mod_id = "0";

    sendClickTraceNew(BaseApplication.getInstance(), module_id, page_id, mPageResouce, ele_id,mod_id);
  }

  /**
   * 点击差评埋点
   */
  private void traceNegativeComment(){
    String module_id = "5";
    String page_id = "9";
    String ele_id = "8";
    String mod_id = "0";

    sendClickTraceNew(BaseApplication.getInstance(), module_id, page_id, mPageResouce, ele_id,mod_id);
  }

  /**
   * 关闭弹窗
   */
  private void closeReviewManager(){
    String module_id = "5";
    String page_id = "9";
    String ele_id = "9";
    String mod_id = "0";

    sendClickTraceNew(BaseApplication.getInstance(), module_id, page_id, mPageResouce, ele_id,mod_id);
  }

}
