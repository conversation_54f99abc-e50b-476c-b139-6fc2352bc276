package com.chervon.libBase.ui.dialog;

import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libBase.utils.Utils.sendDurationTrace;
import static com.chervon.libBase.utils.Utils.sendExposure;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;

import com.blankj.utilcode.util.FragmentUtils;
import com.blankj.utilcode.util.LogUtils;
import com.chervon.libBase.R;
import com.chervon.libBase.databinding.DialogCloseFeedbackConfirmAlertBinding;
import com.chervon.libBase.databinding.DialogUpgradeConfirmAlertBinding;

import io.reactivex.functions.Consumer;

/**
 * @ProjectName: app
 * @Package: com.chervon.libBase.ui.dialog
 * @ClassName: CloseFeedBackConfirmAlertDialog
 * @Description: 确认对话框
 * @Author: wj
 * @CreateDate: 2025/6/11
 * @UpdateUser: wj
 * @UpdateDate: 2025/6/11
 * @UpdateRemark: new class
 * @Version: 1.0
 */
public class CloseFeedBackConfirmAlertDialog extends DialogFragment {
    private static CloseFeedBackConfirmAlertDialog instance;
    private static final String TAG = CloseFeedBackConfirmAlertDialog.class.getName();

    public static String mouduleId;
    public static String pageId;
    public static String pageResouce;
    public static String stayEleId;
    public static String doButtoneleid;
    public static String cancelButtoneleid;
    public long enterTime;

    //data observer
    private Consumer<Boolean> consumer;

    private CloseFeedBackConfirmAlertDialog() {
    }


    public static CloseFeedBackConfirmAlertDialog showCloseFeedBackDialog(@NonNull FragmentManager fragmentManager,
                                                                          Consumer<Boolean> callback) {
        // 检查是否已有实例在显示或存在
        Fragment existingDialog = fragmentManager.findFragmentByTag(TAG);
        if (existingDialog instanceof CloseFeedBackConfirmAlertDialog) {
            CloseFeedBackConfirmAlertDialog dialog = (CloseFeedBackConfirmAlertDialog) existingDialog;
            dialog.setData(callback);
            if (!dialog.isVisible()) {
                dialog.show(fragmentManager, TAG);
            }
            instance = dialog;
            return dialog;
        }

        // 创建新实例
        instance = new CloseFeedBackConfirmAlertDialog();
        instance.setData(callback);
        instance.show(fragmentManager, TAG);
        return instance;
    }


    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStyle(DialogFragment.STYLE_NO_FRAME, R.style.NoBackgroundDialog);
        // 允许状态保存
        setRetainInstance(true);
    }

    @SuppressLint("NotifyDataSetChanged")
    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        getDialog().requestWindowFeature(Window.FEATURE_NO_TITLE);
        DialogCloseFeedbackConfirmAlertBinding inflate = DataBindingUtil.inflate(LayoutInflater.from(getContext()),
                R.layout.dialog_close_feedback_confirm_alert, container, false);
        inflate.setConfirmAlertDialog(this);
        return inflate.getRoot();
    }

    @Override
    public void onStart() {
        super.onStart();
        //set dialog window
        Dialog dialog = getDialog();
        assert dialog != null;
        dialog.getWindow().getDecorView().setPadding(0, 0, 0, 0);
        dialog.getWindow().setGravity(Gravity.CENTER);
        WindowManager.LayoutParams attributes = dialog.getWindow().getAttributes();
        attributes.width = WindowManager.LayoutParams.MATCH_PARENT;
        attributes.height = WindowManager.LayoutParams.WRAP_CONTENT;
        dialog.getWindow().setAttributes(attributes);

        getDialog().setCancelable(false);


        getDialog().setOnKeyListener(new DialogInterface.OnKeyListener() {
            @Override
            public boolean onKey(DialogInterface dialogInterface, int keyCode, KeyEvent event) {
                if (keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_DOWN) {
                    try {
                        consumer.accept(false);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                    dismiss();
                    return true;
                }

                return false;
            }
        });

        enterTime = System.currentTimeMillis();
        sendExposure(this.getContext(), mouduleId, pageId, pageResouce);
    }


    /**
     * @param callback 回调接口
     * @return void
     * @method setData
     * @description 设置弹窗数据
     * @date: 2022/6/22 11:35
     * @author: LangMeng
     */
    public void setData(Consumer<Boolean> callback) {
        this.consumer = callback;
    }

    /**
     * @return void
     * @method lCLick
     * @description 左按钮点击事件
     * @date: 2022/6/22 11:39
     * @author: LangMeng
     */
    public void lCLick() {
        if (consumer != null) {
            try {
                consumer.accept(false);
                sendNextButtonClick(cancelButtoneleid);
                dismiss();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * @return void
     * @method rCLick
     * @description 右按钮点击事件
     * @date: 2022/6/22 11:39
     * @author: LangMeng
     */
    public void rCLick() {
        if (consumer != null) {
            try {
                consumer.accept(true);
                sendNextButtonClick(doButtoneleid);
                dismiss();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public static void initTrace(String stayEleIda, String pageIda, String mouduleIda, String pageResoucea, String cancelButtoneleida, String doButtoneleida) {
        stayEleId = stayEleIda;
        pageId = pageIda;
        mouduleId = mouduleIda;
        pageResouce = pageResoucea;
        cancelButtoneleid = cancelButtoneleida;
        doButtoneleid = doButtoneleida;
    }

    @Override
    public void onPause() {
        super.onPause();
        sendDurationTrace(this.getContext(), mouduleId, pageId, pageResouce, "", stayEleId, System.currentTimeMillis() - enterTime);
    }

    @Override
    public void onStop() {
        super.onStop();
        // 不在 onStop 时销毁对话框，保持实例状态
        if (getDialog() != null) {
            getDialog().setDismissMessage(null);
        }
    }

    @Override
    public void dismiss() {
        if (getFragmentManager() != null) {
            super.dismiss();
        }
        // 清除实例引用
        instance = null;
    }

    @Override
    public void onSaveInstanceState(@NonNull Bundle outState) {
        super.onSaveInstanceState(outState);
        // 可以在这里保存额外的状态数据如果需要
    }

    @Override
    public void onDestroyView() {
        Dialog dialog = getDialog();
        if (dialog != null && getRetainInstance()) {
            dialog.setDismissMessage(null);
        }
        super.onDestroyView();
    }


    public void sendNextButtonClick(String eleid) {
//    sendClickTrace(this.getContext(),mouduleId,pageId,pageResouce, eleid,"1");
    }
}
