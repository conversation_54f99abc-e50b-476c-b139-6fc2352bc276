package com.chervon.libBase.model;

import com.chervon.libBase.ui.BaseUistate;
import com.chervon.libDB.entities.DeviceInfo;
import com.chervon.libDB.entities.ProductInfo;

public class NoIotResultUIState extends BaseUistate {
  public String sn;
  public boolean isAdded;
  public boolean isRegistered;
  public DeviceInfo deviceInfo;
  public boolean naEvn;



  public DeviceInfo getDeviceInfo() {
    return deviceInfo;
  }

  public void setDeviceInfo(DeviceInfo deviceInfo) {
    this.deviceInfo = deviceInfo;
  }


  public boolean isRegistered() {
    return isRegistered;
  }

  public void setRegistered(boolean registered) {
    isRegistered = registered;
  }

  public boolean isAdded() {
    return isAdded;
  }

  public void setAdded(boolean added) {
    isAdded = added;
  }

  public String getSn() {
    return sn;
  }

  public void setSn(String sn) {
    this.sn = sn;
  }

  public boolean isNaEvn() {
    return naEvn;
  }

  public void setNaEvn(boolean naEvn) {
    this.naEvn = naEvn;
  }
}
