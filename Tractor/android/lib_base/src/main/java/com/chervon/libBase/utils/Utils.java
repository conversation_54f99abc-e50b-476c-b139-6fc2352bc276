package com.chervon.libBase.utils;


import static android.content.Context.LOCATION_SERVICE;
import static android.os.Build.VERSION.RELEASE;

import static com.chervon.libBase.model.AppConstants.ADD_0;
import static com.chervon.libBase.model.AppConstants.LOCK_POSITION;

import android.Manifest;
import android.animation.ValueAnimator;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.ActivityManager;
import android.app.Application;
import android.app.Application.ActivityLifecycleCallbacks;
import android.app.NotificationManager;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.res.AssetManager;
import android.location.Location;
import android.location.LocationManager;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.provider.Settings;
import android.telephony.TelephonyManager;
import android.text.TextUtils;
import android.util.Log;
import android.view.Display;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;

import androidx.annotation.NonNull;
import androidx.core.app.ActivityCompat;
import androidx.core.app.NotificationManagerCompat;
import androidx.core.content.FileProvider;

import com.blankj.utilcode.util.ConvertUtils;
import com.blankj.utilcode.util.FileIOUtils;
import com.blankj.utilcode.util.GsonUtils;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.NetworkUtils;
import com.blankj.utilcode.util.SPUtils;
import com.blankj.utilcode.util.TimeUtils;
import com.chervon.libBase.BaseApplication;
import com.chervon.libBase.BuildConfig;
import com.chervon.libBase.model.PermissionInfo;
import com.chervon.libBase.model.UserInfo;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libBase.utils.mlang.MyLang;
import com.chervon.libDB.SoftRoomDatabase;
import com.chervon.libDB.dao.RnBundleInfoDao;
import com.chervon.libDB.entities.AppSettingEntry;
import com.chervon.libDB.entities.BundleInfoEntry;
import com.chervon.libDB.entities.User;
import com.chervon.trace.CEvent;
import com.chervon.trace.bean.EventBean;
import com.chervon.trace.bean.EventConstant;
import com.chervon.trace.utils.BatteryUtils;
import com.clj.fastble.BleManager;
import com.google.gson.Gson;


import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Random;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * Created by LangMeng on 2018/12/5.
 */
@SuppressWarnings("AlibabaUndefineMagicConstant")
public final class Utils {
    private static final String TAG = "Utils";
    private static final String PERMISSION_ACTIVITY_CLASS_NAME = "com.blankj.utilcode.util.PermissionUtils$PermissionActivity";

    private static final ActivityLifecycleImpl ACTIVITY_LIFECYCLE = new ActivityLifecycleImpl();
    private static final ExecutorService UTIL_POOL = Executors.newFixedThreadPool(3);
    private static final Handler UTIL_HANDLER = new Handler(Looper.getMainLooper());

    @SuppressLint("StaticFieldLeak")
    private static Application sApplication;
    //来源
    public static final String SOURCE_NAME = "source_name";
    //埋点10月31日新增字段---‘时区’
    public static final String TIMEZONE_KEY = "timezone";

    //欧洲时间格式
    public static final String EU_DAY_MONTH_YEAR = "dayMonthYear";
    //北美时间格式
    public static final String NA_MM_DD_YYYY = "monthDayYear";
    public static final String NA_TAG = "na";
    //公制
    public static final String METRIC = "metric";
    //英制
    public static final String IMPERIAL = "imperial";

    public static final String SETTING_OF_HOUR_12 = "12hours";
    public static final String SETTING_OF_HOUR_24 = "24hours";

    public static final String EVN_EU = "EU";
    public static final String EVN_NA = "NA";
    public static final String EVN_ANZ = "ANZ";

    //多语言相关
    public static final String NA_LANGUAGE = "en";
    public static final String NA_DEFAULT_LANG_CODE = "en-US";
    public static final String EU_DEFAULT_LANG_CODE = "en-GB";
    public static final String DEFAULT_LANGUAGE_TIME = "1000000000";
    public static final String NA_LANGUAGE_NAME = "English";


    private Utils() {
        throw new UnsupportedOperationException("u can't instantiate me...");
    }

    /**
     * Init utils.
     * <p>Init it in the class of Application.</p>
     *
     * @param context context
     */
    public static void init(final Context context) {
        if (context == null) {
            init(getApplicationByReflect());
            return;
        }
        init((Application) context.getApplicationContext());
    }

    /**
     * Init utils.
     * <p>Init it in the class of Application.</p>
     *
     * @param app application
     */
    public static void init(final Application app) {
        if (sApplication == null) {
            if (app == null) {
                sApplication = getApplicationByReflect();
            } else {
                sApplication = app;
            }
            sApplication.registerActivityLifecycleCallbacks(ACTIVITY_LIFECYCLE);
        } else {
            if (app != null && app.getClass() != sApplication.getClass()) {
                sApplication.unregisterActivityLifecycleCallbacks(ACTIVITY_LIFECYCLE);
                ACTIVITY_LIFECYCLE.mActivityList.clear();
                sApplication = app;
                sApplication.registerActivityLifecycleCallbacks(ACTIVITY_LIFECYCLE);
            }
        }
    }

    /**
     * Return the context of Application object.
     *
     * @return the context of Application object
     */
    public static Application getApp() {
        if (sApplication != null) {
            return sApplication;
        }
        Application app = getApplicationByReflect();
        init(app);
        return app;
    }

    static ActivityLifecycleImpl getActivityLifecycle() {
        return ACTIVITY_LIFECYCLE;
    }

    static LinkedList<Activity> getActivityList() {
        return ACTIVITY_LIFECYCLE.mActivityList;
    }

    static Context getTopActivityOrApp() {
        if (isAppForeground()) {
            Activity topActivity = ACTIVITY_LIFECYCLE.getTopActivity();
            return topActivity == null ? Utils.getApp() : topActivity;
        } else {
            return Utils.getApp();
        }
    }

    static boolean isAppForeground() {
        ActivityManager am = (ActivityManager) Utils.getApp().getSystemService(Context.ACTIVITY_SERVICE);
        if (am == null) {
            return false;
        }
        List<ActivityManager.RunningAppProcessInfo> info = am.getRunningAppProcesses();
        if (info == null || info.size() == 0) {
            return false;
        }
        for (ActivityManager.RunningAppProcessInfo aInfo : info) {
            if (aInfo.importance == ActivityManager.RunningAppProcessInfo.IMPORTANCE_FOREGROUND) {
                if (aInfo.processName.equals(Utils.getApp().getPackageName())) {
                    return true;
                }
            }
        }
        return false;
    }

    static <T> Task<T> doAsync(final Task<T> task) {
        UTIL_POOL.execute(task);
        return task;
    }

    public static void runOnUiThread(final Runnable runnable) {
        if (Looper.myLooper() == Looper.getMainLooper()) {
            runnable.run();
        } else {
            Utils.UTIL_HANDLER.post(runnable);
        }
    }

    public static void runOnUiThreadDelayed(final Runnable runnable, long delayMillis) {
        Utils.UTIL_HANDLER.postDelayed(runnable, delayMillis);
    }

//    static String getCurrentProcessName() {
//        String name = getCurrentProcessNameByFile();
//        if (!TextUtils.isEmpty(name)) {
//            return name;
//        }
//        name = getCurrentProcessNameByAms();
//        if (!TextUtils.isEmpty(name)) {
//            return name;
//        }
//        name = getCurrentProcessNameByReflect();
//        return name;
//    }

    static void fixSoftInputLeaks(final Window window) {
        InputMethodManager imm =
                (InputMethodManager) Utils.getApp().getSystemService(Context.INPUT_METHOD_SERVICE);
        if (imm == null) {
            return;
        }
        String[] leakViews = new String[]{"mLastSrvView", "mCurRootView", "mServedView", "mNextServedView"};
        for (String leakView : leakViews) {
            try {
                Field leakViewField = InputMethodManager.class.getDeclaredField(leakView);
                if (leakViewField == null) {
                    continue;
                }
                if (!leakViewField.isAccessible()) {
                    leakViewField.setAccessible(true);
                }
                Object obj = leakViewField.get(imm);
                if (!(obj instanceof View)) {
                    continue;
                }
                View view = (View) obj;
                if (view.getRootView() == window.getDecorView().getRootView()) {
                    leakViewField.set(imm, null);
                }
            } catch (Throwable ignore) {/**/}
        }
    }


    ///////////////////////////////////////////////////////////////////////////
    // private method
    ///////////////////////////////////////////////////////////////////////////

//    private static String getCurrentProcessNameByFile() {
//        try {
//            File file = new File("/proc/" + android.os.Process.myPid() + "/" + "cmdline");
//            BufferedReader mBufferedReader = new BufferedReader(new FileReader(file));
//            String processName = mBufferedReader.readLine().trim();
//            mBufferedReader.close();
//            return processName;
//        } catch (Exception e) {
//            e.printStackTrace();
//            return "";
//        }
//    }

    private static String getCurrentProcessNameByAms() {
        ActivityManager am = (ActivityManager) Utils.getApp().getSystemService(Context.ACTIVITY_SERVICE);
        if (am == null) {
            return "";
        }
        List<ActivityManager.RunningAppProcessInfo> info = am.getRunningAppProcesses();
        if (info == null || info.size() == 0) {
            return "";
        }
        int pid = android.os.Process.myPid();
        for (ActivityManager.RunningAppProcessInfo aInfo : info) {
            if (aInfo.pid == pid) {
                if (aInfo.processName != null) {
                    return aInfo.processName;
                }
            }
        }
        return "";
    }

    private static String getCurrentProcessNameByReflect() {
        String processName = "";
        try {
            Application app = Utils.getApp();
            Field loadedApkField = app.getClass().getField("mLoadedApk");
            loadedApkField.setAccessible(true);
            Object loadedApk = loadedApkField.get(app);

            Field activityThreadField = loadedApk.getClass().getDeclaredField("mActivityThread");
            activityThreadField.setAccessible(true);
            Object activityThread = activityThreadField.get(loadedApk);

            Method getProcessName = activityThread.getClass().getDeclaredMethod("getProcessName");
            processName = (String) getProcessName.invoke(activityThread);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return processName;
    }

    private static Application getApplicationByReflect() {
        try {
            @SuppressLint("PrivateApi")
            Class<?> activityThread = Class.forName("android.app.ActivityThread");
            Object thread = activityThread.getMethod("currentActivityThread").invoke(null);
            Object app = activityThread.getMethod("getApplication").invoke(thread);
            if (app == null) {
                throw new NullPointerException("u should init first");
            }
            return (Application) app;
        } catch (NoSuchMethodException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InvocationTargetException e) {
            e.printStackTrace();
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        }
        throw new NullPointerException("u should init first");
    }

    /**
     * Set animators enabled.
     */
    private static void setAnimatorsEnabled() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O && ValueAnimator.areAnimatorsEnabled()) {
            return;
        }
        try {
            //noinspection JavaReflectionMemberAccess
            @SuppressLint("SoonBlockedPrivateApi") Field sDurationScaleField = ValueAnimator.class.getDeclaredField("sDurationScale");
            sDurationScaleField.setAccessible(true);
            float sDurationScale = (Float) sDurationScaleField.get(null);
            if (sDurationScale == 0f) {
                sDurationScaleField.set(null, 1f);
                Log.i("Utils", "setAnimatorsEnabled: Animators are enabled now!");
            }
        } catch (NoSuchFieldException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
    }


    static class ActivityLifecycleImpl implements ActivityLifecycleCallbacks {

        final LinkedList<Activity> mActivityList = new LinkedList<>();
        final Map<Object, OnAppStatusChangedListener> mStatusListenerMap = new HashMap<>();
        final Map<Activity, Set<OnActivityDestroyedListener>> mDestroyedListenerMap = new HashMap<>();

        private int mForegroundCount = 0;
        private int mConfigCount = 0;
        private boolean mIsBackground = false;

        @Override
        public void onActivityCreated(Activity activity, Bundle savedInstanceState) {
            //      LanguageUtils.applyLanguage(activity);
            setAnimatorsEnabled();
            setTopActivity(activity);
        }

        @Override
        public void onActivityStarted(Activity activity) {
            if (!mIsBackground) {
                setTopActivity(activity);
            }
            if (mConfigCount < 0) {
                ++mConfigCount;
            } else {
                ++mForegroundCount;
            }
        }

        @Override
        public void onActivityResumed(final Activity activity) {
            setTopActivity(activity);
            if (mIsBackground) {
                mIsBackground = false;
                postStatus(true);
            }
            processHideSoftInputOnActivityDestroy(activity, false);
        }

        @Override
        public void onActivityPaused(Activity activity) {

        }

        @Override
        public void onActivityStopped(Activity activity) {
            if (activity.isChangingConfigurations()) {
                --mConfigCount;
            } else {
                --mForegroundCount;
                if (mForegroundCount <= 0) {
                    mIsBackground = true;
                    postStatus(false);
                }
            }
            processHideSoftInputOnActivityDestroy(activity, true);
        }

        @Override
        public void onActivitySaveInstanceState(Activity activity, Bundle outState) {/**/}

        @Override
        public void onActivityDestroyed(Activity activity) {
            mActivityList.remove(activity);
            consumeOnActivityDestroyedListener(activity);
            fixSoftInputLeaks(activity.getWindow());
        }

        Activity getTopActivity() {
            if (!mActivityList.isEmpty()) {
                for (int i = mActivityList.size() - 1; i >= 0; i--) {
                    Activity activity = mActivityList.get(i);
                    if (activity == null
                            || activity.isFinishing()
                            || (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1 && activity.isDestroyed())) {
                        continue;
                    }
                    return activity;
                }
            }
            Activity topActivityByReflect = getTopActivityByReflect();
            if (topActivityByReflect != null) {
                setTopActivity(topActivityByReflect);
            }
            return topActivityByReflect;
        }

        void addOnAppStatusChangedListener(final Object object,
                                           final OnAppStatusChangedListener listener) {
            mStatusListenerMap.put(object, listener);
        }

        void removeOnAppStatusChangedListener(final Object object) {
            mStatusListenerMap.remove(object);
        }

        void removeOnActivityDestroyedListener(final Activity activity) {
            if (activity == null) {
                return;
            }
            mDestroyedListenerMap.remove(activity);
        }

        void addOnActivityDestroyedListener(final Activity activity,
                                            final OnActivityDestroyedListener listener) {
            if (activity == null || listener == null) {
                return;
            }
            Set<OnActivityDestroyedListener> listeners;
            if (!mDestroyedListenerMap.containsKey(activity)) {
                listeners = new HashSet<>();
                mDestroyedListenerMap.put(activity, listeners);
            } else {
                listeners = mDestroyedListenerMap.get(activity);
                if (listeners.contains(listener)) {
                    return;
                }
            }
            listeners.add(listener);
        }

        /**
         * To solve close keyboard when activity onDestroy.
         * The preActivity set windowSoftInputMode will prevent
         * the keyboard from closing when curActivity onDestroy.
         */
        private void processHideSoftInputOnActivityDestroy(final Activity activity, boolean isSave) {
            if (isSave) {
                final WindowManager.LayoutParams attrs = activity.getWindow().getAttributes();
                final int softInputMode = attrs.softInputMode;
                activity.getWindow().getDecorView().setTag(-123, softInputMode);
                activity.getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_HIDDEN);
            } else {
                final Object tag = activity.getWindow().getDecorView().getTag(-123);
                if (!(tag instanceof Integer)) {
                    return;
                }
                Utils.runOnUiThreadDelayed(new Runnable() {
                    @Override
                    public void run() {
                        activity.getWindow().setSoftInputMode(((Integer) tag));
                    }
                }, 100);
            }
        }

        private void postStatus(final boolean isForeground) {
            if (mStatusListenerMap.isEmpty()) {
                return;
            }
            for (OnAppStatusChangedListener onAppStatusChangedListener : mStatusListenerMap.values()) {
                if (onAppStatusChangedListener == null) {
                    return;
                }
                if (isForeground) {
                    onAppStatusChangedListener.onForeground();
                } else {
                    onAppStatusChangedListener.onBackground();
                }
            }
        }

        private void setTopActivity(final Activity activity) {
            if (PERMISSION_ACTIVITY_CLASS_NAME.equals(activity.getClass().getName())) {
                return;
            }
            if (mActivityList.contains(activity)) {
                if (!mActivityList.getLast().equals(activity)) {
                    mActivityList.remove(activity);
                    mActivityList.addLast(activity);
                }
            } else {
                mActivityList.addLast(activity);
            }
        }

        private void consumeOnActivityDestroyedListener(Activity activity) {
            Iterator<Map.Entry<Activity, Set<OnActivityDestroyedListener>>> iterator
                    = mDestroyedListenerMap.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry<Activity, Set<OnActivityDestroyedListener>> entry = iterator.next();
                if (entry.getKey() == activity) {
                    Set<OnActivityDestroyedListener> value = entry.getValue();
                    for (OnActivityDestroyedListener listener : value) {
                        listener.onActivityDestroyed(activity);
                    }
                    iterator.remove();
                }
            }
        }

        private Activity getTopActivityByReflect() {
            try {
                @SuppressLint("PrivateApi")
                Class<?> activityThreadClass = Class.forName("android.app.ActivityThread");
                Object currentActivityThreadMethod = activityThreadClass.getMethod("currentActivityThread").invoke(null);
                Field mActivityListField = activityThreadClass.getDeclaredField("mActivityList");
                mActivityListField.setAccessible(true);
                Map activities = (Map) mActivityListField.get(currentActivityThreadMethod);
                if (activities == null) {
                    return null;
                }
                for (Object activityRecord : activities.values()) {
                    Class activityRecordClass = activityRecord.getClass();
                    Field pausedField = activityRecordClass.getDeclaredField("paused");
                    pausedField.setAccessible(true);
                    if (!pausedField.getBoolean(activityRecord)) {
                        Field activityField = activityRecordClass.getDeclaredField("activity");
                        activityField.setAccessible(true);
                        return (Activity) activityField.get(activityRecord);
                    }
                }
            } catch (ClassNotFoundException e) {
                e.printStackTrace();
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            } catch (InvocationTargetException e) {
                e.printStackTrace();
            } catch (NoSuchMethodException e) {
                e.printStackTrace();
            } catch (NoSuchFieldException e) {
                e.printStackTrace();
            }
            return null;
        }
    }

    public static final class FileProvider4UtilCode extends FileProvider {

        @Override
        public boolean onCreate() {
            Utils.init(getContext());
            return true;
        }
    }

    ///////////////////////////////////////////////////////////////////////////
    // interface
    ///////////////////////////////////////////////////////////////////////////

    public abstract static class Task<Result> implements Runnable {

        private static final int NEW = 0;
        private static final int COMPLETING = 1;
        private static final int CANCELLED = 2;
        private static final int EXCEPTIONAL = 3;

        private volatile int state = NEW;

        abstract Result doInBackground();

        private Callback<Result> mCallback;

        public Task(final Callback<Result> callback) {
            mCallback = callback;
        }

        @Override
        public void run() {
            try {
                final Result t = doInBackground();

                if (state != NEW) {
                    return;
                }
                state = COMPLETING;
                UTIL_HANDLER.post(new Runnable() {
                    @Override
                    public void run() {
                        mCallback.onCall(t);
                    }
                });
            } catch (Throwable th) {
                if (state != NEW) {
                    return;
                }
                state = EXCEPTIONAL;
            }
        }

        public void cancel() {
            state = CANCELLED;
        }

        public boolean isDone() {
            return state != NEW;
        }

        public boolean isCanceled() {
            return state == CANCELLED;
        }
    }

    public interface Callback<T> {
        void onCall(T data);
    }

    public interface OnAppStatusChangedListener {
        void onForeground();

        void onBackground();
    }

    public interface OnActivityDestroyedListener {
        void onActivityDestroyed(Activity activity);
    }

    static SPUtils getSpUtils4Utils() {
        return SPUtils.getInstance("Utils");
    }

    public static String getCountryCode(Context context, String def) {
        try {
            TelephonyManager tm = (TelephonyManager) context.getSystemService(Context.TELEPHONY_SERVICE);
            if (tm.getPhoneType() != 2) {
                return tm.getNetworkCountryIso().toUpperCase();
            }
        } catch (Exception var3) {
        }

        return def;
    }

    public static byte[] uriToByteArray(Context context, Uri uri) {
        InputStream inputStream = null;
        ByteArrayOutputStream byteArrayOutputStream = null;
        try {
            inputStream = context.getContentResolver().openInputStream(uri);
            byte[] data = new byte[2048];
            byteArrayOutputStream = new ByteArrayOutputStream();
            while (inputStream.read(data) != -1) {
                byteArrayOutputStream.write(data);
            }
            byteArrayOutputStream.flush();
            return byteArrayOutputStream.toByteArray();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (byteArrayOutputStream != null) {
                try {
                    byteArrayOutputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return null;
    }


    public static void sendExposure(Context context, String moduleId, String pageId, String pagesource) {
        if (pageId == null) {
            return;
        }
        String residencetimeKey = "residencetime";
        try {
            Long appStartTime = ((BaseApplication) ((Activity) context).getApplication()).getAppStartTime();
            EventBean eventBean = new EventBean();
            eventBean.moduleid = moduleId;
            eventBean.pageid = pageId;
            eventBean.pagesource = pagesource;
            eventBean.modid = "0";
            eventBean.eleid = "0";
            EventBean.Expand expand = new EventBean.Expand();
            expand.map = new HashMap<>();
            expand.map.put(SOURCE_NAME, TextUtils.isEmpty(context.getClass().getName()) ? "unKnowClass" : context.getClass().getName());
            expand.map.put(residencetimeKey, System.currentTimeMillis() - appStartTime);

            SoftRoomDatabase.databaseWriteExecutor.execute(() -> {
                sendTrace(eventBean, expand, context, EventConstant.EVENT_TYPE_EXPOSURE);
            });
        } catch (Exception e) {
            LogUtils.e(TAG, "sendExposure is error--->" + e.getMessage() + "--context---"+context.getClass().getName());
        }

    }

    public static void sendExposure(Context context, String moduleId, String pageId, String pageSource, HashMap<String, String> expandMap) {
        if (pageId == null) {
            return;
        }
        String residencetimeKey = "residencetime";
        try {
            Long appStartTime = ((BaseApplication) ((Activity) context).getApplication()).getAppStartTime();
            EventBean eventBean = new EventBean();
            eventBean.moduleid = moduleId;
            eventBean.pageid = pageId;
            eventBean.pagesource = pageSource;
            eventBean.modid = "0";
            eventBean.eleid = "0";
            Map<String, Object> map = new HashMap<>();
            EventBean.Expand expand = new EventBean.Expand();
            map.put(residencetimeKey, System.currentTimeMillis() - appStartTime);
            if (!expandMap.isEmpty()) {
                for (Map.Entry<String, String> entry : expandMap.entrySet()) {
                    map.put(entry.getKey(), entry.getValue());
                }
            }

            if (null != context) {
                map.put(SOURCE_NAME, TextUtils.isEmpty(context.getClass().getName()) ? "unKnowClass" : context.getClass().getName());
            }
            if (map.size() > 0) {
                expand.map = map;
            }

            SoftRoomDatabase.databaseWriteExecutor.execute(() -> {
                sendTrace(eventBean, expand, context, EventConstant.EVENT_TYPE_EXPOSURE);
            });
        } catch (Exception e) {
            LogUtils.e(TAG, "sendExposure is error contain expandMap---" + e.getMessage());
        }

    }
    public static void sendExposureContainEleId(Context context, String moduleId, String pageId, String pageSource,String eleId,
                                                HashMap<String, String> expandMap) {
        if (pageId == null) {
            return;
        }
        try {
            EventBean eventBean = new EventBean();
            eventBean.moduleid = moduleId;
            eventBean.pageid = pageId;
            eventBean.pagesource = pageSource;
            eventBean.modid = "0";
            eventBean.eleid = eleId;
            Map<String, Object> map = new HashMap<>();
            EventBean.Expand expand = new EventBean.Expand();
            if (!expandMap.isEmpty()) {
                for (Map.Entry<String, String> entry : expandMap.entrySet()) {
                    map.put(entry.getKey(), entry.getValue());
                }
            }

            if (null != context) {
                map.put(SOURCE_NAME, TextUtils.isEmpty(context.getClass().getName()) ? "unKnowClass" : context.getClass().getName());
            }
            if (map.size() > 0) {
                expand.map = map;
            }

            SoftRoomDatabase.databaseWriteExecutor.execute(() -> {
                sendTrace(eventBean, expand, context, EventConstant.EVENT_TYPE_EXPOSURE);
            });
        } catch (Exception e) {
            LogUtils.e(TAG, "sendExposure is error contain expandMap---" + e.getMessage());
        }

    }


    public static void sendClickTrace(Context reactContext, String moduleid, String pageid, String pagesource, String eleid, String eventid, String key, String value
            , String key1, String value1
            , String key2, String value2
            , String key3, String value3) {
        if (pageid == null) {
            return;
        }
        EventBean eventBean = new EventBean();
        eventBean.moduleid = moduleid;
        eventBean.pageid = pageid;
        eventBean.pagesource = pagesource;
        eventBean.eleid = eleid;
        EventBean.Expand expand = new EventBean.Expand();
        //TODO TEST CODE
        expand.map = new HashMap<>();
        try {
            expand.map.put(SOURCE_NAME, TextUtils.isEmpty(reactContext.getClass().getName()) ? "unKnowClass" : reactContext.getClass().getName());
            expand.map.put(key, value);
            expand.map.put(key1, value1);
            expand.map.put(key2, value2);
            expand.map.put(key3, value3);
        } catch (Exception e) {

        }
        SoftRoomDatabase.databaseWriteExecutor.execute(() -> {
            sendTrace(eventBean, expand, reactContext, EventConstant.EVENT_TYPE_CLICK);
        });

    }

    public static void sendClickTrace(Context reactContext, String moduleid, String pageid, String pagesource, String eleid, String eventid, String key, String value) {
        if (pageid == null) {
            return;
        }
        EventBean eventBean = new EventBean();
        eventBean.moduleid = moduleid;
        eventBean.pageid = pageid;
        eventBean.pagesource = pagesource;
        eventBean.eleid = eleid;
        EventBean.Expand expand = new EventBean.Expand();
        //TODO TEST CODE
        expand.map = new HashMap<>();
        try {
            expand.map.put(SOURCE_NAME, TextUtils.isEmpty(reactContext.getClass().getName()) ? "unKnowClass" : reactContext.getClass().getName());
            expand.map.put(key, value);
        } catch (Exception e) {

        }
        SoftRoomDatabase.databaseWriteExecutor.execute(() -> {
            sendTrace(eventBean, expand, reactContext, EventConstant.EVENT_TYPE_CLICK);
        });

    }

    /**
     * 点击事件埋点--新增
     *
     * @param context
     * @param moduleid
     * @param pageid
     * @param pagesource
     * @param eleid
     * @param expandMap
     */
    public static void sendClickTraceNew(Context context, String moduleid, String pageid, String pagesource, String eleid, Map<String, String> expandMap) {
        if (pageid == null) {
            return;
        }
        EventBean eventBean = new EventBean();
        eventBean.moduleid = moduleid;
        eventBean.pageid = pageid;
        eventBean.pagesource = pagesource;
        eventBean.eleid = eleid;
        EventBean.Expand expand = new EventBean.Expand();

        expand.map = new HashMap<>();
        try {
            expand.map.put(SOURCE_NAME, TextUtils.isEmpty(context.getClass().getName()) ? "unKnowClass" : context.getClass().getName());
            expand.map.putAll(expandMap);
        } catch (Exception e) {
            LogUtils.i("sendClickTrace--->" + e.getMessage());
        }
        SoftRoomDatabase.databaseWriteExecutor.execute(() -> {
            sendTrace(eventBean, expand, context, EventConstant.EVENT_TYPE_CLICK);
        });
        FileHelper.appendToFile(context, new Gson().toJson(eventBean));
    }


    /**
     * 点击事件埋点--新增
     *
     * @param context
     * @param moduleid
     * @param pageid
     * @param pagesource
     * @param eleid
     */
    public static void sendClickTraceNew(Context context, String moduleid, String pageid, String pagesource, String eleid, String mod_id) {
        if (pageid == null) {
            return;
        }
        EventBean eventBean = new EventBean();
        eventBean.moduleid = moduleid;
        eventBean.pageid = pageid;
        eventBean.pagesource = pagesource;
        eventBean.eleid = eleid;
        eventBean.modid = mod_id;
        EventBean.Expand expand = new EventBean.Expand();

        expand.map = new HashMap<>();

        SoftRoomDatabase.databaseWriteExecutor.execute(() -> {
            sendTrace(eventBean, expand, context, EventConstant.EVENT_TYPE_CLICK);
        });
        FileHelper.appendToFile(context, new Gson().toJson(eventBean));
    }

    public static void sendClickTrace(Context reactContext, String moduleid, String pageid, String pagesource, String eleid, String eventid) {
        if (pageid == null) {
            return;
        }
        EventBean eventBean = new EventBean();
        eventBean.moduleid = moduleid;
        eventBean.pageid = pageid;
        eventBean.pagesource = pagesource;
        eventBean.eleid = eleid;
        EventBean.Expand expand = new EventBean.Expand();
        //TODO TEST CODE
        expand.map = new HashMap<>();
        try {
            expand.map.put(SOURCE_NAME, TextUtils.isEmpty(reactContext.getClass().getName()) ? "unKnowClass" : reactContext.getClass().getName());
        } catch (Exception e) {

        }
        SoftRoomDatabase.databaseWriteExecutor.execute(() -> {
            sendTrace(eventBean, expand, reactContext, EventConstant.EVENT_TYPE_CLICK);
        });

    }

    public static void sendDurationTrace(Context reactContext, String moduleid, String pageid, String pagesource, String modid, String eleid, Object residencetime) {
        if (pageid == null) {
            return;
        }
        EventBean eventBean = new EventBean();
        eventBean.moduleid = moduleid;
        eventBean.pageid = pageid;
        eventBean.pagesource = pagesource;
        eventBean.modid = modid;
        eventBean.eleid = eleid;
        EventBean.Expand expand = new EventBean.Expand();
        Map<String, Object> map = new HashMap<>();
        try {
            expand.map.put(SOURCE_NAME, TextUtils.isEmpty(reactContext.getClass().getName()) ? "unKnowClass" : reactContext.getClass().getName());
        } catch (Exception e) {

        }

        map.put("residencetime", residencetime);
        testCode(reactContext, map, expand);
        SoftRoomDatabase.databaseWriteExecutor.execute(() -> {
            sendTrace(eventBean, expand, reactContext, EventConstant.EVENT_TYPE_DURATION);
        });
    }

    public static void sendDurationWithExpandTrace(Context context,
                                                   String moduleId,
                                                   String pageId,
                                                   String pageSource,
                                                   String modId,
                                                   String eleId,
                                                   HashMap<String, String> expandMaps) {
        if (pageId == null) {
            return;
        }
        EventBean eventBean = new EventBean();
        eventBean.moduleid = moduleId;
        eventBean.pageid = pageId;
        eventBean.pagesource = pageSource;
        eventBean.modid = modId;
        eventBean.eleid = eleId;
        EventBean.Expand expand = new EventBean.Expand();
        Map<String, Object> map = new HashMap<>();

        try {
            expand.map.put(SOURCE_NAME, TextUtils.isEmpty(context.getClass().getName()) ? "unKnowClass" : context.getClass().getName());
        } catch (Exception e) {

        }
        for (Map.Entry<String, String> entry : expandMaps.entrySet()) {
            map.put(entry.getKey(), entry.getValue());
        }
        testCode(context, map, expand);
        SoftRoomDatabase.databaseWriteExecutor.execute(() -> {
            sendTrace(eventBean, expand, context, EventConstant.EVENT_TYPE_DURATION);
        });
    }


    private static void testCode(Context context, Map<String, Object> map, EventBean.Expand expand) {
        try {
            expand.map = map;
            expand.map.put(SOURCE_NAME, TextUtils.isEmpty(context.getClass().getName()) ? "unKnowClass" : context.getClass().getName());
        } catch (Exception e) {

        }

    }

    public static void sendTrace(EventBean eventBean, EventBean.Expand expand, Context reactContext, String eventType) {
        int TRACE_DATA_LOCATION_PERMISSION_GRANTED = 1;
        int TRACE_DATA_LOCATION_PERMISSION_UNGRANTED = 3;

        try {
            if (null == eventBean) {
                return;
            }
            eventBean.modid = "0";
            Map<String, Object> result = new HashMap();
            result.put(EventConstant.EVENTTYPE, eventType);
            result.put(EventConstant.PAGERESOURCE, eventBean.getPagesource());
            result.put(EventConstant.MODULEID, eventBean.getModuleid());
            result.put(EventConstant.PAGEID, eventBean.getPageid());
            result.put(EventConstant.MODID, eventBean.modid);
            result.put(EventConstant.ELEID, eventBean.getEleid());
            result.put(EventConstant.TIMESTAMP, System.currentTimeMillis() + "");
            result.put(EventConstant.PLATFORM, "Android");

            eventBean.eventid = eventBean.moduleid + "_" + eventBean.pageid + "_" + eventBean.modid + "_" + eventBean.eleid + "_" + eventType;
            result.put(EventConstant.EVENTID, eventBean.getEventid());


            EventBean.AppInfo appInfo = new EventBean.AppInfo();
            appInfo.appcode = "EGO_Connect";
            appInfo.appname = AppUtils.getAppName() + "";
            appInfo.version = AppUtils.getAppVersionName();
            appInfo.language = MyLang.getCurrentLanguageCode();
            if (Build.VERSION.SDK_INT >= 31) {
                appInfo.bluetoothauthority = checkPermission(new PermissionInfo(LanguageStrings.getBluetoothtitle(),
                        LanguageStrings.getBluetoothContent(), "granted"), "android.permission.BLUETOOTH_SCAN", reactContext);
            } else {
                appInfo.bluetoothauthority = checkPermission(new PermissionInfo(LanguageStrings.getBluetoothtitle(), LanguageStrings.getBluetoothContent(), "granted"), Manifest.permission.ACCESS_COARSE_LOCATION, reactContext);
            }
            if (!BleManager.getInstance().isBlueEnable()) {
                appInfo.bluetoothstatus = "5";
            } else {
                appInfo.bluetoothstatus = "4";
            }

            appInfo.locationauthority = BaseApplication.getInstance().locationPermission ? TRACE_DATA_LOCATION_PERMISSION_GRANTED : TRACE_DATA_LOCATION_PERMISSION_UNGRANTED;
            appInfo.cameraauthority = checkPermission(new PermissionInfo(LanguageStrings.getBluetoothtitle(), LanguageStrings.getBluetoothContent(),
                    "granted"), Manifest.permission.CAMERA, reactContext);
            appInfo.photoauthority = checkPermission(new PermissionInfo(LanguageStrings.getBluetoothtitle(), LanguageStrings.getBluetoothContent(),
                    "granted"), Manifest.permission.READ_EXTERNAL_STORAGE, reactContext);
            if (NotificationManagerCompat.from(reactContext).areNotificationsEnabled()) {
                appInfo.notificationauthority = 1;
            } else {
                appInfo.notificationauthority = 2;
            }

            EventBean.MobileInfo mobileInfo = new EventBean.MobileInfo();
            mobileInfo.mobile = Build.MODEL;
            mobileInfo.os = "android";
            mobileInfo.osversion = RELEASE;
            NetworkUtils.NetworkType netWorkType = NetworkUtils.getNetworkType();
            if (netWorkType == NetworkUtils.NetworkType.NETWORK_WIFI) {
                mobileInfo.network = "1";
            } else if (netWorkType == NetworkUtils.NetworkType.NETWORK_2G) {
                mobileInfo.network = "2";
            } else if (netWorkType == NetworkUtils.NetworkType.NETWORK_3G) {
                mobileInfo.network = "3";
            } else if (netWorkType == NetworkUtils.NetworkType.NETWORK_4G) {
                mobileInfo.network = "4";
            } else if (netWorkType == NetworkUtils.NetworkType.NETWORK_5G) {
                mobileInfo.network = "5";
            } else {
                mobileInfo.network = "0";
            }
            String batterystateStr = "";
            try {
                batterystateStr = BatteryUtils.getBatteryCharging(reactContext);
            } catch (Exception e) {
                LogUtils.d(e.toString());
            }

            if ("1".equalsIgnoreCase(batterystateStr)) {

                mobileInfo.batterystate = "1";
            } else if ("2".equalsIgnoreCase(batterystateStr)) {
                mobileInfo.batterystate = "2";
            } else {
                mobileInfo.batterystate = "0";
            }

            if (BaseApplication.getInstance().locationPermission) {
                mobileInfo.longitude = TextUtils.isEmpty(BaseApplication.getInstance().longitude) ? "" : BaseApplication.getInstance().longitude;
                mobileInfo.latitude = TextUtils.isEmpty(BaseApplication.getInstance().latitude) ? "" : BaseApplication.getInstance().latitude;
            } else {
                mobileInfo.longitude = "";
                mobileInfo.latitude = "";
            }
            //手动刷新定位
            BaseApplication.getInstance().initMap();

            mobileInfo.batterylevel = BatteryUtils.getBatteryLeft(reactContext);
            mobileInfo.locallanguage = MyLang.getCurrentLanguageCode();

            SoftRoomDatabase db = SoftRoomDatabase.getDatabase(reactContext.getApplicationContext());
            RnBundleInfoDao mRnBundleInfoDao = db.rnBundleInfoDao();
            List<BundleInfoEntry> bundleInfoEntryList = mRnBundleInfoDao.getBundleInfoList();
            List<EventBean.RnInfo> rnInfoList = new ArrayList<>();
            if (bundleInfoEntryList != null && bundleInfoEntryList.size() > 0) {

                for (int i = 0; i < bundleInfoEntryList.size(); i++) {
                    BundleInfoEntry bundleInfoEntry = bundleInfoEntryList.get(i);
                    EventBean.RnInfo rnInfo = new EventBean.RnInfo();
                    rnInfo.platename = bundleInfoEntry.getRnBundleName();
                    rnInfo.version = bundleInfoEntry.getLastRnVersion();
                    rnInfoList.add(rnInfo);
                }

            }


            result.put(EventConstant.RNINFO, rnInfoList);


            User user = UserInfo.getDataOnly();
            EventBean.UserInfo eventBeanUserInfo = new EventBean.UserInfo();
            eventBeanUserInfo.userid = doEmptyValue(user.getId());
            eventBeanUserInfo.address = doEmptyValue(user.getAddressLine());
            eventBeanUserInfo.email = doEmptyValue(user.getEmail());
            eventBeanUserInfo.firstname = doEmptyValue(user.getFirstName());
            eventBeanUserInfo.gender = doEmptyValue(user.getGender() + "");
            eventBeanUserInfo.lastname = doEmptyValue(user.getLastName());
            eventBeanUserInfo.nickname = doEmptyValue(user.getNickName());
            eventBeanUserInfo.zipcode = doEmptyValue(user.getPostCode());

            result.put(EventConstant.USERINFO, eventBeanUserInfo);
            result.put(EventConstant.APPINFO, appInfo);
            result.put(EventConstant.MOBILEINFO, mobileInfo);
            // result.put(EventConstant.EXPAND,GsonUtils.toJson(expand.map));//扩展字段

            if (expand.map != null) {
                expand.map.put(TIMEZONE_KEY, getTimeZone());
                result.put(EventConstant.EXPAND, expand.map.isEmpty() ? new EventBean.Expand() : expand.map);
            } else {
                result.put(EventConstant.EXPAND, new EventBean.Expand());
            }


            CEvent.event(result);


        } catch (Exception e) {
            LogUtils.e(e.getMessage());
        }

    }

    /**
     * 获取当前时间和默认时区
     *
     * @return
     */
    private static String getTimeZone() {
        String defaultTimeZone = "UTC-1";
        String timeZoneStr = "UTC";
        int defaultZone = 3600;
        String offSetIntTag = "+";
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {

            ZoneId zoneId = ZonedDateTime.now().getZone();
            int zoneOffSet = zoneId.getRules().getOffset(Instant.ofEpochSecond(System.currentTimeMillis())).getTotalSeconds();
            int zoneOffSetInt = zoneOffSet / defaultZone;

            if (zoneOffSetInt > 0) {
                return timeZoneStr + offSetIntTag + zoneOffSetInt;
            } else {
                return timeZoneStr + zoneOffSetInt;
            }

        } else {
            return defaultTimeZone;
        }

    }


    public static void sendCustomTrace(String userId, Map<String, String> appClient) {
        final String USER_ID = "user_id";
        String UNLOGIN = "unknow";
        String BASE_URL = "base_url";
        String END_POINT = "end_point";

        String PHONE_BRAND_KEY = "phoneBrandKey";
        String ANDROID_VERSION = "androidVersion";
        String APP_VERSION = "appVersion";
        //分辨率
        String NETWORK = "resolution";

        Map<String, Object> result = new HashMap();

        result.put(EventConstant.DATE, System.currentTimeMillis() + "");
        result.put(EventConstant.TIMESTAMP, System.currentTimeMillis() + "");
        result.put(EventConstant.PLATFORM, "Android");

        if (TextUtils.isEmpty(userId)) {
            result.put(EventConstant.USERID, UNLOGIN);
        } else {
            result.put(EventConstant.USERID, userId);
        }

        //设置必要必要信息
        Map<String, String> appClientKv = new HashMap<>();
        for (Map.Entry<String, String> entry : appClient.entrySet()) {
            appClientKv.put(entry.getKey(), entry.getValue());
        }
        appClientKv.put(USER_ID, userId);


        //补充必要的相关信息
        appClientKv.put(BASE_URL, BuildConfig.EVN);
        appClientKv.put(PHONE_BRAND_KEY, Build.MODEL);
        appClientKv.put(APP_VERSION, AppUtils.getAppVersionName());
        appClientKv.put(ANDROID_VERSION, android.os.Build.VERSION.RELEASE);
        appClientKv.put(END_POINT, BuildConfig.MQTT_END_POINT);
        //-1：没有网络  1：WIFI网络  2：wap网络  3：net网络
        appClient.put(NETWORK, getAPNType(BaseApplication.getInstance()) + "");

        result.put(EventConstant.CUSTOMMAPS, appClientKv);
        //保存处理结果
        CEvent.event(result);

    }


    private static String doEmptyValue(String str) {
        return TextUtils.isEmpty(str) ? "" : str;

    }


    private static int checkPermission(PermissionInfo permissionInfo, String s, Context context) {
        try {
            PackageManager pm = context.getPackageManager();

            boolean permission = (PackageManager.PERMISSION_GRANTED == pm.checkPermission(s, context.getPackageName()));

            if (permission) {
                return 1;
            } else {
                return 3;
            }
        } catch (Exception e) {
            return 3;
        }
    }


    /**
     * 获取当前的网络状态  -1：没有网络  1：WIFI网络  2：wap网络  3：net网络
     */
    public static int getAPNType(Context context) {

        int netType = -1;

        ConnectivityManager connMgr = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        NetworkInfo networkInfo = connMgr.getActiveNetworkInfo();
        if (networkInfo == null) {
            return netType;
        }

        if (null == networkInfo.getExtraInfo()) {
            return netType;
        }


        if (null == networkInfo.getExtraInfo()) {
            return netType;
        }
        int nType = networkInfo.getType();
        if (nType == ConnectivityManager.TYPE_MOBILE) {
            if ("cmnet".equals(networkInfo.getExtraInfo().toLowerCase())) {
                netType = 3;
            } else {
                netType = 3;
            }
        } else if (nType == ConnectivityManager.TYPE_WIFI) {
            netType = 1;
        }
        return netType;
    }


    public static boolean getAccessFileisExist(Context context, String fileName) {
        AssetManager assets = context.getAssets();
        String[] locales = assets.getLocales();
        if (TextUtils.isEmpty(fileName)) {
            return false;
        } else {
            for (String locale : locales) {
                if (locale.equals(fileName)) {
                    return true;
                }
            }
        }
        return false;
    }


    /**
     * unit setting
     */
    public static String getAppSettingOfUnit() {


        try {
            AppSettingEntry appSetting = SoftRoomDatabase.getDatabase(BaseApplication.getInstance()).appSettingDao().getSetting();

            if (null != appSetting) {
                if (!TextUtils.isEmpty(appSetting.getUnit())) {
                    return appSetting.getUnit();
                } else {
                    //未设置单位。根据环境给单位 NA默认英制
                    if (BuildConfig.EVN.contains(NA_TAG)) {
                        return IMPERIAL;

                    }
                }
            } else {
                if (BuildConfig.EVN.contains(NA_TAG)) {
                    return IMPERIAL;
                } else {
                    return METRIC;

                }
            }


        } catch (Throwable e) {
            if (BuildConfig.EVN.contains(NA_TAG)) {
                return IMPERIAL;
            }
        }
        return "";
    }

    /**
     * 日期格式
     * 需求：增加日期显示格式切换：EU所有日期格式改为：DDMMYYYY；NA所有日期格式改为：MMDDYYYY （交互）
     * dayMonthYear    dd-mm-yyyy: dayMonthYear
     * monthDayYear.   mm-dd-yyyy:monthDayYear
     */

    public static String getAppSettingOfDate() {


        if (BuildConfig.EVN.contains(NA_TAG)) {
            return NA_MM_DD_YYYY;
        } else {
            return EU_DAY_MONTH_YEAR;
        }
    }

    /**
     * 日期格式
     * 需求：增加日期显示格式切换：EU所有日期格式改为：DDMMYYYY；NA所有日期格式改为：MMDDYYYY （交互）
     * dayMonthYear    dd-mm-yyyy: dayMonthYear
     * monthDayYear.   mm-dd-yyyy:monthDayYear
     */

    public static String getAppSettingOfHour() {


        if (BuildConfig.EVN.contains(NA_TAG)) {
            return SETTING_OF_HOUR_12;
        } else {
            return SETTING_OF_HOUR_24;
        }
    }

    /**
     * 获取当前环境
     */

    public static String getBuildEvn() {


        if (!BuildConfig.EVN.contains(NA_TAG)) {
            return EVN_EU;
        } else {
            return EVN_NA;
        }
    }

    public static boolean passwordMatches(String content) {

        //原型设计需求--不遵守命名规则
        String Lowercase_characters = "qwertyuiopasdfghjklzxcvbnm";
        String Uppercase_characters = "QWERTYUIOPASDFGHJKLZXCVBNM";
        String Numbers = "0123456789";
        String Exclamation_point = "!";
        String Open_parenthesis = "(";
        String Close_parenthesis = ")";
        String Dash = "-";
        String Period = ".";
        String Question_mark = "?";
        String Open_bracket = "[";
        String Close_bracket = "]";
        String Underscore = "_";
        String Grave_accent = "`";
        String Tilde = "~";
        String Semicolon = ";";
        String Colon = ":";
        String Exclamation_mark = "!";
        String Commercial_at = "@";
        String Number_sign = "#";
        String Dollar_sign = "$";
        String Percent_sign = "%";
        String Circumflex_accent = "^";
        String Ampersand = "&";
        String Asterisk = "*";
        String Plus_sign = "+";
        String Equals_sign = "=";

        if (TextUtils.isEmpty(content)) {
            return false;
        }

        String firstCharacter = content.substring(0, 1);
        //首字母不能为 ‘-’
        if ("-".equals(firstCharacter)) {
            LogUtils.e("passwordMatches", "this character is not supported--- " + Dash);
            return false;
        }
        //首字母不能为 ‘.’
        if (".".equals(firstCharacter)) {
            LogUtils.e("passwordMatches", "this character is not supported--- " + Period);

            return false;
        }

        String regex = "[" +
                Lowercase_characters +
                Uppercase_characters +
                Numbers +
                Exclamation_point +
                Open_parenthesis +
                Close_parenthesis +
                Dash +
                Period +
                Question_mark +
                Open_bracket +
                Close_bracket +
                Underscore +
                Grave_accent +
                Tilde +
                Semicolon +
                Colon +
                Exclamation_mark +
                Commercial_at +
                Number_sign +
                Dollar_sign +
                Percent_sign +
                Circumflex_accent +
                Ampersand +
                Asterisk +
                Plus_sign +
                Equals_sign + "]";

        char[] chars = content.toCharArray();

        boolean result = true;

        for (char c : chars) {
            if (!regex.contains(c + "")) {
                result = false;
            }
        }

        return result;
    }


    /**
     * 校验输入字符和空格
     *
     * @param content
     * @return
     */
    public static boolean nameMatches(String content) {

        if (TextUtils.isEmpty(content)) {
            return false;
        }

        String regex = "[a-zA-Z\\s-]+";

        return content.matches(regex);
    }


    public static String passwordRegiesterMatchesString(String content) {

        String unMatchString = "";

        //原型设计需求--不遵守命名规则
        String Lowercase_characters = "qwertyuiopasdfghjklzxcvbnm";
        String Uppercase_characters = "QWERTYUIOPASDFGHJKLZXCVBNM";
        String Numbers = "0123456789";
        String Exclamation_point = "!";
        String Open_parenthesis = "(";
        String Close_parenthesis = ")";
        String Dash = "-";
        String Period = ".";
        String Question_mark = "?";
        String Open_bracket = "[";
        String Close_bracket = "]";
        String Underscore = "_";
        String Grave_accent = "`";
        String Tilde = "~";
        String Semicolon = ";";
        String Colon = ":";
        String Exclamation_mark = "!";
        String Commercial_at = "@";
        String Number_sign = "#";
        String Dollar_sign = "$";
        String Percent_sign = "%";
        String Circumflex_accent = "^";
        String Ampersand = "&";
        String Asterisk = "*";
        String Plus_sign = "+";
        String Equals_sign = "=";

        if (TextUtils.isEmpty(content)) {
            return "";
        }


        String regex = "[" +
                Lowercase_characters +
                Uppercase_characters +
                Numbers +
                Exclamation_point +
                Open_parenthesis +
                Close_parenthesis +
                Dash +
                Period +
                Question_mark +
                Open_bracket +
                Close_bracket +
                Underscore +
                Grave_accent +
                Tilde +
                Semicolon +
                Colon +
                Exclamation_mark +
                Commercial_at +
                Number_sign +
                Dollar_sign +
                Percent_sign +
                Circumflex_accent +
                Ampersand +
                Asterisk +
                Plus_sign +
                Equals_sign + "]";

        char[] chars = content.toCharArray();


        for (char c : chars) {
            if (!regex.contains(c + "")) {
                if (TextUtils.isEmpty(unMatchString)) {
                    unMatchString = unMatchString + "“" + c + "”";
                } else {
                    unMatchString = unMatchString + ",“" + c + "”";
                }

            }
        }

        return unMatchString;
    }

    /**
     * 获取当前环境
     * （1）北美格式：MM/DD/YYYY hh:mm am/pm
     * （2）欧洲格式：DD/MM/YYYY hh:mm 24小时制
     */

    public static String getTimeForHour(Long times) {

        if (null == times) {
            return "";
        }
        if (0 == times) {
            return "";
        }

        if (BuildConfig.EVN.contains(NA_TAG)) {

            Date date = new Date(times);


            String hour = date.getHours() < 12 ? " am" : " pm";


            String millis2String = TimeUtils.millis2String(times, "MM/dd/yyyy hh:mm");

            //--北美格式：MM/DD/YYYY hh:mm am/pm
            return millis2String + hour;
        } else {
            //--欧洲格式：DD/MM/YYYY hh:mm 24小时制
            return TimeUtils.millis2String(times, "dd/MM/yyyy HH:mm");
        }
    }


    /**
     * 获取当前环境北美/欧洲
     */

    public static String getRegion() {


        if (BuildConfig.EVN.contains(NA_TAG)) {
            return EVN_NA;
        } else {
            return EVN_EU;
        }
    }


    public static boolean checkGPSIsOpen() {
        LocationManager locationManager = (LocationManager) BaseApplication.getInstance().getSystemService(Context.LOCATION_SERVICE);
        boolean gps = locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER);
        boolean network = locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER);
        if (gps || network) {
            return true;
        }
        return false;
    }

    /**
     * 用于标识同一请求响应 20位数据 timestamp（毫秒）+7位随机数
     *
     * @return
     */
    public static String getMessageId() {
        String currentTime = System.currentTimeMillis() + "";

        Random random = new Random();
        int randomNumber = random.nextInt(9999999) % 10000000;

        currentTime = currentTime + String.valueOf(randomNumber);

        return currentTime;

    }

    @NonNull
    public static String hexString2String(String hexSnCodeStr) {
        String str = "0123456789ABCDEF";
        char[] hexs = hexSnCodeStr.toCharArray();
        byte[] bytes = new byte[hexSnCodeStr.length() / 2];
        int n;
        for (int i = 0; i < bytes.length; i++) {
            n = str.indexOf(hexs[2 * i]) * 16;
            n += str.indexOf(hexs[2 * i + 1]);
            bytes[i] = (byte) (n & 0xff);
        }
        return new String(bytes);
    }
    // TODO Z6   Z6设备id 转换为绑定接口可使用的

    public static String z6DeviceIdAppendWithZero(String numberStr1) {
        Integer number1 = ConvertUtils.hexString2Int(numberStr1);
        // 长度为1 需要补0  ，例如 1 补 为 01
        if (number1.toString().length() == 1) {
            return ADD_0 + number1;
        }
        return number1 + "";
    }

    // TODO Z6   Z6 获取
    public static String z6DeviceIdHex2digit(String numberStr1) {
        String numberStr = ConvertUtils.hexString2Int(numberStr1) + "";
        if (numberStr.length() == 5) {
            return numberStr;
        }
        int length = numberStr.length();
        for (int i = 0; i < 5 - length; i++) {
            numberStr = ADD_0 + numberStr;
        }
        return numberStr;
    }


    public static void goIntentSetting(Activity activity) {
        Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
        Uri uri = Uri.fromParts("package", activity.getPackageName(), null);
        intent.setData(uri);
        try {
            activity.startActivity(intent);
        } catch (Exception e) {
            LogUtils.e("Utils", "跳转App设置页面异常");
        }
    }

    /**
     * 检查List是否为空
     *
     * @param list
     * @return
     */
    public static boolean isNullOrEmpty(List<?> list) {
        return list == null || list.isEmpty();
    }

    /**
     * 将en转换成en-GB或者en-US
     *
     * @param langCode
     * @return
     */
    public static String enToUSAndGB(String langCode) {
        String langCodeFormat = langCode;
        if (langCode.equalsIgnoreCase(Utils.NA_LANGUAGE)) {
            if (BuildConfig.EVN.equalsIgnoreCase(Utils.NA_TAG)) {
                langCodeFormat = Utils.NA_DEFAULT_LANG_CODE;
            } else if (BuildConfig.EVN.equalsIgnoreCase(Utils.EVN_EU)) {
                langCodeFormat = Utils.EU_DEFAULT_LANG_CODE;
            }
        }
        return langCodeFormat;
    }

    /**
     * 将en-GB或者en-US转换成en
     *
     * @param langCode
     * @return
     */
    public static String uSAndGBToEn(String langCode) {
        String langCodeFormat = langCode;
        if (langCode.equalsIgnoreCase(Utils.NA_DEFAULT_LANG_CODE) || langCode.equalsIgnoreCase(Utils.EU_DEFAULT_LANG_CODE)) {
            langCodeFormat = Utils.NA_LANGUAGE;
        }
        return langCodeFormat;
    }

    /**
     * 把$$切换成换行字符
     *
     * @param content
     * @return
     */
    public static String exchangeString(String content) {
        String BLANK_SPACE = "$$";
        String returnContent = "";
        try {
            if (TextUtils.isEmpty(content)) {
                return returnContent;
            }

            returnContent = content.replace(BLANK_SPACE, "\n");

            return returnContent;
        } catch (Exception e) {
            return returnContent;
        }

    }

}
