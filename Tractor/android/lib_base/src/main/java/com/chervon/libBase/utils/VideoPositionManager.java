package com.chervon.libBase.utils;

import android.content.Context;
import android.content.SharedPreferences;
import com.blankj.utilcode.util.LogUtils;

/**
 * 视频播放位置管理器
 */
public class VideoPositionManager {
    private static final String TAG = "VideoPositionManager";
    private static final String PREF_NAME = "video_position_prefs";
    private static final String POSITION_PREFIX = "video_position_";
    private final SharedPreferences preferences;

    public VideoPositionManager(Context context) {
        preferences = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
    }

    /**
     * 保存视频播放位置
     * @param videoId 视频ID
     * @param position 播放位置（毫秒）
     */
    public void savePosition(String videoId, long position) {
        if (videoId == null) return;
        
        preferences.edit()
                .putLong(POSITION_PREFIX + videoId, position)
                .apply();
        LogUtils.i(TAG, "保存视频播放位置：videoId=" + videoId + ", position=" + position);
    }

    /**
     * 获取视频播放位置
     * @param videoId 视频ID
     * @return 上次播放位置（毫秒），如果没有记录则返回0
     */
    public long getPosition(String videoId) {
        if (videoId == null) return 0;
        
        long position = preferences.getLong(POSITION_PREFIX + videoId, 0);
        LogUtils.i(TAG, "获取视频播放位置：videoId=" + videoId + ", position=" + position);
        return position;
    }

    /**
     * 清除指定视频的播放位置记录
     * @param videoId 视频ID
     */
    public void clearPosition(String videoId) {
        if (videoId == null) return;
        
        preferences.edit()
                .remove(POSITION_PREFIX + videoId)
                .apply();
        LogUtils.i(TAG, "清除视频播放位置：videoId=" + videoId);
    }

    /**
     * 清除所有视频的播放位置记录
     */
    public void clearAllPositions() {
        preferences.edit().clear().apply();
        LogUtils.i(TAG, "清除所有视频播放位置");
    }
}
