package com.chervon.libBase.utils;


import android.annotation.SuppressLint;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothManager;
import android.bluetooth.BluetoothProfile;
import android.content.Context;
import android.content.pm.PackageManager;
import android.os.Build;

import java.util.List;
import java.util.Set;

/**
 * @Author: 184862
 * @CreateDate: 2025/3/17
 * @UpdateDate: 2025/3/18
 */
public class BluetoothUtils {

    /**
     * 检查设备是否支持蓝牙
     */
    public static boolean isBluetoothSupported(Context context) {
        return context.getPackageManager().hasSystemFeature(PackageManager.FEATURE_BLUETOOTH);
    }

    /**
     * 获取BluetoothAdapter
     */
    public static BluetoothAdapter getBluetoothAdapter(Context context) {
        BluetoothAdapter bluetoothAdapter;

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2) {
            // Android 4.3及以上使用BluetoothManager
            BluetoothManager bluetoothManager = (BluetoothManager) context.getSystemService(Context.BLUETOOTH_SERVICE);
            bluetoothAdapter = bluetoothManager.getAdapter();
        } else {
            // 较旧版本直接获取
            bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
        }

        return bluetoothAdapter;
    }

    /**
     * 检查蓝牙是否可用（设备支持蓝牙且蓝牙已开启）
     */
    public static boolean isBluetoothAvailable(Context context) {
        // 检查设备是否支持蓝牙
        if (!isBluetoothSupported(context)) {
            return false;
        }

        // 获取蓝牙适配器
        BluetoothAdapter bluetoothAdapter = getBluetoothAdapter(context);

        // 检查是否获取到蓝牙适配器并且蓝牙已开启
        return bluetoothAdapter != null && bluetoothAdapter.isEnabled();
    }

    /**
     * 根据MAC地址判断当前设备是否被连接
     *
     * @param context 上下文
     * @param macAddress 设备MAC地址
     * @return 如果设备已连接返回true，否则返回false
     */
    @SuppressLint("MissingPermission")
    public static boolean isDeviceConnected(Context context, String macAddress) {
        if (macAddress == null || macAddress.isEmpty()) {
            return false;
        }

        BluetoothAdapter bluetoothAdapter = getBluetoothAdapter(context);
        if (bluetoothAdapter == null || !bluetoothAdapter.isEnabled()) {
            return false;
        }

        // 方法1: 通过已配对设备和连接状态检查
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2) {
            BluetoothManager bluetoothManager = (BluetoothManager) context.getSystemService(Context.BLUETOOTH_SERVICE);
            if (bluetoothManager != null) {
                // 获取已连接的设备
                try {
                    // 检查各种蓝牙配置文件的连接状态
                    int[] profiles = {BluetoothProfile.GATT, BluetoothProfile.GATT_SERVER};
                    for (int profile : profiles) {
                        // 获取连接到指定配置文件的设备列表
                        @SuppressLint("MissingPermission") List<BluetoothDevice> connectedDevices = bluetoothManager.getConnectedDevices(profile);
                        for (BluetoothDevice device : connectedDevices) {
                            if (macAddress.equalsIgnoreCase(device.getAddress())) {
                                return true; // 找到匹配的MAC地址
                            }
                        }
                    }
                } catch (SecurityException e) {
                    // 处理可能的权限问题
                    e.printStackTrace();
                }
            }
        }

        // 方法2: 通过已配对设备检查(作为备选方案，不能100%确定连接状态)
        try {
            @SuppressLint("MissingPermission") Set<BluetoothDevice> bondedDevices = bluetoothAdapter.getBondedDevices();
            if (bondedDevices != null) {
                for (BluetoothDevice device : bondedDevices) {
                    if (macAddress.equalsIgnoreCase(device.getAddress())) {
                        // 注意：这只能确认设备是配对的，不能100%确认是连接的
                        // 如果设备已配对且isConnected()返回true，则设备很可能是已连接的
                        return device.getBondState() == BluetoothDevice.BOND_BONDED;
                    }
                }
            }
        } catch (SecurityException e) {
            // 处理可能的权限问题
            e.printStackTrace();
        }

        return false;
    }
}
