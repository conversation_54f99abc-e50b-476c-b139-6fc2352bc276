package com.chervon.libBase.utils;

import android.text.TextUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.TimeZone;

/**
 * 时间转换工具
 */
public class TimestampToUTCConverter {

    static final String NA_TIME_TYPE = "MM/dd/yyyy";
    static final String EU_TIME_TYPE = "dd/MM/yyyy";

    /**
     * 将当前时间戳转换成UTC时间
     * @param timestamp
     * @return
     */
    public static long convertToUTC(String timestamp) {
        //默认设置系统时间
        long utcTimeStamp = System.currentTimeMillis();

        //如果传入时间戳为空。设置当前系统时间
        if (TextUtils.isEmpty(timestamp)) {
            return System.currentTimeMillis();
        }

        SimpleDateFormat sdf = new SimpleDateFormat(
                Utils.getBuildEvn().equals(Utils.EVN_NA) ?
                NA_TIME_TYPE :
                EU_TIME_TYPE);
        sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
        Date date = null;
        try {
            date = sdf.parse(timestamp);
            utcTimeStamp = date.getTime();
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }

        return utcTimeStamp;
    }

    /**
     * 将时间戳转换为字符串格式
     * @param timestamp 时间戳
     * @return 格式化后的时间字符串，如果输入为空则返回空字符串
     */
    public static String convertTimestampToString(Long timestamp) {
        if (timestamp == null) {
            return "";
        }

        SimpleDateFormat sdf = new SimpleDateFormat(
                Utils.getBuildEvn().equals(Utils.EVN_NA) ?
                NA_TIME_TYPE :
                EU_TIME_TYPE);
        return sdf.format(new Date(timestamp));
    }

}
