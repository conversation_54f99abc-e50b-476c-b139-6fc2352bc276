package com.chervon.libBase.ui.widget;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.text.Editable;
import android.text.InputFilter;
import android.text.InputType;
import android.text.Selection;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.text.method.HideReturnsTransformationMethod;
import android.text.method.PasswordTransformationMethod;
import android.util.AttributeSet;
import android.util.Log;
import android.view.ActionMode;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import androidx.core.content.ContextCompat;

import com.blankj.utilcode.util.KeyboardUtils;
import com.blankj.utilcode.util.LogUtils;
import com.chervon.libBase.R;

import java.util.ArrayList;
import java.util.List;
import java.util.zip.Inflater;

/**
 * Created by SongSenior on 2021/4/16
 * 一个 EditText 和 n 个 TextView
 */
public class VerifyEditText extends LinearLayout {
  protected static final String TAG = "VerifyEditText";
  //默认 item 个数为 4 个
  private final static int DEFAULT_ITEM_COUNT = 4;
  //默认每个 item 的宽度为 100
  private final static int DEFAULT_ITEM_WIDTH = 100;
  //默认每个 item 的间距为 50
  private final static int DEFAULT_ITEM_MARGIN = 50;
  //默认每个 item 的字体大小为 14
  private final static int DEFAULT_ITEM_TEXT_SIZE = 14;
  //默认密码明文显示时间为 200ms，之后密文显示
  private final static int DEFAULT_PASSWORD_VISIBLE_TIME = 200;

  private final List<TextView> mTextViewList = new ArrayList<>();
  private EditText mEditText;
  private Drawable drawableNormal, drawableSelected;
  private Context mContext;
  int currentPosition = -1;
  int count;
  //输入完成监听
  private InputCompleteListener mInputCompleteListener;

  public VerifyEditText(Context context) {
    this(context, null);
  }

  public VerifyEditText(Context context, @Nullable AttributeSet attrs) {
    this(context, attrs, 0);
  }

  public VerifyEditText(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
    super(context, attrs, defStyleAttr);
    init(context, attrs);
  }

  private void init(Context context, @Nullable AttributeSet attrs) {
    mContext = context;
    setOrientation(HORIZONTAL);
    setGravity(Gravity.CENTER);
    @SuppressLint("CustomViewStyleable") TypedArray obtainStyledAttributes =
      getContext().obtainStyledAttributes(attrs, R.styleable.verify_EditText);
    drawableNormal = obtainStyledAttributes.getDrawable(R.styleable.verify_EditText_verify_background_normal);
    drawableSelected = obtainStyledAttributes.getDrawable(R.styleable.verify_EditText_verify_background_selected);
    int textColor = obtainStyledAttributes.getColor(R.styleable.verify_EditText_verify_textColor,
      ContextCompat.getColor(context, android.R.color.black));
    count = obtainStyledAttributes.getInt(R.styleable.verify_EditText_verify_count, DEFAULT_ITEM_COUNT);
    int inputType = obtainStyledAttributes.getInt(R.styleable.verify_EditText_verify_inputType, InputType.TYPE_CLASS_NUMBER);
    int passwordVisibleTime = obtainStyledAttributes.getInt(R.styleable.verify_EditText_verify_password_visible_time, DEFAULT_PASSWORD_VISIBLE_TIME);
    int width = (int) obtainStyledAttributes.getDimension(R.styleable.verify_EditText_verify_width, DEFAULT_ITEM_WIDTH);
    int height = (int) obtainStyledAttributes.getDimension(R.styleable.verify_EditText_verify_height, 0);
    int margin = (int) obtainStyledAttributes.getDimension(R.styleable.verify_EditText_verify_margin, DEFAULT_ITEM_MARGIN);
    float textSize = px2sp(context, obtainStyledAttributes.getDimension(R.styleable.verify_EditText_verify_textSize, sp2px(context, DEFAULT_ITEM_TEXT_SIZE)));
    boolean password = obtainStyledAttributes.getBoolean(R.styleable.verify_EditText_verify_password, false);
    obtainStyledAttributes.recycle();
    if (count < 2) {
      //最少 2 个 item
      count = 2;
    }

    mEditText = new EditText(context);
    mEditText.setInputType(inputType);
    mEditText.setLayoutParams(new LinearLayout.LayoutParams(1, 1));
    mEditText.setCursorVisible(false);
    mEditText.setBackground(null);
    mEditText.setOnKeyListener(new OnKeyListener() {
      @Override
      public boolean onKey(View v, int keyCode, KeyEvent event) {
        return false;
      }
    });
    //限制输入长度为 count
    mEditText.setFilters(new InputFilter[]{new InputFilter.LengthFilter(count)});
    mEditText.addTextChangedListener(new TextWatcher() {
      @Override
      public void beforeTextChanged(CharSequence s, int start, int count, int after) {

      }

      @Override
      public void onTextChanged(CharSequence s, int start, int before, int count) {
        TextView textView = mTextViewList.get(start);
        if (before == 0) {
          CharSequence input = s.subSequence(start, s.length());
          textView.setText(input);
          if (password) {
            textView.setTransformationMethod(HideReturnsTransformationMethod.getInstance());
            //passwordVisibleTime 毫秒后设置为密文显示
            textView.postDelayed(() ->
                textView.setTransformationMethod(PasswordTransformationMethod.getInstance()),
              passwordVisibleTime);
          }
          setTextViewBackground(textView, drawableSelected);
        } else {
          textView.setText("");
          setTextViewBackground(textView, drawableNormal);
        }
        if (mInputCompleteListener != null && s.length() == mTextViewList.size()) {
          mInputCompleteListener.complete(s.toString());
        } else {

        }
      }

      @Override
      public void afterTextChanged(Editable s) {

      }
    });
    addView(mEditText);

    //点击弹出软键盘
    setOnClickListener(v -> {
      mEditText.requestFocus();
      showSoftKeyBoard();
    });


    //遍历生成 textview
    for (int i = 0; i < count; i++) {
      VerifyEditView textView = (VerifyEditView) LayoutInflater.from(getContext()).inflate(R.layout.base_verify_et_item, null);
      textView.setOnPasteCallback(new VerifyEditView.OnPasteCallback() {
        @Override
        public void onPause(String clipboard) {
          if (null!= mInputCompleteListener){
            mInputCompleteListener.onPauseFromView(clipboard);
          }
        }
      });

      textView.setTextSize(textSize);
      textView.setGravity(Gravity.CENTER);
      //  textView.setTextColor(textColor);
      LayoutParams layoutParams = new LayoutParams(width, height == 0 ? ViewGroup.LayoutParams.WRAP_CONTENT : height);
      if (i == 0) {
        layoutParams.leftMargin = -1;
      } else {
        layoutParams.leftMargin = margin;
      }

      textView.setLayoutParams(layoutParams);
      //    setTextViewBackground(textView, drawableNormal);
      addView(textView);
      mTextViewList.add(textView);
      textView.setTag(i);

      textView.setOnKeyListener(new OnKeyListener() {
        @Override
        public boolean onKey(View v, int keyCode, KeyEvent event) {

          if (event.getAction() == KeyEvent.ACTION_DOWN) {
            if (keyCode == KeyEvent.KEYCODE_DEL) {
              EditText editText = (EditText) v;
              if (currentPosition > 0) {
                if (TextUtils.isEmpty(editText.getText().toString())) {
                  v.clearFocus();
                  mTextViewList.get((currentPosition - 1) % mTextViewList.size()).setText("");
                  mTextViewList.get((currentPosition - 1) % mTextViewList.size()).requestFocus();
                }
              }
            }
          }

          return false;
        }
      });



      textView.setOnFocusChangeListener(new OnFocusChangeListener() {
        @RequiresApi(api = Build.VERSION_CODES.M)
        @SuppressLint("ResourceAsColor")
        @Override
        public void onFocusChange(View v, boolean hasFocus) {
          if (hasFocus) {
            EditText editText = (EditText) v;
            Object indexObj = editText.getTag();
            if (indexObj != null) {
              int index = (int) indexObj;
              if (index == 0) {
                KeyboardUtils.showSoftInput();
              }
            }
            currentPosition = (int) v.getTag();
            setDrawableNormal();
          }
        }
      });

      if (i == 0) {
        textView.requestFocus();
      }
      textView.addTextChangedListener(new TextWatcher() {
        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {
          int position = (int) textView.getTag();
          if (start == 1 && after == 1) {
            // textView.setText(s.charAt(s.length()-1)+"");
          }
        }

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
          if (start == 1) {
            textView.setText(s.charAt(s.length() - 1) + "");
          } else if (s.length() == 2) {
            textView.setText(s.charAt(0) + "");
          }
          Editable text = textView.getText();
          if (text != null) {
            Selection.setSelection(text, text.toString().length());
          }

        }

        @Override
        public void afterTextChanged(Editable s) {
          int position = (int) textView.getTag();
          if (!TextUtils.isEmpty(s)) {
            if (position >= mTextViewList.size() - 1) {
              KeyboardUtils.hideSoftInput(textView);
              textView.clearFocus();
              //   mTextViewList.get(position).requestFocus();

            } else {
              textView.clearFocus();
              mTextViewList.get((position + 1) % mTextViewList.size()).requestFocus();

            }
            String content = getContentAll();
            if (mInputCompleteListener != null && content.length() == count) {
              mInputCompleteListener.complete(getContentAll());
            } else {
              mInputCompleteListener.process();
            }

          } else {
            mInputCompleteListener.process();
          }

//                    else{
//                        if(position>0){
//                            textView.clearFocus();
//                            mTextViewList.get((position-1)%mTextViewList.size()).requestFocus();
//                        }
//                    }
        }
      });
    }
  }

  /**
   * view 添加到窗口时，延迟 500ms 弹出软键盘
   */
  @Override
  protected void onAttachedToWindow() {
    super.onAttachedToWindow();
    mEditText.postDelayed(this::showSoftKeyBoard, 500);
  }

  /**
   * 设置背景
   *
   * @param textView
   * @param drawable
   */
  private void setTextViewBackground(TextView textView, Drawable drawable) {
    if (drawable != null) {
      textView.setBackground(drawable);
    }
  }

  /**
   * 获取当前输入的内容
   *
   * @return
   */
  public String getContent() {
    Editable text = mEditText.getText();
    if (TextUtils.isEmpty(text)) {
      return "";
    }
    return mEditText.getText().toString();
  }

  /**
   * 清除内容
   */
  public void clearContent() {
    mEditText.setText("");
    for (int i = 0; i < mTextViewList.size(); i++) {
      TextView textView = mTextViewList.get(i);
      textView.setText("");
      setTextViewBackground(textView, drawableNormal);
    }
  }

  /**
   * 设置默认的内容
   *
   * @param content
   */
  public void setDefaultContent(String content) {
    mEditText.setText(content);
    mEditText.requestFocus();
    char[] chars = content.toCharArray();
    int min = Math.min(chars.length, mTextViewList.size());
    for (int i = 0; i < min; i++) {
      char aChar = chars[i];
      String s = String.valueOf(aChar);
      TextView textView = mTextViewList.get(i);
      textView.setText(s);
      setTextViewBackground(textView, drawableSelected);
    }
    if (mInputCompleteListener != null && min == mTextViewList.size()) {
      mInputCompleteListener.complete(content.substring(0, min));
    }
  }

  public String getContentAll() {
    String content = "";
    for (int i = 0; i < mTextViewList.size(); i++) {

      String s = mTextViewList.get(i).getText().toString();
      content += s;
    }
    return content;
  }

  /**
   * 显示软键盘
   */
  private void showSoftKeyBoard() {
    InputMethodManager imm = (InputMethodManager) mContext.getSystemService(Context.INPUT_METHOD_SERVICE);
    imm.showSoftInput(mEditText, InputMethodManager.SHOW_FORCED);
  }

  /**
   * 添加输入完成的监听
   *
   * @param inputCompleteListener
   */
  public void addInputCompleteListener(InputCompleteListener inputCompleteListener) {
    mInputCompleteListener = inputCompleteListener;
//        Editable content = mEditText.getText();
//        if (!TextUtils.isEmpty(content) && content.toString().length() == mTextViewList.size()) {
//            mInputCompleteListener.complete(content.toString());
//        }
  }

  public void setInvalidColor() {
    clearContent();
    for (int i = 0; i < mTextViewList.size(); i++) {
      mTextViewList.get(i).setBackgroundResource(R.drawable.module_login_et_invalid_shape);
      mTextViewList.get(i).clearFocus();
    }
  }

  public void setDrawableNormal() {
    for (int i = 0; i < mTextViewList.size(); i++) {
      mTextViewList.get(i).setBackgroundResource(R.drawable.base_et_selector);
    }
  }

  public interface InputCompleteListener {
    void complete(String content);
    void onPauseFromView(String content);
    void process();
  }

  private int px2sp(Context context, float pxValue) {
    final float fontScale = context.getResources().getDisplayMetrics().scaledDensity;
    return (int) (pxValue / fontScale + 0.5f);
  }

  private int sp2px(Context context, float spValue) {
    final float fontScale = context.getResources().getDisplayMetrics().scaledDensity;
    return (int) (spValue * fontScale + 0.5f);
  }


  @Override
  public boolean onKeyDown(int keyCode, KeyEvent event) {
    if (keyCode == KeyEvent.KEYCODE_DEL) {
      if (currentPosition > 0) {
        mTextViewList.get(currentPosition).clearFocus();
        mTextViewList.get((currentPosition - 1) % mTextViewList.size()).requestFocus();
      }
    }
    return super.onKeyDown(keyCode, event);
  }
}
