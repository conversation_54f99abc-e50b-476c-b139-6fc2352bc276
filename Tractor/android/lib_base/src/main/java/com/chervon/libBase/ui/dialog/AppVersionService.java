package com.chervon.libBase.ui.dialog;

import android.app.Activity;
import android.app.Dialog;
import android.content.ActivityNotFoundException;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.net.Uri;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.databinding.DataBindingUtil;

import com.blankj.utilcode.util.LogUtils;
import com.bumptech.glide.Glide;
import com.chervon.libBase.BaseApplication;
import com.chervon.libBase.R;
import com.chervon.libBase.databinding.DialogAppversionUpgradeConfirmBinding;
import com.chervon.libBase.utils.AppConfig;
import com.chervon.libBase.utils.AppUtils;
import com.chervon.libDB.SoftRoomDatabase;
import com.chervon.libDB.dao.AppVersionEntryDao;
import com.chervon.libDB.entities.AppVersionEntry;

/**
 * @Author: 184862
 * @CreateDate: 2025/5/14
 * @UpdateDate: 2025/5/14
 * @desc 检查当前App是否需要升级
 */
public class AppVersionService {

    private final String TAG = "AppVersionService";
    private static AppVersionService instance = null;
    private Dialog upgradeDialog;
    private Activity mActivity;
    private AppVersionEntry appVersionData;
    final int SHOW_FORCE_DIALOG_TAG = 1;
    public boolean isShowing = false;

    public static AppVersionService getInstance() {
        if (null == instance) {
            synchronized (AppVersionService.class) {
                if (null == instance) {
                    instance = new AppVersionService();
                } else {
                    return instance;
                }
            }
        }
        return instance;
    }


    public void simpleConfirmDialog(Activity activity, AppVersionEntry appVersionEntry) {
        this.mActivity = activity;
        this.appVersionData = appVersionEntry;


        DialogAppversionUpgradeConfirmBinding dialogAppUpgradeConfirmBinding = null;

        try {

            if (!getShowDialogResult()){
                return;
            }



            if (upgradeDialog == null || dialogAppUpgradeConfirmBinding == null) {
                //如果dialog显示就退出
                if (null!=upgradeDialog){
                    if (upgradeDialog.isShowing()){
                        return;
                    }
                }

                upgradeDialog = new Dialog(activity);

                dialogAppUpgradeConfirmBinding = DataBindingUtil.inflate(LayoutInflater.from(activity), R.layout.dialog_appversion_upgrade_confirm, null, false);

                upgradeDialog.setContentView(dialogAppUpgradeConfirmBinding.getRoot());
                upgradeDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
                upgradeDialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);

                if (null != appVersionData) {
                    dialogAppUpgradeConfirmBinding.tvVersionName.setText(TextUtils.isEmpty(appVersionData.getVersionName()) ? "" : appVersionData.getVersionName());
                }

                Glide.with(BaseApplication.getInstance()).load(AppConfig.getAppIconRound2()).into(dialogAppUpgradeConfirmBinding.imgLogo);
                dialogAppUpgradeConfirmBinding.getRoot().findViewById(R.id.img_appversion_dismiss).setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (null == upgradeDialog) {
                            return;
                        }
                        upgradeDialog.dismiss();
                        upgradeDialog = null;
                        isShowing = false;
                        upgradeDb();

                    }
                });
            }

            dialogAppUpgradeConfirmBinding.tvCancel.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (null == upgradeDialog) {
                        return;
                    }
                    upgradeDialog.dismiss();
                    upgradeDialog = null;
                    isShowing = false;
                    upgradeDb();

                }
            });
            dialogAppUpgradeConfirmBinding.tvConfirm.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (null != upgradeDialog) {
                        upgradeDialog.dismiss();
                        upgradeDialog = null;
                        isShowing = false;
                    }
                    upgradeDb();
                    intentGoogleMarket();

                }
            });

            upgradeDialog.show();
            isShowing = true;
            upgradeDialog.setOnKeyListener(new DialogInterface.OnKeyListener() {
                @Override
                public boolean onKey(DialogInterface dialogInterface, int keyCode, KeyEvent event) {
                    if (keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_DOWN) {
                        if (null!=upgradeDialog){
                            upgradeDialog.dismiss();
                            isShowing = false;
                        }
                        return true;
                    }

                    return false;
                }
            });
        } catch (Exception e) {
            LogUtils.d(e.getMessage());
        }

    }

    private void intentGoogleMarket() {
        try {
            if (null == mActivity) {
                return;
            }

            String packageNameNa = "com.chervon.connect.na";
            String packageNameEu = "com.chervon.connect.eu";
            String packageNameAnz = "com.chervon.connect.anz";

            String packageName = "";
            if (AppConfig.featureIsNA()) {
                packageName = packageNameNa;
            } else if (AppConfig.featureIsEU()) {
                packageName = packageNameEu;
            } else if (AppConfig.featureIsANZ()) {
                packageName = packageNameAnz;
            }
            if (TextUtils.isEmpty(packageName)) {
                return;
            }
            Intent intent = new Intent(Intent.ACTION_VIEW);
            intent.setData(Uri.parse("market://details?id=" + packageName));
            intent.setPackage("com.android.vending");
            if (intent.resolveActivity(mActivity.getPackageManager()) != null) {
                mActivity.startActivity(intent);
            } else {
                Intent intent2 = new Intent(Intent.ACTION_VIEW);
                intent2.setData(Uri.parse("https://play.google.com/store/apps/details?id=" + packageName));
                if (intent2.resolveActivity(mActivity.getPackageManager()) != null) {
                    mActivity.startActivity(intent2);
                }
            }
        } catch (ActivityNotFoundException activityNotFoundException1) {
            LogUtils.e("GoogleMarket Intent not found");
        }
    }

    /**
     * 检查是否显示当前dialog
     * @return
     */
    private boolean getShowDialogResult(){

        int forceDialog = appVersionData.getForceDialog();

        //后台控制不显示，那么就直接过滤
        if (SHOW_FORCE_DIALOG_TAG != forceDialog){
           return false;
        }

        int appVersionCodeByNet = appVersionData.getVersionCode();
        int appVersionCode = AppUtils.getAppVersionCode();
        long currentTime = System.currentTimeMillis();
        //间隔时间14天
        int diffDay = 14*24*60*60*1000;




        //低于当前App的VersionCode 不显示Dialog--后台配置错误
        if (appVersionCode >= appVersionCodeByNet){
            return false;
        }

        //从数据库获取缓存
        AppVersionEntry versionEntryDb = SoftRoomDatabase.getDatabase(mActivity).appVersionDao().getAppVersion();
        //缓存记录是空的,显示dialog
        if (null == versionEntryDb){
            return true;
        }
        //相同版本对比
        if (versionEntryDb.getVersionCode() == appVersionCodeByNet){
            if ((currentTime - Long.parseLong(versionEntryDb.getLastCheckVersionTime()))>diffDay){
                //满足相同版本，且14天--返回true 显示dialog
                return true;
            }else {
                return false;
            }
        }else {
            //大于当前本地存储版本,显示dialog
            return true;
        }


    }

    public boolean getAppVersionShowStatus(){
      return isShowing;
    }

    /**
     * 更新本地db
     */
    private void upgradeDb(){

        AppVersionEntry versionEntry = SoftRoomDatabase.getDatabase(mActivity).appVersionDao().getAppVersion();
        String defaultId = "1";
        //如果存在更新下时间
        if (null!=versionEntry){
            versionEntry.setVersionName(appVersionData.getVersionName());
            versionEntry.setVersionCode(appVersionData.getVersionCode());
            versionEntry.setLastCheckVersionTime(System.currentTimeMillis()+"");
            versionEntry.setForceDialog(appVersionData.getForceDialog());
            SoftRoomDatabase.getDatabase(mActivity).appVersionDao().updateAppVersion(versionEntry);
        }else {
            //不存在插入新对象且更新最新时间
            if (appVersionData!=null){
                appVersionData.setId(defaultId);
                appVersionData.setLastCheckVersionTime(System.currentTimeMillis()+"");
            }
            SoftRoomDatabase.getDatabase(mActivity).appVersionDao().insertAppVersion(appVersionData);
        }
    }

}
