package com.chervon.libBase.utils.mlang;

import android.content.Context;
import android.content.SharedPreferences;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.os.Build;
import android.os.LocaleList;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.blankj.utilcode.util.LanguageUtils;
import com.blankj.utilcode.util.LogUtils;
import com.chervon.libBase.BaseApplication;
import com.chervon.libBase.BuildConfig;
import com.chervon.libBase.utils.Utils;
import com.chervon.libDB.SoftRoomDatabase;
import com.chervon.libDB.entities.LanguageInfo;
import com.chervon.libDB.entities.LanguagePackage;
import com.timecat.component.locale.LangAction;
import com.timecat.component.locale.LocaleInfo;
import com.timecat.component.locale.MLang;
import com.timecat.component.locale.Util;
import com.timecat.component.locale.model.LangPackLanguage;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

/**
 * @ProjectName: app
 * @Package: com.chervon.libBase.utils.mlang
 * @ClassName: MyLang
 * @Description: 动态多语言的隔离层
 * @Author: LangMeng
 * @CreateDate: 2022/4/21 19:19
 * @UpdateUser: LangMeng
 * @UpdateDate: 2022/4/21 19:19
 * @UpdateRemark: new class
 * @Version: 1.0
 */
public class MyLang {

  private static File filesDir;

  private static LangAction action = new MyLangAction();
  private static Context mContext = null;
  private static final String BLANK_SPACE = "$$";

  public static void init(@NonNull Context applicationContext) {
    mContext = applicationContext;
    filesDir = getFilesDirFixed(getContext());
    try {
      getInstance(applicationContext);
    } catch (Exception e) {
      e.printStackTrace();
    }
  }

  public static void saveLanguageKeyInLocal(String language) {
    SharedPreferences preferences = getContext().getSharedPreferences("language_locale", Context.MODE_PRIVATE);
    SharedPreferences.Editor editor = preferences.edit();
    editor.putString("language", language);
    editor.apply();
  }

  @Nullable
  public static String loadLanguageKeyInLocal() {
    SharedPreferences preferences = getContext().getSharedPreferences("language_locale", Context.MODE_PRIVATE);
    return preferences.getString("language", null);
  }

  public static void onConfigurationChanged(@NonNull Configuration newConfig) {
    getInstance().onDeviceConfigurationChange(getContext(), newConfig);
  }

  public static Context getContext() {
    return mContext;
  }

  public static MLang getInstance() {
    return getInstance(getContext());
  }

  public static MLang getInstance(Context context) {
    return MLang.getInstance(context, filesDir, action);
  }

  public static File getFilesDirFixed(Context context) {
    return Util.getFilesDirFixed(context, "/data/data/com.locale.ui/files");
  }

  public static void loadRemoteLanguages(final Context context, final MLang.FinishLoadCallback callback) {
    getInstance().loadRemoteLanguages(context, callback);
  }

  public static void applyLanguage(Context context, LocaleInfo localeInfo) {
    getInstance().applyLanguage(context, localeInfo);
  }

  public static String getLocaleAlias(String code) {
    return MLang.getLocaleAlias(code);
  }

  public static String getCurrentLanguageName() {

    LanguagePackage languageByCode = SoftRoomDatabase.getDatabase(BaseApplication.getInstance()).languagePackageDao().getLanguageByCode(getCurrentLanguageCode());

    if (null==languageByCode){
      return Utils.NA_LANGUAGE_NAME;
    }

    return getInstance().getCurrentLanguageName(getContext());
  }

  /**
   * @return java.lang.String
   * @method getCurrentLanguageCode
   * @description 通过MLang CurrentLanguageName查询db数据匹配LanguageCode,耗时操作
   * @date: 2022/10/31 16:30
   * @author: langmeng
   */
  public static String getCurrentLanguageCode() {

    String langCode = getInstance().getCurrentLocaleInfo().getLangCode();
    //当期系统使用的语言
    String localeCode = Resources.getSystem().getConfiguration().locale.getLanguage();

    LanguageInfo languageInfo = SoftRoomDatabase.getDatabase(BaseApplication.getInstance().getApplicationContext())
      .languageInfoDao().getLanguageInfoByCode(localeCode);

    if (null != languageInfo) {
      if (!TextUtils.isEmpty(languageInfo.getType())){
        langCode = languageInfo.getType();
      }
    }

    return langCode;

  }



  public static String getServerString(String key) {
    return getInstance().getServerString(getContext(), key);
  }

  public static String getString(String key, int res) {
    //需求：引导页文案换行功能：现在文案很长但是没有换行标记，导致前端展示很影响用户体验，是否可加个换行功能
    String langString = getInstance().getString(getContext(), key, res);
    if (langString.contains(BLANK_SPACE)) {
      langString = langString.replace(BLANK_SPACE, "\n");
    }
    return langString;
  }

  public static String getString(String key) {
    return getInstance().getString(getContext(), key);
  }

  public static String getPluralString(String key, int plural) {
    return getInstance().getPluralString(getContext(), key, plural);
  }

  public static String formatPluralString(String key, int plural) {
    return getInstance().formatPluralString(getContext(), key, plural);
  }

  public static String formatPluralStringComma(String key, int plural) {
    return getInstance().formatPluralStringComma(getContext(), key, plural);
  }

  public static String formatString(String key, int res, Object... args) {
    return getInstance().formatString(getContext(), key, res, args);
  }

  public static String formatTTLString(int ttl) {
    return getInstance().formatTTLString(getContext(), ttl);
  }

  public static String formatStringSimple(String string, Object... args) {
    return getInstance().formatStringSimple(getContext(), string, args);
  }

  public static String formatCallDuration(int duration) {
    return getInstance().formatCallDuration(getContext(), duration);
  }

  public static String formatDateChat(long date) {
    return getInstance().formatDateChat(getContext(), date);
  }

  public static String formatDateChat(long date, boolean checkYear) {
    return getInstance().formatDateChat(getContext(), date, checkYear);
  }

  public static String formatDate(long date) {
    return getInstance().formatDate(getContext(), date);
  }

  public static String formatDateAudio(long date, boolean shortFormat) {
    return getInstance().formatDateAudio(getContext(), date, shortFormat);
  }

  public static String formatDateCallLog(long date) {
    return getInstance().formatDateCallLog(getContext(), date);
  }

  public static String formatLocationUpdateDate(long date, long timeFromServer) {
    return getInstance().formatLocationUpdateDate(getContext(), date, timeFromServer);
  }

  public static String formatLocationLeftTime(int time) {
    return MLang.formatLocationLeftTime(time);
  }

  public static String formatDateOnline(long date) {
    return getInstance().formatDateOnline(getContext(), date);
  }

  public static boolean isRTLCharacter(char ch) {
    return MLang.isRTLCharacter(ch);
  }

  public static String formatSectionDate(long date) {
    return getInstance().formatSectionDate(getContext(), date);
  }

  public static String formatDateForBan(long date) {
    return getInstance().formatDateForBan(getContext(), date);
  }

  public static String stringForMessageListDate(long date) {
    return getInstance().stringForMessageListDate(getContext(), date);
  }


}

