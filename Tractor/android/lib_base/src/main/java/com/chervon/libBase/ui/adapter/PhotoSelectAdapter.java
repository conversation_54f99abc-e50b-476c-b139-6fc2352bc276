package com.chervon.libBase.ui.adapter;


import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.chervon.libBase.R;
import com.chervon.libBase.ui.ItemClick;
import com.chervon.libBase.ui.PhotoSelectItemClick;

import java.util.List;

public class PhotoSelectAdapter extends RecyclerView.Adapter {
    public List<String> datas;
    public static final int TYPE_HEADER = 0;
    public static final int TYPE_FOOTER = 1;
    public static final int TYPE_ITEM = 2;
    private Context context;
    private PhotoSelectItemClick itemClick;

    public void setDatas(List<String> datas) {
        this.datas = datas;
    }

    public PhotoSelectAdapter(Context context, PhotoSelectItemClick itemClick, List<String> datas) {
        this.context = context;
        this.datas = datas;
        this.itemClick=itemClick;
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        if (viewType == TYPE_FOOTER) {
            View itemView = LayoutInflater.from(context).inflate(R.layout.moudle_devicemanage_recycle_receipt_item_footer, parent, false);
            return new FooterViewHolder(itemView);
        } else     {
            View itemView = LayoutInflater.from(context).inflate(R.layout.moudle_devicemanage_recycle_receipt_item, parent, false);
            return new ItemViewHolder(itemView);
        }
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        int viewType = getItemViewType(position);
        if (viewType == TYPE_FOOTER) {
          if(position>3){
            ((FooterViewHolder) holder).tvFooter.setVisibility(View.GONE);
          }else{
            ((FooterViewHolder) holder).tvFooter.setVisibility(View.VISIBLE);
          }
            ((FooterViewHolder) holder).tvFooter.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    itemClick.onItemClick(v,PhotoSelectAdapter.this);
                }
            });
        } else  {
            ((ItemViewHolder)holder).btDel.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    datas.remove(position);
                    notifyDataSetChanged();
                  itemClick.onItemDelClick(v );
                }
            });

            ((ItemViewHolder)holder).clContainer.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    v.setTag(position);
                    itemClick.onItemClick(v,PhotoSelectAdapter.this);
                }
            });
            ((ItemViewHolder)holder).ivDeviceIcon.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    v.setTag(position);
                    itemClick.onItemClick(v,PhotoSelectAdapter.this);
                }
            });

            String url = datas.get(position);
            Glide.with(context).load(url).into(((ItemViewHolder) holder).ivDeviceIcon);

        }
    }

    @Override
    public int getItemCount() {
        return datas.size();
    }

    @Override
    public int getItemViewType(int position) {
        if ((position == getItemCount() - 1)) {
            return TYPE_FOOTER;
        } else {
            return TYPE_ITEM;
        }
    }

  public List<String> getDatas() {
      return datas;
  }


  class FooterViewHolder extends RecyclerView.ViewHolder {
        View tvFooter;
        public FooterViewHolder(@NonNull View itemView) {
            super(itemView);
            tvFooter = itemView;
        }
    }

    class ItemViewHolder extends RecyclerView.ViewHolder {
        ImageButton btDel;
        ImageView ivDeviceIcon;
        View clContainer;

        public ItemViewHolder(@NonNull View itemView) {
            super(itemView);
            btDel = itemView.findViewById(R.id.ibDeviceDel);
            ivDeviceIcon = itemView.findViewById(R.id.ivDeviceIcon);
            clContainer= itemView.findViewById(R.id.clContainer);
        }
    }
}
