package com.chervon.libBase.ui;

import android.app.Activity;
import android.content.DialogInterface;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;

import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

public class BaseDialogFragment extends DialogFragment  {

    boolean mDismissed;
    boolean mShownByMe;

    public boolean getDismissed() {

        return mDismissed;
    }

    public void setDismissed(boolean dismissed) {
        this.mDismissed = dismissed;
    }

    public boolean getShownByMe() {
        return mShownByMe;
    }

    public void setShownByMe(boolean shownByMe) {
        this.mShownByMe = shownByMe;
    }

    @Override
    public void show(FragmentManager manager, String tag) {
        setDismissed(false);
        setShownByMe(false);
        FragmentTransaction ft = manager.beginTransaction();
        ft.add(this, tag);
        ft.commitAllowingStateLoss();
    }

    @Override
    public void dismiss() {
        //super.dismiss();
        dismissAllowingStateLoss();
    }


}
