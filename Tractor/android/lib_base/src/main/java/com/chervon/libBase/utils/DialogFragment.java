package com.chervon.libBase.utils;

import android.app.Dialog;
import android.content.DialogInterface;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.fragment.app.FragmentManager;

import com.blankj.utilcode.util.LogUtils;
import com.chervon.libBase.R;
import com.chervon.libBase.databinding.BaseDialogSimpleTipNoTitleBinding;
import com.chervon.libBase.utils.mlang.LanguageStrings;

/**
 * @Author: 184862
 * @CreateDate: 2024/4/9
 * @UpdateDate: 2024/4/9
 */
public class DialogFragment extends androidx.fragment.app.DialogFragment {

  private static final String TAG = "DialogFragment";
  private BaseDialogSimpleTipNoTitleBinding mBinding;

  public static DialogFragment mInstance;
  //蓝牙开关key
  public static final String BLE_ENABLE_DIALOG_KEY = "ble_enable_dialog_key";
  //GPS开关key 基于Android12以下版本兼容
  public static final String GPS_ENABLE_DIALOG_KEY = "gps_enable_dialog_key";
  //定位权限key
  public static final String LOCATION_PERMISSION_DIALOG_KEY = "location_permission_dialog_key";

  public static final String FRAGMENT_CONTENT_KEY = "fragment_content_key";


  private static DialogClickListener mListener;

  public static DialogFragment getInstance(String content) {

    if (null == mInstance) {
      mInstance = new DialogFragment();
    }
    mListener = null;
    Bundle bundle = new Bundle();
    bundle.putString(FRAGMENT_CONTENT_KEY, content);
    mInstance.setArguments(bundle);
    return mInstance;
  }

  //支持接口回调
  public static DialogFragment getInstance(String content, DialogClickListener listener) {

    if (null == mInstance) {
      mInstance = new DialogFragment();
    }

    mListener = listener;
    Bundle bundle = new Bundle();
    bundle.putString(FRAGMENT_CONTENT_KEY, content);
    mInstance.setArguments(bundle);
    return mInstance;
  }

  @Nullable
  @Override
  public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
    mBinding = DataBindingUtil.inflate(inflater, R.layout.base_dialog_simple_tip_no_title, null, false);
    return mBinding.getRoot();
  }


  @Override
  public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
    super.onViewCreated(view, savedInstanceState);
    Dialog dialog = getDialog();
    String showContent = getArguments().getString(FRAGMENT_CONTENT_KEY);
    try {
      dialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
      dialog.getWindow().setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
      mBinding.tvMessage.setText(TextUtils.isEmpty(showContent) ? "" : showContent);
      mBinding.btnConfirm.setText(LanguageStrings.app_base_ok_button_text());
      mBinding.btnConfirm.setOnClickListener(new View.OnClickListener() {
        @Override
        public void onClick(View v) {

          getDialog().dismiss();

          if (null != mListener) {
            mListener.clickComfirm();
          }

        }
      });
    } catch (Exception e) {
      LogUtils.d(e.getMessage());
    }
  }

  public void showDialog(FragmentManager manager, String tag) {

    try {
      //在每个add事务前增加一个remove事务，防止连续的add
      manager.beginTransaction().remove(this).commit();
      super.show(manager, tag);
    } catch (Exception e) {
      e.printStackTrace();
    }


  }

  /**
   * 提供给外部使用
   */
  public interface DialogClickListener {
    void clickComfirm();
  }

  public void dismissDialog() {
    if (getFragmentManager() == null) {
      LogUtils.i(TAG, "dismiss: " + this + " not associated with a fragment manager.");
    } else {
      super.dismiss();
    }

  }


}
