package com.chervon.libBase.ui.viewmodel;

import android.app.Application;
import android.content.Context;

import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleEventObserver;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.LifecycleRegistry;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.navigation.NavGraph;

import com.chervon.libBase.ui.BaseUistate;

/**
 * @ProjectName: app
 * @Package: com.chervon.libBase.ui.viewmodel
 * @ClassName: BaseViewModel
 * @Description: base of ViewModeL
 * @Author: wangheng
 * @CreateDate: 2022/4/19 下午7:02
 * @UpdateUser: wangheng
 * @UpdateDate: 2022/4/19 下午7:02
 * @UpdateRemark: new
 * @Version: 1.0
 */
public abstract class BaseViewModel< U extends BaseUistate> extends AndroidViewModel implements IViewModel {
    public LifecycleRegistry mRegistry;
    public Context context;
    public int navId;
    public LiveData<U> mLiveData = new MutableLiveData<U>();
    public NavGraph navGraph;
    public BaseViewModel(@NonNull Application application) {
        super(application);
        mRegistry = new LifecycleRegistry((LifecycleOwner) this);
        getLifecycle().addObserver(new LifecycleEventObserver() {
            @Override
            public void onStateChanged(@NonNull LifecycleOwner source, @NonNull Lifecycle.Event event) {
                if (event== Lifecycle.Event.ON_CREATE) {
                    onCreate(source);
                } else if (event == Lifecycle.Event.ON_RESUME) {
                    onResume(source);
                } else if (event  == Lifecycle.Event.ON_DESTROY) {
                    onDestroy(source);
                }else if (event == Lifecycle.Event.ON_START){
                  onStart(source);
                }
            }
        });
    }

    @NonNull
    @Override
    public Lifecycle getLifecycle() {
        return mRegistry;
    }

    public void setnavGraph(NavGraph navGraph) {
        this.navGraph=navGraph;
    }

    public NavGraph getNavGraph() {
        return navGraph;
    }

    public void setNav(int id) {
        navId=id;
    }

    public int getNavId() {
        return navId;
    }


}
