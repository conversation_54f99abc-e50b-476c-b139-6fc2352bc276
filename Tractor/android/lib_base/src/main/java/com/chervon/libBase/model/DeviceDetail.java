package com.chervon.libBase.model;

import com.fasterxml.jackson.annotation.JsonIgnore;

import java.io.Serializable;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleConfigNet.data.model
 * @ClassName: DeviceDetail
 * @Description: 类描述
 * @Author: name
 * @CreateDate: 2022/5/12 下午4:46
 * @UpdateUser: 更新者
 * @UpdateDate: 2022/5/12 下午4:46
 * @UpdateRemark: 更新说明
 * @Version: 1.0
 */
public class DeviceDetail implements Serializable {
    String id;
    String deviceName;
    String nickName;
    String deviceIcon;
    int isOnline;
    String sn;
    String createTime;
    int status;
    int communicateMode;
    int deviceType;
    String applyWith;
    String purchasePlace;
    String purchaseTime;
    String receiptInformation;
    String receiptfilekey;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getDeviceIcon() {
        return deviceIcon;
    }

    public void setDeviceIcon(String deviceIcon) {
        this.deviceIcon = deviceIcon;
    }

    public int getIsOnline() {
        return isOnline;
    }

    public void setIsOnline(int isOnline) {
        this.isOnline = isOnline;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getCommunicateMode() {
        return communicateMode;
    }

    public void setCommunicateMode(int communicateMode) {
        this.communicateMode = communicateMode;
    }

    public int getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(int deviceType) {
        this.deviceType = deviceType;
    }

    public String getApplyWith() {
        return applyWith;
    }

    public void setApplyWith(String applyWith) {
        this.applyWith = applyWith;
    }

    public String getPurchasePlace() {
        return purchasePlace;
    }

    public void setPurchasePlace(String purchasePlace) {
        this.purchasePlace = purchasePlace;
    }

    public String getPurchaseTime() {
        return purchaseTime;
    }

    public void setPurchaseTime(String purchaseTime) {
        this.purchaseTime = purchaseTime;
    }

    public String getReceiptInformation() {
        return receiptInformation;
    }

    public void setReceiptInformation(String receiptInformation) {
        this.receiptInformation = receiptInformation;
    }

    public String getReceiptfilekey() {
        return receiptfilekey;
    }

    public void setReceiptfilekey(String receiptfilekey) {
        this.receiptfilekey = receiptfilekey;
    }
}
