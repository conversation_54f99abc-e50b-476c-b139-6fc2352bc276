package com.chervon.libBase.ui.adapter;


import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.chervon.libBase.R;
import com.chervon.libBase.ui.PhotoSelectItemClick;

import java.util.List;

public class PhotoGirdAdapter extends RecyclerView.Adapter {
    public List<String> datas;

  public List<String> getDatas() {
    return datas;
  }

  public static final int TYPE_ITEM = 2;
    private Context context;
    private PhotoSelectItemClick itemClick;

    public void setDatas(List<String> datas) {
        this.datas = datas;
    }

    public PhotoGirdAdapter(Context context, PhotoSelectItemClick itemClick, List<String> datas) {
        this.context = context;
        this.datas = datas;
        this.itemClick=itemClick;
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            View itemView = LayoutInflater.from(context).inflate(R.layout.moudle_devicemanage_recycle_receipt_item, parent, false);
            return new ItemViewHolder(itemView);
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        int viewType = getItemViewType(position);
        {
            ((ItemViewHolder)holder).btDel.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    datas.remove(position);
                    notifyDataSetChanged();
             //     itemClick.onItemDelClick(v );
                }
            });

            ((ItemViewHolder)holder).clContainer.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                  itemClick.onItemClick(datas.get(position));
                }
            });
          ((ItemViewHolder)holder).btDel.setVisibility(View.GONE);
            ((ItemViewHolder)holder).ivDeviceIcon.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    v.setTag(position);
            //        itemClick.onItemClick(v, PhotoGirdAdapter.this);
                  itemClick.onItemClick(datas.get(position));
                }
            });

            String url = datas.get(position);
            Glide.with(context).load(url).into(((ItemViewHolder) holder).ivDeviceIcon);

        }
    }

    @Override
    public int getItemCount() {
        return datas.size();
    }

    @Override
    public int getItemViewType(int position) {
            return TYPE_ITEM;
    }


    class FooterViewHolder extends RecyclerView.ViewHolder {

        View tvFooter;

        public FooterViewHolder(@NonNull View itemView) {
            super(itemView);
            tvFooter = itemView;
        }
    }

    class ItemViewHolder extends RecyclerView.ViewHolder {
        ImageButton btDel;
        ImageView ivDeviceIcon;
        View clContainer;

        public ItemViewHolder(@NonNull View itemView) {
            super(itemView);
            btDel = itemView.findViewById(R.id.ibDeviceDel);
            ivDeviceIcon = itemView.findViewById(R.id.ivDeviceIcon);
            clContainer= itemView.findViewById(R.id.clContainer);
        }
    }
}
