package com.chervon.libBase.ui;


import androidx.recyclerview.widget.RecyclerView;

import com.chervon.libBase.ui.adapter.PhotoSelectAdapter;
import com.chervon.libDB.entities.DeviceInfo;

/**
 * @ProjectName: app
 * @Package: com.chervon.libBase.ui.presenter
 * @ClassName: ItemClick
 * @Description: 类描述
 * @Author: name
 * @CreateDate: 2022/5/11 下午5:41
 * @UpdateUser: 更新者
 * @UpdateDate: 2022/5/11 下午5:41
 * @UpdateRemark: 更新说明
 * @Version: 1.0
 */
public interface PhotoSelectItemClick {
    public void onItemClick(Object uiState, PhotoSelectAdapter adapter);

    public void onItemDelClick(Object uiState);

    public void onItemClick(Object v);

}
