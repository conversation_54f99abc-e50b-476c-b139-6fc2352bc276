package com.chervon.libBase.model;

public class ProductsParams {
   int pageNum;
    int pageSize;
//    String snCode;
//   int communicateMode;
//   int productType;
//   int status;
    public ProductsParams(){

    }

    public ProductsParams(int pageNum, int pageSize) {
        this.pageNum = pageNum;
        this.pageSize = pageSize;
    }
//    public ProductsParams(int pageNum, int pageSize, String snCode, int communicateMode, int productType, int status) {
//        this.pageNum = pageNum;
//        this.pageSize = pageSize;
//        this.snCode = snCode;
//        this.communicateMode = communicateMode;
//        this.productType = productType;
//        this.status = status;
//    }

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

//    public String getSnCode() {
//        return snCode;
//    }
//
//    public void setSnCode(String snCode) {
//        this.snCode = snCode;
//    }
//
//    public int getCommunicateMode() {
//        return communicateMode;
//    }
//
//    public void setCommunicateMode(int communicateMode) {
//        this.communicateMode = communicateMode;
//    }
//
//    public int getProductType() {
//        return productType;
//    }
//
//    public void setProductType(int productType) {
//        this.productType = productType;
//    }
//
//    public int getStatus() {
//        return status;
//    }
//
//    public void setStatus(int status) {
//        this.status = status;
//    }
}
