package com.chervon.libBase.ui;


import android.content.Context;
import android.content.Intent;

import com.chervon.libBase.model.FeedBackReplyRequest;
import com.chervon.libDB.entities.DeviceInfo;

import java.util.List;

/**
 * @ProjectName: app
 * @Package: com.chervon.libBase.ui.presenter
 * @ClassName: ItemClick
 * @Description: 类描述
 * @Author: name
 * @CreateDate: 2022/5/11 下午5:41
 * @UpdateUser: 更新者
 * @UpdateDate: 2022/5/11 下午5:41
 * @UpdateRemark: 更新说明
 * @Version: 1.0
 */
public interface PhotoSelectInterface {
  void goToTakePhotoPage();

  abstract   void openGalley();

  void setResultListenner(ResultListenner resultListenner);


  void startActivityForResult(Intent i, int codeGallery);

  /**
   * @param feedBackReplyRequest
   * @param urls
   * @param type--0:feedback回复，1：编辑反馈
   */
  void replyFeedBack(FeedBackReplyRequest feedBackReplyRequest, List<String> urls,int type);


  void previewPhoto(String url);
}

