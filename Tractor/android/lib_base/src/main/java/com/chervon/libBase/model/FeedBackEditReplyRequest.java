package com.chervon.libBase.model;

import java.util.List;

/**
 * @ProjectName: app
 * @Package: com.chervon.libNetwork.http.model.request
 * @ClassName: EditInfoRequest
 * @Description: 修改用户信息接口请求模型
 * @Author: LangMeng
 * @CreateDate: 2022/6/23 15:33
 * @UpdateUser: LangMeng
 * @UpdateDate: 2022/6/23 15:33
 * @UpdateRemark: new class
 * @Version: 1.0
 */
public class FeedBackEditReplyRequest {
  /**
   * 1、如果是原始问题编辑的话：feedback类型: feedback---1 feedback_reply----2
   */
  private int  type	;
  /**
   * 1、如果是原始问题：设置为feedbackId
   * 2、如果是自己回复的问题即为回复的id
   */
  private String  id;
  private String  content ;
  private List<String> pictures;

  public int getType() {
    return type;
  }

  public void setType(int type) {
    this.type = type;
  }

  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public String getContent() {
    return content;
  }

  public void setContent(String content) {
    this.content = content;
  }

  public List<String> getPictures() {
    return pictures;
  }

  public void setPictures(List<String> pictures) {
    this.pictures = pictures;
  }
}
