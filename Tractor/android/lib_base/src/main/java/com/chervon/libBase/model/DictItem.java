package com.chervon.libBase.model;

import com.chervon.libBase.model.DictNode;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleDeviceManage.ui.state
 * @ClassName: DeviceListUistate
 * @Description: 类描述
 * @Author: name
 * @CreateDate: 2022/5/17 下午6:18
 * @UpdateUser: 更新者
 * @UpdateDate: 2022/5/17 下午6:18
 * @UpdateRemark: 更新说明
 * @Version: 1.0
 */
public class DictItem {
    private String dictId;

    private String dictName;

    private String dictDescription;

    private String dealer_category;

    private DictNode[] nodes;

    public String getDictId() {
        return dictId;
    }

    public void setDictId(String dictId) {
        this.dictId = dictId;
    }

    public String getDictName() {
        return dictName;
    }

    public void setDictName(String dictName) {
        this.dictName = dictName;
    }

    public String getDictDescription() {
        return dictDescription;
    }

    public void setDictDescription(String dictDescription) {
        this.dictDescription = dictDescription;
    }

    public DictNode[] getNodes() {
        return nodes;
    }

    public void setNodes(DictNode[] nodes) {
        this.nodes = nodes;
    }


    public String getDealer_category() {
        return dealer_category;
    }

    public void setDealer_category(String dealer_category) {
        this.dealer_category = dealer_category;
    }
}
