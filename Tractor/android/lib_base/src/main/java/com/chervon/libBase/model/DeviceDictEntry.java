package com.chervon.libBase.model;

import com.chervon.libBase.model.DictItem;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleDeviceManage.ui.state
 * @ClassName: DeviceListUistate
 * @Description: 类描述
 * @Author: name
 * @CreateDate: 2022/5/17 下午6:18
 * @UpdateUser: 更新者
 * @UpdateDate: 2022/5/17 下午6:18
 * @UpdateRemark: 更新说明
 * @Version: 1.0
 */
public class DeviceDictEntry {

    private DictItem[] entry;

    public DictItem[] getEntry() {
        return entry;
    }

    public void setEntry(DictItem[] entry) {
        this.entry = entry;
    }
}
