package com.chervon.libBase.model;

import android.content.Intent;
import android.text.TextUtils;
import android.util.Log;

import androidx.arch.core.util.Function;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.Transformations;

import com.alibaba.android.arouter.launcher.ARouter;
import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.GsonUtils;
import com.blankj.utilcode.util.JsonUtils;
import com.blankj.utilcode.util.SPUtils;
import com.chervon.libBase.BaseApplication;
import com.chervon.libBase.ui.UserTokenListernner;
import com.chervon.libDB.SoftRoomDatabase;
import com.chervon.libDB.dao.UserDao;
import com.chervon.libDB.entities.User;
import com.chervon.libRouter.RouterConstants;



/**
 * @ProjectName: app
 * @Package: com.chervon.libBase
 * @ClassName: UserInfo
 * @Description: UserInfo entity
 * @Author: langmeng
 * @CreateDate: 2022/4/19 下午7:02
 * @UpdateUser: langmeng
 * @UpdateDate: 2022/4/19 下午7:02
 * @UpdateRemark: new
 * @Version: 1.0
 */
public class UserInfo {
  private static UserTokenListernner mUserTokenListernner;
  private static User curUser = null;
  private static MutableLiveData<User> userLiveData = new MutableLiveData();

  public static synchronized void set(User user) {
    curUser = user;
    userLiveData.postValue(user);
    SPUtils.getInstance().put(SpConstants.TOKEN, user.getAccessToken());
    SoftRoomDatabase db = SoftRoomDatabase.getDatabase(BaseApplication.getInstance().getApplicationContext());
    UserDao mUserDao = db.userDao();


    SoftRoomDatabase.databaseWriteExecutor.execute(() -> {
       mUserDao.deleteAll();
      if (!TextUtils.isEmpty(user.getId())){

        mUserDao.insertUser(user);
      }

      Log.e("获取-SmartScanState ",String.valueOf(UserInfo.get().isSmartScanState()));
    });
  }

  public static boolean isUserNull() {
    return curUser == null;
  }

  public static synchronized User get() {
    if (curUser == null) {
      String token = SPUtils.getInstance().getString(SpConstants.TOKEN);
      SoftRoomDatabase db = SoftRoomDatabase.getDatabase(BaseApplication.getInstance().getApplicationContext());
      UserDao mUserDao = db.userDao();
      curUser = mUserDao.getUser();
      if (curUser != null) {
        userLiveData.postValue(curUser);
        if (TextUtils.isEmpty(token)) {
          ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_LOGIN).navigation();
          curUser.setAccessToken("");
        } else {
          curUser.setAccessToken(token);
        }
      } else {
        ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_LOGIN).navigation();
        curUser = new User();
        curUser.setAccessToken("");
      }
    }
    return curUser;
  }


  public static synchronized User getDataOnly() {
    if (curUser == null) {
      String token = SPUtils.getInstance().getString(SpConstants.TOKEN);
      SoftRoomDatabase db = SoftRoomDatabase.getDatabase(BaseApplication.getInstance().getApplicationContext());
      UserDao mUserDao = db.userDao();
      curUser = mUserDao.getUser();
      if (curUser != null) {
        userLiveData.postValue(curUser);
        if (TextUtils.isEmpty(token)) {
          curUser.setAccessToken("");
        } else {
          curUser.setAccessToken(token);
        }
      } else {
        curUser = new User();
        curUser.setAccessToken("");
      }
    }
    return curUser;
  }

  public static synchronized LiveData<User> getLiveData() {
    return userLiveData;
  }
  public static synchronized LiveData<User> getUserLiveData() {

    SoftRoomDatabase.databaseWriteExecutor.execute(() -> {
      User user= getDataOnly();
      userLiveData.postValue(user);
    });
    return userLiveData;
  }

  public static boolean clear() {
    User user = get();

    if (null==user){
      return false;
    }
    if (TextUtils.isEmpty(user.getAccessToken())){
      return false;
    }

    user.setAccessToken("");
    set(user);
    FragmentActivity topActivity = (FragmentActivity) ActivityUtils.getTopActivity();String activityName = topActivity.getClass().getName();
    if ("com.chervon.moudleOobe.ui.LoginAndRegisterActivity".equals(activityName) || "com.chervon.moudleOobe.ui.LoginAndRegisterActivity".equals(activityName)) {
      //如果栈顶activity是RN面板则不弹窗
    }else{
      ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_LOGIN).addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP).navigation();
    }


    if(mUserTokenListernner != null) {
      mUserTokenListernner.onTokenChange();
    }
    return curUser == null;
  }

  public static void addTokenListener(UserTokenListernner userTokenListernner) {
    mUserTokenListernner = userTokenListernner;
  }
}
