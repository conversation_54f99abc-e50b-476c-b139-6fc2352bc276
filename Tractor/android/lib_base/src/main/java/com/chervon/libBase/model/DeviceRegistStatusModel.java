package com.chervon.libBase.model;

/**
 * @Author: 184862
 * @CreateDate: 2024/3/21
 * @desc 设备注册发送的通知
 * @UpdateDate: 2024/3/21
 */
public class DeviceRegistStatusModel {
  //设备id
  private String deviceId;
  // 0或1
  private String infoStatus;
  //注册成功
  public static String REGIST_RESULT_SUCCESS = "1";
  //注册失败
  public static String REGIST_RESULT_FAIL = "0";


  public DeviceRegistStatusModel(String deviceId, String infoStatus) {
    this.deviceId = deviceId;
    this.infoStatus = infoStatus;
  }

  public String getDeviceId() {
    return deviceId;
  }

  public void setDeviceId(String deviceId) {
    this.deviceId = deviceId;
  }

  public String getInfoStatus() {
    return infoStatus;
  }

  public void setInfoStatus(String infoStatus) {
    this.infoStatus = infoStatus;
  }
}
