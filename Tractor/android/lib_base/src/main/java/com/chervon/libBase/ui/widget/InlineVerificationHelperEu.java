package com.chervon.libBase.ui.widget;

import static com.chervon.libBase.utils.CommonUtils.checkPassword;

import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.CheckBox;

import com.chervon.libBase.utils.CommonUtils;
import com.chervon.libBase.utils.DialogUtil;
import com.chervon.libBase.utils.Utils;
import com.chervon.libBase.utils.mlang.LanguageStrings;

import java.util.ArrayList;
import java.util.List;

/**
 * @ProjectName: app
 * @Package: com.chervon.libBase.utils
 * @ClassName: InlineVerificationHelperEu
 * @Description: the Helper   of InlineVerification
 * @Author: wuxd
 * @CreateDate: 2024/7/2
 * @UpdateUser:
 * @UpdateDate:
 * @UpdateRemark:
 * @Version: 1.0
 */
public class InlineVerificationHelperEu {
  private List<Checker> list = new ArrayList<>();
  private View btnNext;
  private String mail = null;
  private String editPassword = null;
  private static final String MAIL_TAG = "@";
  private boolean emailCheckResult = false;

  public InlineVerificationHelperEu(View btnNext) {
    this.btnNext = btnNext;
  }

  public InlineVerificationHelperEu buildEmailExistChecker() {
    list.add(() -> emailCheckResult);
    return this;
  }

  public boolean isEmailCheckResult() {
    return emailCheckResult;
  }

  public void setEmailCheckResult(boolean emailCheckResult) {
    this.emailCheckResult = emailCheckResult;
  }

  public InlineVerificationHelperEu buildEmailChecker(InlineTipEditTextEu view) {
    return buildEmailChecker(view, null);
  }


  public InlineVerificationHelperEu buildEmailChecker(InlineTipEditTextEu view, EmailInputComplete listenner) {
    view.setEditTextOnFocusChangeListener(new View.OnFocusChangeListener() {
      @Override
      public void onFocusChange(View v, boolean hasFocus) {
        if (!hasFocus) {
          mail = view.getInlineTipEditTextString();
          if (!checkEmail(view)) {
            view.showErrorMargin();
            view.showInlineTip(InlineTipEditTextEu.INPUT_TYPE_EMAIL, LanguageStrings.getEnterCorrectMailbox(), "", "");
          } else {
            if (listenner != null) {
              listenner.complete(view.getInlineTipEditTextString());
            }

          }
        }
      }
    });
    list.add(new Checker() {
      @Override
      public boolean check() {
        return checkEmail(view);
      }
    });
    return this;
  }


  public InlineVerificationHelperEu buildFirstName(InlineTipEditTextEu view) {
    return buildFirstNameChecker(view, null);
  }


  public InlineVerificationHelperEu buildFirstNameChecker(InlineTipEditTextEu view, EmailInputComplete listenner) {
    view.setEditTextOnFocusChangeListener(new View.OnFocusChangeListener() {
      @Override
      public void onFocusChange(View v, boolean hasFocus) {
        if (!hasFocus) {
          String firstName = view.getInlineTipEditTextString();
          if (TextUtils.isEmpty(firstName)) {
            view.showInlineTip(InlineTipEditTextEu.INPUT_TYPE_FIRST_NAME, LanguageStrings.app_base_inputrequirederror_textview_text(), "", "");
            view.showErrorMargin();
          } else {
            if (!checkFirstName(view)) {

              view.showInlineTip(InlineTipEditTextEu.INPUT_TYPE_FIRST_NAME, LanguageStrings.app_base_nameinputerror_textview_text(), "", "");
              view.showErrorMargin();
            } else {
              if (listenner != null) {

                listenner.complete(view.getInlineTipEditTextString());
              }

            }
          }


        }
      }
    });
    list.add(new Checker() {
      @Override
      public boolean check() {
        return checkFirstName(view);
      }
    });
    return this;
  }

  public boolean checkFirstName(InlineTipEditTextEu view) {
    String firstName = view.getInlineTipEditTextString();

    if (TextUtils.isEmpty(firstName)) {
      return false;
    }

    if (Utils.nameMatches(firstName)){
      view.showInlineTip(InlineTipEditTextEu.INPUT_TYPE_FIRST_NAME, null, null, null);
    }

    return Utils.nameMatches(firstName);
  }



  public InlineVerificationHelperEu buildLastName(InlineTipEditTextEu view) {
    return buildLastNameChecker(view, null);
  }

  public InlineVerificationHelperEu buildLastNameChecker(InlineTipEditTextEu view, EmailInputComplete listenner) {
    view.setEditTextOnFocusChangeListener(new View.OnFocusChangeListener() {
      @Override
      public void onFocusChange(View v, boolean hasFocus) {
        if (!hasFocus) {
          String lastName = view.getInlineTipEditTextString();
          if (TextUtils.isEmpty(lastName)) {
            view.showInlineTip(InlineTipEditTextEu.INPUT_TYPE_LAST_NAME, LanguageStrings.app_base_inputrequirederror_textview_text(), "", "");
            view.showErrorMargin();
          } else {
            if (!checkLastName(view)) {

              view.showInlineTip(InlineTipEditTextEu.INPUT_TYPE_LAST_NAME, LanguageStrings.app_base_nameinputerror_textview_text(), "", "");
              view.showErrorMargin();
            } else {
              if (listenner != null) {

                listenner.complete(view.getInlineTipEditTextString());
              }

            }
          }


        }
      }
    });
    list.add(new Checker() {
      @Override
      public boolean check() {
        return checkLastName(view);
      }
    });
    return this;
  }


  public boolean checkLastName(InlineTipEditTextEu view) {
    String lastName = view.getInlineTipEditTextString();

    if (TextUtils.isEmpty(lastName)) {
      return false;
    }

    if (Utils.nameMatches(lastName)){
      view.showInlineTip(InlineTipEditTextEu.INPUT_TYPE_LAST_NAME, null, null, null);
    }

    return Utils.nameMatches(lastName);
  }




  private boolean checkEmail(InlineTipEditTextEu view) {
    boolean isEmail = false;
    String email = view.getInlineTipEditTextString();
    if (!CommonUtils.checkEmail(email)) {
      disableBtnClick(false);
      return false;
    } else {
      isEmail = true;

      view.showInlineTip(InlineTipEditTextEu.INPUT_TYPE_EMAIL, null, null, null);
    }
    return isEmail;
  }

  public InlineVerificationHelperEu buildPasswordChecker(InlineTipEditTextEu view) {

    final boolean[] haFocusOn = {false};


    view.setEditTextOnFocusChangeListener(new View.OnFocusChangeListener() {
      @Override
      public void onFocusChange(View v, boolean hasFocus) {

        String password = view.getInlineTipEditTextString();
        editPassword = password;
        haFocusOn[0] = hasFocus;
        view.setMail(mail);
        if (hasFocus) {
          //在焦点内
          if (TextUtils.isEmpty(password)) {
            view.showInlineTip(InlineTipEditTextEu.INPUT_TYPE_PWD_NORMAL,
              "· " + LanguageStrings.app_oobe_sign_up_password_char_and_spaces(),
              "· " + LanguageStrings.app_oobe_sign_up_password_confirm_numbers_and_letters(),
              "· " + LanguageStrings.app_signup_passwordcheckemail_textview_text());
          }
        } else {
          //在焦点外
          if (TextUtils.isEmpty(password)) {
              view.showInlineTip(InlineTipEditTextEu.INPUT_TYPE_PWD_NORMAL,
                "· " + LanguageStrings.app_oobe_sign_up_password_char_and_spaces(),
                "· " + LanguageStrings.app_oobe_sign_up_password_confirm_numbers_and_letters(),
                "· " + LanguageStrings.app_signup_passwordcheckemail_textview_text());
          } else {
            view.showInlineTip(InlineTipEditTextEu.INPUT_TYPE_PWD,
              LanguageStrings.app_oobe_sign_up_password_char_and_spaces(),
              LanguageStrings.app_oobe_sign_up_password_confirm_numbers_and_letters(),
              LanguageStrings.app_signup_passwordcheckemail_textview_text());
          }

          view.showInTipsForMargin(InlineTipEditTextEu.INPUT_TYPE_PWD,
                  LanguageStrings.app_oobe_sign_up_password_char_and_spaces(),
                  LanguageStrings.app_oobe_sign_up_password_confirm_numbers_and_letters(),
                  LanguageStrings.app_signup_passwordcheckemail_textview_text());

        }

      }
    });


    view.addTextChangedListener(new TextWatcher() {
      @Override
      public void beforeTextChanged(CharSequence s, int start, int count, int after) {

      }

      @Override
      public void onTextChanged(CharSequence s, int start, int before, int count) {

      }

      @Override
      public void afterTextChanged(Editable edit) {

        String password = view.getInlineTipEditTextString();
        editPassword = password;
        //在焦点内
        if (TextUtils.isEmpty(password)) {
          if (haFocusOn[0]) {
            view.showInlineTip(InlineTipEditTextEu.INPUT_TYPE_PWD_NORMAL,
              "· " + LanguageStrings.app_oobe_sign_up_password_char_and_spaces(),
              "· " + LanguageStrings.app_oobe_sign_up_password_confirm_numbers_and_letters(),
              "· " + LanguageStrings.app_signup_passwordcheckemail_textview_text());
          }
        } else {
          view.showInlineTip(InlineTipEditTextEu.INPUT_TYPE_PWD,
            LanguageStrings.app_oobe_sign_up_password_char_and_spaces(),
            LanguageStrings.app_oobe_sign_up_password_confirm_numbers_and_letters(),
            LanguageStrings.app_signup_passwordcheckemail_textview_text());
        }
      }
    });


    list.add(new Checker() {
      @Override
      public boolean check() {
        view.setMail(mail);
        if (TextUtils.isEmpty(view.getInlineTipEditTextString())) {
          view.showInlineTip(InlineTipEditTextEu.INPUT_TYPE_PWD_NORMAL,
            "· " + LanguageStrings.app_oobe_sign_up_password_char_and_spaces(),
            "· " + LanguageStrings.app_oobe_sign_up_password_confirm_numbers_and_letters(),
            "· " + LanguageStrings.app_signup_passwordcheckemail_textview_text());
        } else {
          view.showInlineTip(InlineTipEditTextEu.INPUT_TYPE_PWD,
            LanguageStrings.app_oobe_sign_up_password_char_and_spaces(),
            LanguageStrings.app_oobe_sign_up_password_confirm_numbers_and_letters(),
            LanguageStrings.app_signup_passwordcheckemail_textview_text());
        }
        return checkPasswordText(view);
      }
    });
    return this;
  }

  public InlineVerificationHelperEu setEmail(String email) {
    this.mail = email;
    return this;
  }


  public InlineVerificationHelperEu buildPasswordCheckerWithSameChecker2(InlineTipEditTextEu view,
                                                                         InlineTipEditTextEu confirmView,
                                                                         InlineTipEditTextEu samechekerTipView) {

    view.setEditTextOnFocusChangeListener(new View.OnFocusChangeListener() {
      @Override
      public void onFocusChange(View v, boolean hasFocus) {


        if (!hasFocus) {
//          if (!checkPasswordText(view)) {
//            if(TextUtils.isEmpty(editPassword)&&TextUtils.isEmpty(view.getInlineTipEditTextString())) {
//              view.showInlineTip(InlineTipEditTextEu.INPUT_TYPE_CONFIRM_PWD, null, null, null);
//            } else {
//              view.showInlineTip(InlineTipEditTextEu.INPUT_TYPE_CONFIRM_PWD, LanguageStrings.app_forgetpasswordreset_passwordnotsame_textview_text(), null, null);
//            }
//          } else {
            String password;
            String confirmPassword;
            password = confirmView.getInlineTipEditTextString();
            confirmPassword = view.getInlineTipEditTextString();
            if (TextUtils.isEmpty(password) || TextUtils.isEmpty(confirmPassword)) {
              return;
            } else {
              if (confirmPassword.equals(password)) {

                samechekerTipView.showInlineTip(InlineTipEditTextEu.INPUT_TYPE_CONFIRM_PWD,
                  null,
                  null,
                  null);

              } else {
                samechekerTipView.showInlineTip(InlineTipEditTextEu.INPUT_TYPE_CONFIRM_PWD,
                  LanguageStrings.app_forgetpasswordreset_passwordnotsame_textview_text(),
                  null,
                  null);
              }
            }

          }
//        }
      }
    });


    list.add(new Checker() {
      @Override
      public boolean check() {
        return checkPasswordText(view);
      }
    });
    return this;
  }

  private void checkWhite(String password) {
    String error_header = LanguageStrings.app_regist_password_error_worning_header();
    boolean isMatch = Utils.passwordMatches(password);

    if (!isMatch) {
      String unMatchesString = Utils.passwordRegiesterMatchesString(password);
      error_header = error_header + unMatchesString;
      DialogUtil.showRegisterErrorDialog(btnNext.getContext(), error_header);

    }

  }


  public InlineVerificationHelperEu buildPasswordCheckerWithSameChecker(InlineTipEditTextEu view, InlineTipEditTextEu confirmView, InlineTipEditTextEu samechekerTipView) {

    view.setEditTextOnFocusChangeListener(new View.OnFocusChangeListener() {
      @Override
      public void onFocusChange(View v, boolean hasFocus) {
        if (!hasFocus) {
          if (!checkPasswordText(view)) {
            view.showInlineTip(InlineTipEditTextEu.INPUT_TYPE_PWD,
              LanguageStrings.app_oobe_sign_up_password_char_and_spaces(),
              LanguageStrings.app_oobe_sign_up_password_confirm_numbers_and_letters(),
              LanguageStrings.app_signup_passwordcheckemail_textview_text());
          } else {
            String password;
            String confirmPassword;
            password = confirmView.getInlineTipEditTextString();
            confirmPassword = view.getInlineTipEditTextString();
            if (TextUtils.isEmpty(password) || TextUtils.isEmpty(confirmPassword)) {
              return;
            } else {
              if (confirmPassword.equals(password)) {
                samechekerTipView.showInlineTip(
                  InlineTipEditTextEu.INPUT_TYPE_PWD,
                  null,
                  null, null);
              } else {
                samechekerTipView.showInlineTip(InlineTipEditTextEu.INPUT_TYPE_PWD,
                  LanguageStrings.app_oobe_sign_up_password_char_and_spaces(),
                  LanguageStrings.app_oobe_sign_up_password_confirm_numbers_and_letters(),
                  LanguageStrings.app_signup_passwordcheckemail_textview_text());
              }
            }

          }
        }
      }
    });


    list.add(new Checker() {
      @Override
      public boolean check() {
        return checkPasswordText(view);
      }
    });
    return this;
  }


  public InlineVerificationHelperEu buildAgreementChecker(CheckBox view) {
    list.add(new Checker() {
      @Override
      public boolean check() {
        return view.isChecked();
      }
    });
    return this;
  }


  private boolean checkPasswordText(InlineTipEditTextEu view) {
    boolean isPassword = false;
    String password = view.getInlineTipEditTextString();
    if (checkPassword(password)) {
      view.showInlineTip(InlineTipEditTextEu.INPUT_TYPE_CONFIRM_PWD, null, null, null);
      isPassword = true;
    } else {
      disableBtnClick(false);
      isPassword = false;
    }
    return isPassword;
  }


  public InlineVerificationHelperEu buildPasswordSameChecker(InlineTipEditTextEu passwordView, InlineTipEditTextEu confirmView) {
    list.add(new Checker() {
      @Override
      public boolean check() {
        return checkPasswordSame(passwordView, confirmView);
      }
    });
    return this;
  }

  private boolean checkPasswordSame(InlineTipEditTextEu passwordView, InlineTipEditTextEu confirmView) {
    boolean isPasswordSame = false;
    String password;
    String confirmPassword;
    password = passwordView.getInlineTipEditTextString();
    confirmPassword = confirmView.getInlineTipEditTextString();
    if (TextUtils.isEmpty(password) || TextUtils.isEmpty(confirmPassword)) {
      return isPasswordSame;
    }
    if (confirmPassword.equals(password)) {
      isPasswordSame = true;
//      if (isPasswordSame) {
//        checkWhite(password);
//      }
      confirmView.showInlineTip(InlineTipEditTextEu.INPUT_TYPE_CONFIRM_PWD, null, null, null);
    } else {
      confirmView.showInlineTip(InlineTipEditTextEu.INPUT_TYPE_CONFIRM_PWD, LanguageStrings.app_forgetpasswordreset_passwordnotsame_textview_text(), null, null);
    }
    return isPasswordSame;
  }

  public InlineVerificationHelperEu buildStreet(InlineTipEditTextEu view) {
    return buildStreetChecker(view, null);
  }

  public InlineVerificationHelperEu buildStreetChecker(InlineTipEditTextEu view, EmailInputComplete listenner) {
    view.setEditTextOnFocusChangeListener((v, hasFocus) -> {
      if (!hasFocus) {
        if (!checkStreet(view)) {
          view.showInlineTip(InlineTipEditTextEu.INPUT_TYPE_STREET, LanguageStrings.app_base_inputrequirederror_textview_text(), "", "");
          view.showErrorMargin();
        } else {
          view.showInlineTip(InlineTipEditTextEu.INPUT_TYPE_STREET, null, "", "");
          if (listenner != null) {
            listenner.complete(view.getInlineTipEditTextString());
          }
        }
      }
    });
    list.add(() -> checkStreet(view));
    return this;
  }

  public boolean checkStreet(InlineTipEditTextEu view) {
    String street = view.getInlineTipEditTextString();
    return !TextUtils.isEmpty(street);
  }

  public InlineVerificationHelperEu buildCity(InlineTipEditTextEu view) {
    return buildCityChecker(view, null);
  }

  public InlineVerificationHelperEu buildCityChecker(InlineTipEditTextEu view, EmailInputComplete listenner) {
    view.setEditTextOnFocusChangeListener((v, hasFocus) -> {
      if (!hasFocus) {
        if (!checkCity(view)) {
          view.showInlineTip(InlineTipEditTextEu.INPUT_TYPE_CITY, LanguageStrings.app_base_inputrequirederror_textview_text(), "", "");
          view.showErrorMargin();
        } else {
          view.showInlineTip(InlineTipEditTextEu.INPUT_TYPE_CITY, null, null, null);
          if (listenner != null) {
            listenner.complete(view.getInlineTipEditTextString());
          }
        }
      }
    });
    list.add(() -> checkCity(view));
    return this;
  }

  public boolean checkCity(InlineTipEditTextEu view) {
    String city = view.getInlineTipEditTextString();
    return !TextUtils.isEmpty(city);
  }
  public InlineVerificationHelperEu buildCounty(InlineTipEditTextEu view) {
    return buildCountyChecker(view, null);
  }

  public InlineVerificationHelperEu buildCountyChecker(InlineTipEditTextEu view, EmailInputComplete listenner) {
    view.setEditTextOnFocusChangeListener((v, hasFocus) -> {
      if (!hasFocus) {
        if (!checkCounty(view)) {
          view.showInlineTip(InlineTipEditTextEu.INPUT_TYPE_COUNTY, LanguageStrings.app_base_inputrequirederror_textview_text(), "", "");
          view.showErrorMargin();
        } else {
          view.showInlineTip(InlineTipEditTextEu.INPUT_TYPE_COUNTY, null, "", "");
          if (listenner != null) {
            listenner.complete(view.getInlineTipEditTextString());
          }
        }
      }
    });
    list.add(() -> checkCounty(view));
    return this;
  }

  public boolean checkCounty(InlineTipEditTextEu view) {
    String county = view.getInlineTipEditTextString();
    return !TextUtils.isEmpty(county);
  }

  public InlineVerificationHelperEu buildPostcode(InlineTipEditTextEu view) {
    return buildPostcodeChecker(view, null);
  }

  public InlineVerificationHelperEu buildPostcodeChecker(InlineTipEditTextEu view, EmailInputComplete listenner) {
    view.setEditTextOnFocusChangeListener((v, hasFocus) -> {
      if (!hasFocus) {
        if (!checkPostcode(view)) {
          view.showInlineTip(InlineTipEditTextEu.INPUT_TYPE_POSTCODE, LanguageStrings.app_base_inputrequirederror_textview_text(), "", "");
          view.showErrorMargin();
        } else {
          view.showInlineTip(InlineTipEditTextEu.INPUT_TYPE_POSTCODE, null, "", "");
          if (listenner != null) {
            listenner.complete(view.getInlineTipEditTextString());
          }
        }
      }
    });
    list.add(() -> checkPostcode(view));
    return this;
  }

  public boolean checkPostcode(InlineTipEditTextEu view) {
    String postcode = view.getInlineTipEditTextString();
    return !TextUtils.isEmpty(postcode);
  }


  private void disableBtnClick(boolean b) {

    btnNext.setEnabled(b);
    btnNext.setClickable(b);
  }

  public void checkInline() {
    for (int i = 0; i < list.size(); i++) {
      boolean checkRes = list.get(i).check();
      if (checkRes && (i == list.size() - 1)) {

        if (!TextUtils.isEmpty(editPassword)) {
          if (mailFormat(editPassword)) {
            disableBtnClick(true);
          }
        }


      } else {
        if (!checkRes) {
          disableBtnClick(false);
          break;
        }
      }
    }
  }

  public void checkInlineAccount() {
    for (int i = 0; i < list.size(); i++) {
      boolean checkRes = list.get(i).check();
      if (checkRes && (i == list.size() - 1)) {
        disableBtnClick(true);


      } else {
        if (!checkRes) {
          disableBtnClick(false);
          break;
        }
      }
    }
  }


  interface Checker {
    /**
     * check
     *
     * @return return is true or false
     */
    public boolean check();
  }

  public interface EmailInputComplete {
    /**
     * check
     *
     * @return return is true or false
     */
    public boolean complete(String str);
  }

  public void clear() {
    btnNext = null;
  }


  /**
   * 根据邮箱校验是否包含@前部的字符
   *
   * @return
   */
  private boolean mailFormat(String password) {
    boolean result = true;

    if (TextUtils.isEmpty(password)) {
      return false;
    }

    //如果未输入邮箱则不校验---不校验
    if (TextUtils.isEmpty(mail)) {
      return false;
    }

    //如果输入邮箱未非法邮箱---不校验
    if (!CommonUtils.checkEmail(mail)) {
      return false;
    }
    String[] split = mail.split(MAIL_TAG);
    //取邮箱前面部分校验
    String mailHeader = split[0];
    //健壮性校验，截取字符不能为空
    if (TextUtils.isEmpty(mailHeader)) {
      return false;
    }

    if (password.contains(mailHeader)) {
      result = false;
    }

    return result;
  }
}
