package com.chervon.libBase.model;

import com.google.gson.annotations.SerializedName;

/**
 * @Author: 184862
 * @description WIFI设备返回当前状态是否可升级
 * @CreateDate: 2023/12/19
 * @UpdateDate: 2023/12/19
 */
public class WifiUpgradeAllow {


  /**
   * state : {"reported":{"1001":1}}
   */

  private StateBean state;

  public StateBean getState() {
    return state;
  }

  public void setState(StateBean state) {
    this.state = state;
  }

  public static class StateBean {
    /**
     * reported : {"1001":1}
     */

    private ReportedBean reported;

    public ReportedBean getReported() {
      return reported;
    }

    public void setReported(ReportedBean reported) {
      this.reported = reported;
    }

    public static class ReportedBean {
      /**
       * 1001 : 1
       */

      @SerializedName("1001")
      private int upgradeAble = -1;

      public int getUpgradeAble() {
        return upgradeAble;
      }

      public void setUpgradeAble(int upgradeAble) {
        this.upgradeAble = upgradeAble;
      }
    }
  }
}
