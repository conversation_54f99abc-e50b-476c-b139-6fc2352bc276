package com.chervon.libBase.ui;

import static com.chervon.libBase.utils.APPConstants.RESPONSE_FAIL;
import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libRouter.RouterConstants.KEY_FROM;
import static com.chervon.libRouter.RouterConstants.KEY_TO;

import android.app.Activity;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.databinding.ViewDataBinding;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.Observer;
import androidx.lifecycle.ViewModelProvider;
import androidx.navigation.NavController;
import androidx.navigation.Navigation;

import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.chervon.libBase.R;
import com.chervon.libBase.ui.viewmodel.BaseViewModel;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

/**
 * @ProjectName: app
 * @Package: com.chervon.libBase.ui
 * @ClassName: BaseFragment
 * @Description: the base  Fragment of app
 * @Author: wangheng
 * @CreateDate: 2022/4/19 下午7:02
 * @UpdateUser: wangheng
 * @UpdateDate: 2022/4/19 下午7:02
 * @UpdateRemark: new
 * @Version: 1.0
 */
public abstract class BaseEnhanceFragment<M extends BaseViewModel,B extends ViewDataBinding> extends BaseFragment <M>{
    protected B mViewDataBinding;
    protected int mFrom;
    protected int mPageTo;
    public boolean isVisible = false;
    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
      if(mViewDataBinding==null){
          mViewDataBinding =(B) DataBindingUtil.inflate(inflater, getLayoutId(), container, false);
          getBaseData();
          initViews(mViewDataBinding);
          setPresenter();

      }

        return mViewDataBinding.getRoot();
    }

    private void getBaseData() {

        Bundle bundle= getArguments();
        if(bundle!=null){
              mFrom = bundle.getInt(KEY_FROM);
              mPageTo = bundle.getInt(KEY_TO);
        }
    }

    private void setPresenter() {
        Method m1 = null;
        try {
            m1 = mViewDataBinding.getClass().getMethod("setController",this.getClass());
            m1.invoke(mViewDataBinding,this);
        } catch (NoSuchMethodException e) {
            e.printStackTrace();
        } catch (InvocationTargetException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
    }


    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
            if(getLifecycleOwner()!=null){
                mViewModel.mLiveData.observe(getLifecycleOwner(),   new Observer<BaseUistate>() {
                    @Override
                    public void onChanged(BaseUistate uiState) {
                        if(uiState.getState()==RESPONSE_FAIL){
                            if (!TextUtils.isEmpty(uiState.getMessage())) {
                                ToastUtils.showLong(uiState.getMessage());
                            }
                        }

                            onUiLiveDataChanged(uiState);

                    }
                });
            }
    }

    protected abstract void onUiLiveDataChanged(BaseUistate uiState);

    protected abstract LifecycleOwner getLifecycleOwner();


    public void goToNextFragment(int action, Bundle bundle) {
        NavController controller = Navigation.findNavController(getActivity(), R.id.nav_host_fragment_content_main);
        controller.navigate(action, bundle);
    }

    public void goToNextFragment(int action) {
        NavController controller = Navigation.findNavController(getActivity(), R.id.nav_host_fragment_content_main);
        controller.navigate(action);
    }


    public Bundle getBundle(){
        return new Bundle();
    }

   public void setTitle(String title){
       if(isAdded()){
        Activity activity= getActivity();
           ((BaseActivity)getActivity()).setTitleName(title);
       }
   }

    @Override
    public void onDetach() {
        super.onDetach();
    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
    }

    @Override
    public void onStop() {
        super.onStop();

    }

    public interface OnkeyBackListener {
        /**
         * Called when a view has been clicked.
         *
         * @param The view that was clicked.
         */
        boolean OnkeyBack();
    }

  @Override
  public void onDestroy() {
    super.onDestroy();
  }

  @Override
  public void onDestroyView() {
    super.onDestroyView();
    mViewDataBinding=null;
  }

  @Override
  public void onResume() {
    super.onResume();
    if(this instanceof  OnkeyBackListener){
      if (isAdded()) {
        ((BaseActivity) getActivity()).setKeyBackListener((OnkeyBackListener)this);
      }
    }
    isVisible = true;
  }

    @Override
    public void onPause() {
        super.onPause();
        isVisible = false;
    }
}
