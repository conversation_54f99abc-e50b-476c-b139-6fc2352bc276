package com.chervon.libBase.utils;

import static com.chervon.libBase.model.AppConstants.RM_01;
import static com.chervon.libBase.model.AppConstants.RM_02;
import static com.chervon.libBase.model.AppConstants.ZT_01;
import static com.chervon.libBase.model.AppConstants.ZT_02;
import static com.chervon.libBase.model.AppConstants.ZT_03;

import android.content.Context;
import android.content.Intent;
import android.content.res.AssetManager;
import android.content.res.Configuration;
import android.net.Uri;
import android.os.Build;
import android.text.TextUtils;

import com.blankj.utilcode.util.LogUtils;
import com.chervon.libDB.entities.DeviceInfo;
import com.clj.fastble.data.BleDevice;

import java.io.IOException;
import java.util.Collection;
import java.util.Locale;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class CommonUtils {

  public static final String SPACE_STR = " ";

  public static boolean isNullOrEmpty(Collection collection) {
    return collection == null || collection.isEmpty();
  }

  /**
   * 验证邮箱
   *
   * @param email
   * @return
   */
  public static boolean checkEmail(String email) {
    boolean flag = false;
    try {


       //旧的正则表达式 String str = "^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\\.[a-zA-Z0-9-]+)*\\.[a-zA-Z0-9]{2,6}$";
      //新版正则表达式
      String str = "^.+@.+\\..+$";
      Pattern p = Pattern.compile(str);
      Matcher m = p.matcher(email);
      return m.matches();

    } catch (Exception e) {
      flag = false;
    }

    return flag;
  }

//          String str = "^\\s*\\w+(?:\\.{0,1}[\\w-]+)*@[a-zA-Z0-9]+(?:[-.][a-zA-Z0-9]+)*\\.[a-zA-Z]+\\s*$";

  /**
   * NotSpecialChars
   *
   * @param str
   * @return
   */
  public static boolean checkNotSpecialChars(String str) {
    boolean flag = false;
    try {
      String check = "^[A-Za-z0-9]+$";
      Pattern regex = Pattern.compile(check);
      Matcher matcher = regex.matcher(str);
      flag = matcher.matches();
    } catch (Exception e) {
      flag = false;
    }
    return flag;
  }

  public static boolean checkPassword(String password) {
    if (password == null) {
      return false;
    }
    if (password.length() < 8) {
      return false;
    }

    boolean matchesLetter = password.matches(".*\\d.*") && password.matches(".*[a-zA-Z].*");
    boolean matchesNumber = password.matches(".*[0-9].*");
    boolean specialCharacter = password.matches(".*[`~!@#$%^&*()+=|{}':;',\\\\[\\\\].<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。，、？].*");
    boolean isContainsSpace = password.contains(SPACE_STR);
    return matchesLetter && matchesNumber & specialCharacter & (!isContainsSpace);

  }


  public static boolean isAssetsFileExists(Context context, String filename) {
    AssetManager assetManager = context.getAssets();
    try {
      String[] names = assetManager.list("");
      for (int i = 0; i < names.length; i++) {

        if (names[i].equalsIgnoreCase(filename.trim())) {
          return true;
        }
      }
    } catch (IOException e) {
      e.printStackTrace();
      return false;
    }
    return false;
  }

  public static void goToWebPage(Context context, String url) {

    if (TextUtils.isEmpty(url)) {
      return;
    }

    Intent intent = new Intent();
    intent.setAction("android.intent.action.VIEW");
    Uri content_url = Uri.parse(url);
    intent.setData(content_url);
    context.startActivity(intent);
  }


  public static DeviceInfo createDeviceInfo(BleDevice bledevice) {
    DeviceInfo deviceInfo = new DeviceInfo();
    deviceInfo.setMac(bledevice.getMac());
    return deviceInfo;
  }


  /**
   * 根据deviceId判断当前的设备是不是Z6设备
   *
   * @param deviceId 设备的deviceId
   * @return boolean true 是z6设备  false 不是z6设备
   */
  public static boolean isZ6Device(String deviceId) {
    if (TextUtils.isEmpty(deviceId)) {
      return false;
    }
    try {
      deviceId = deviceId.substring(1, 5);
    } catch (Exception e) {
      return false;
    }
    return ZT_01.equalsIgnoreCase(deviceId)
      || ZT_02.equalsIgnoreCase(deviceId)
      || ZT_03.equalsIgnoreCase(deviceId);
  }

  public static boolean isRDevice(String deviceId) {
    if (TextUtils.isEmpty(deviceId)) {
      return false;
    }
    try {
      deviceId = deviceId.substring(1, 5);
    } catch (Exception e) {
      return false;
    }
    return RM_01.equalsIgnoreCase(deviceId)
            || RM_02.equalsIgnoreCase(deviceId);
  }
}
