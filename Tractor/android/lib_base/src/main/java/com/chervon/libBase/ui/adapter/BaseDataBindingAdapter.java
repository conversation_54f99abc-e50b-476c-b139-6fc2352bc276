package com.chervon.libBase.ui.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.databinding.DataBindingUtil;
import androidx.databinding.ViewDataBinding;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.gms.maps.SupportMapFragment;

/**
 * @ProjectName: app
 * @Package: com.chervon.libBase.ui.adapter
 * @ClassName: BaseDataBindingAdapter
 * @Description: the base of  DataBindingAdapter
 * @Author: wangheng
 * @CreateDate: 2022/4/19 下午7:02
 * @UpdateUser: wangheng
 * @UpdateDate: 2022/4/19 下午7:02
 * @UpdateRemark: new
 * @Version: 1.0
 */
public abstract class BaseDataBindingAdapter<T extends RecyclerView.ViewHolder> extends RecyclerView.Adapter {
    protected Context mContext;
    public BaseDataBindingAdapter(Context context) {
        mContext=context;
    }
    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        LayoutInflater inflater = LayoutInflater.from(mContext);
        ViewDataBinding viewDataBinding = null;
        if (getLayoutId() != null) {
            viewDataBinding = DataBindingUtil.inflate(inflater, getLayoutId(), parent, false);
        }
        return new MyViewHolder(viewDataBinding);
    }



    protected abstract Integer getLayoutId();

    public void onDestroy() {
      mContext=null;
    }


    public  class MyViewHolder extends RecyclerView.ViewHolder {
       public ViewDataBinding viewDataBinding;
        public MyViewHolder(ViewDataBinding viewDataBinding) {
            super(viewDataBinding.getRoot());
           this. viewDataBinding=viewDataBinding;
        }
    }

}
