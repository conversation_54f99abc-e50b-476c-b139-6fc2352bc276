package com.chervon.libBase.ui.widget;


import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.drawable.Drawable;
import android.text.Editable;
import android.text.InputFilter;
import android.text.InputType;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.databinding.BindingAdapter;
import androidx.databinding.InverseBindingAdapter;
import androidx.databinding.InverseBindingListener;

import com.chervon.libBase.R;
import com.chervon.libBase.utils.CommonUtils;
import com.chervon.libBase.utils.UiHelper;
import me.jessyan.autosize.AutoSizeCompat;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleOobe.ui.widget.supertooltip
 * @ClassName: InlineTipEditText
 * @Description: the InlineTip   of EditText
 * @Author: wangheng
 * @CreateDate: 2022/6/18 下午7:02
 * @UpdateUser: hyman
 * @UpdateDate: 2023/12/4 上午10:51
 * @UpdateRemark: 迭代用户注册 jackson需求
 * @Version: 1.1
 */
public class InlineTipEditText2 extends ConstraintLayout {
  private final ViewGroup editLayout;

  private EditText mEditText;
  private Drawable etShapeNomarl;
  private Drawable etShapeFocus;
  private Drawable etShapeError;

  private TextChangedListenner mTextChangedListenner;
  private OnFocusChangeListener mOnFocusChangeListener;
  //自定义FocusChangeListener
  private CVOnFocusChangeListener cvOnFocusChangeListener;

  //普通的提示框
  private TextView tvWarningTip = null;

  //第一行提示框
  private ConstraintLayout consCharAndSpaces = null;
  private ImageView imgResultCharAndSpaces = null;
  private TextView tvWarningTipCharAndSpaces = null;

  //第二行提示框
  private ConstraintLayout consNumberAndLetters = null;
  private ImageView imgResultNumberAndLetters = null;
  private TextView tvWarningTipNumberAndLetters = null;

  //第三行
  private ConstraintLayout consMailFormat = null;
  private ImageView imgResultMailFormat = null;
  private TextView tvWarningTipMailFormat = null;

  public static final String INPUT_TYPE_EMAIL = "email";
  public static final String INPUT_TYPE_PWD = "pwd";
  public static final String INPUT_TYPE_PWD_NORMAL = "pwd_normal";

  public static final String INPUT_TYPE_CONFIRM_PWD = "confirm_pwd";

  public static final String INPUT_TYPE_FIRST_NAME = "first_name";
  public static final String INPUT_TYPE_LAST_NAME = "last_name";


  private String mail = null;
  private static final String MAIL_TAG = "@";

  public InlineTipEditText2(@NonNull Context context) {
    this(context, null);
  }

  public InlineTipEditText2(@NonNull Context context, @Nullable AttributeSet attrs) {
    this(context, attrs, 0);
  }

  public InlineTipEditText2(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
    super(context, attrs, defStyleAttr);

    ViewGroup view = (ViewGroup) LayoutInflater.from(context).inflate(R.layout.moudle_oobe_et_inline_tip2, this);
    @SuppressLint("CustomViewStyleable")
    TypedArray obtainStyledAttributes = getContext().obtainStyledAttributes(attrs, com.chervon.libBase.R.styleable.inline_tip_EditText);
    int iconPassword = (int) obtainStyledAttributes.getInt(com.chervon.libBase.R.styleable.inline_tip_EditText_icon_password, 0);
    String hint = obtainStyledAttributes.getString(com.chervon.libBase.R.styleable.inline_tip_EditText_hint_edit);
    // mEditText = (EditText) view.findViewById(R.id.etPassword);
    editLayout = (ViewGroup) findViewById(R.id.linear_edit);
    int count = editLayout.getChildCount();

    for (int i = 0; i < count; i++) {
      View child = editLayout.getChildAt(i);
      if (child instanceof EditText) {
        mEditText = (EditText) child;
      }
    }

    int maxCharLength = (int) obtainStyledAttributes.getInt(com.chervon.libBase.R.styleable.inline_tip_EditText_max_length, 0);
    if (maxCharLength != 0) {
      mEditText.setFilters(new InputFilter[]{new InputFilter.LengthFilter(maxCharLength)});
    }


    etShapeFocus = getResources().getDrawable(R.drawable.base_et_shape_focus);
    etShapeNomarl = getResources().getDrawable(R.drawable.module_login_et_shape);
    etShapeError = getResources().getDrawable(com.chervon.libBase.R.drawable.base_et_shape_error);

    mEditText.setOnFocusChangeListener(new OnFocusChangeListener() {
      @Override
      public void onFocusChange(View v, boolean hasFocus) {
        if (hasFocus) {
          editLayout.setBackground(etShapeFocus);
        } else {
          editLayout.setBackground(etShapeNomarl);
        }

        if (mOnFocusChangeListener != null) {
          mOnFocusChangeListener.onFocusChange(v, hasFocus);
        }

        //为邮箱定制功能，其他平台化功能不需要传入 cvOnFocusChangeListener
        if (null!=cvOnFocusChangeListener&&!hasFocus){
          String editText = mEditText.getText().toString();
          if (!TextUtils.isEmpty(editText)){
            if (CommonUtils.checkEmail(editText)){
              cvOnFocusChangeListener.checkEmailWithNet(v,hasFocus,editText);
            }
          }
        }
      }
    });

    //普通提示框
    tvWarningTip = findViewById(R.id.tvWarningTip);
    tvWarningTipCharAndSpaces = findViewById(R.id.tvWarningTipCharAndSpaces);
    consCharAndSpaces = findViewById(R.id.consCharAndSpaces);
    imgResultCharAndSpaces = findViewById(R.id.imgResultCharAndSpaces);

    consNumberAndLetters = findViewById(R.id.consNumberAndLetters);
    imgResultNumberAndLetters = findViewById(R.id.imgResultNumberAndLetters);
    tvWarningTipNumberAndLetters = findViewById(R.id.tvWarningTipNumberAndLetters);

    consMailFormat = findViewById(R.id.consMailFormat);
    imgResultMailFormat = findViewById(R.id.imgResultMailFormat);
    tvWarningTipMailFormat = findViewById(R.id.tvWarningTipMailFormat);

    mEditText.setHint(hint);
    ImageButton imageButton = findViewById(R.id.ibPassword);
    if (iconPassword == 0) {
      mEditText.setInputType(InputType.TYPE_CLASS_TEXT);
      imageButton.setVisibility(GONE);
    } else {
      mEditText.setInputType(InputType.TYPE_CLASS_TEXT | InputType.TYPE_TEXT_VARIATION_PASSWORD);
      imageButton.setVisibility(VISIBLE);
    }

    imageButton.setOnClickListener(new OnClickListener() {
      @Override
      public void onClick(View view) {
        UiHelper.switchPasswordVisibility(imageButton, mEditText, view);
      }
    });
    obtainStyledAttributes.recycle();
  }

  public void showInlineTip(String type, String tipCharSpaces, String tipNumberAndLetters,String tipMailFormat) {



    if (type.equals(INPUT_TYPE_PWD_NORMAL)) {
      consCharAndSpaces.setVisibility(VISIBLE);
      consNumberAndLetters.setVisibility(VISIBLE);
      consMailFormat.setVisibility(VISIBLE);
      tvWarningTipCharAndSpaces.setText(tipCharSpaces);
      tvWarningTipCharAndSpaces.setTextColor(getResources().getColor(R.color.color_oobe_sign_up_right));


      tvWarningTipNumberAndLetters.setText(tipNumberAndLetters);
      tvWarningTipNumberAndLetters.setTextColor(getResources().getColor(R.color.color_oobe_sign_up_right));

      tvWarningTipMailFormat.setText(tipMailFormat);
      tvWarningTipMailFormat.setTextColor(getResources().getColor(R.color.color_oobe_sign_up_right));


      imgResultNumberAndLetters.setVisibility(GONE);
      imgResultCharAndSpaces.setVisibility(GONE);
      imgResultMailFormat.setVisibility(GONE);


    } else if (type.equals(INPUT_TYPE_EMAIL)) {
      consCharAndSpaces.setVisibility(GONE);
      consNumberAndLetters.setVisibility(GONE);
      consMailFormat.setVisibility(GONE);

      if (!TextUtils.isEmpty(tipCharSpaces)) {
        tvWarningTip.setText(tipCharSpaces);
        tvWarningTip.setVisibility(VISIBLE);
      } else {
        tvWarningTip.setVisibility(GONE);
      }
    } else if (type.equals(INPUT_TYPE_PWD)) {
      if (!TextUtils.isEmpty(tipCharSpaces)) {
        consCharAndSpaces.setVisibility(VISIBLE);
        tvWarningTipCharAndSpaces.setText(tipCharSpaces);
        tvWarningTipCharAndSpaces.setTextColor(isCharAndSpaces() ? getResources().getColor(R.color.color_oobe_sign_up_right) : getResources().getColor(R.color.color_oobe_sign_up_error));
        imgResultCharAndSpaces.setImageResource(isCharAndSpaces() ? R.drawable.ic_oobe_register_pwd_right : R.drawable.ic_oobe_register_pwd_error);
        imgResultCharAndSpaces.setVisibility(VISIBLE);
      } else {
        consCharAndSpaces.setVisibility(GONE);
      }

      if (!TextUtils.isEmpty(tipNumberAndLetters)) {
        consNumberAndLetters.setVisibility(VISIBLE);
        tvWarningTipNumberAndLetters.setText(tipNumberAndLetters);
        tvWarningTipNumberAndLetters.setTextColor(isNumberAndLetters() ? getResources().getColor(R.color.color_oobe_sign_up_right) : getResources().getColor(R.color.color_oobe_sign_up_error));
        imgResultNumberAndLetters.setImageResource(isNumberAndLetters() ? R.drawable.ic_oobe_register_pwd_right : R.drawable.ic_oobe_register_pwd_error);
        imgResultNumberAndLetters.setVisibility(VISIBLE);

      } else {
        consNumberAndLetters.setVisibility(GONE);
      }

      if (!TextUtils.isEmpty(tipMailFormat)){
        consMailFormat.setVisibility(VISIBLE);
        tvWarningTipMailFormat.setText(tipMailFormat);
        tvWarningTipMailFormat.setTextColor(mailFormat() ? getResources().getColor(R.color.color_oobe_sign_up_right) : getResources().getColor(R.color.color_oobe_sign_up_error));
        imgResultMailFormat.setImageResource(mailFormat() ? R.drawable.ic_oobe_register_pwd_right : R.drawable.ic_oobe_register_pwd_error);
        imgResultMailFormat.setVisibility(VISIBLE);
      }else {
        consMailFormat.setVisibility(GONE);

      }

    } else if (type.equals(INPUT_TYPE_CONFIRM_PWD)) {


      if (!TextUtils.isEmpty(tipCharSpaces)) {
        showErrorMargin();
        tvWarningTip.setText(tipCharSpaces);
        tvWarningTip.setVisibility(VISIBLE);
      } else {
        showNormalMargin();
        tvWarningTip.setVisibility(GONE);
      }


    }else if (type.equals(INPUT_TYPE_FIRST_NAME)){

      consCharAndSpaces.setVisibility(GONE);
      consNumberAndLetters.setVisibility(GONE);
      consMailFormat.setVisibility(GONE);

      if (!TextUtils.isEmpty(tipCharSpaces)) {
        showErrorMargin();
        tvWarningTip.setText(tipCharSpaces);
        tvWarningTip.setVisibility(VISIBLE);
      } else {
        showNormalMargin();
        tvWarningTip.setVisibility(GONE);
      }

    }else if (type.equals(INPUT_TYPE_LAST_NAME)){

      consCharAndSpaces.setVisibility(GONE);
      consNumberAndLetters.setVisibility(GONE);
      consMailFormat.setVisibility(GONE);

      if (!TextUtils.isEmpty(tipCharSpaces)) {
        showErrorMargin();
        tvWarningTip.setText(tipCharSpaces);
        tvWarningTip.setVisibility(VISIBLE);
      } else {
        showNormalMargin();
        tvWarningTip.setVisibility(GONE);
      }

    }


  }

  public void showInTipsForMargin(String type, String tipCharSpaces, String tipNumberAndLetters,String tipMailFormat) {

      if (type.equals(INPUT_TYPE_PWD)&&!TextUtils.isEmpty(tipCharSpaces) && !TextUtils.isEmpty(tipNumberAndLetters) && !TextUtils.isEmpty(tipMailFormat)) {

        if (isCharAndSpaces() && isNumberAndLetters() && mailFormat()) {
          showNormalMargin();
        }else {
          showErrorMargin();
        }
      }
  }

  /**
   * 校验规则1：必须要包含8-16字符，且不包含空格
   *
   * @return
   */
  private boolean isCharAndSpaces() {
    boolean result = false;
    String edit_str = mEditText.getText().toString();
    if (!TextUtils.isEmpty(edit_str)) {
//      if (edit_str.length() >= 8 && edit_str.length() <= 16) {
      if (edit_str.length() >= 8) {

        if (!edit_str.contains(" ")) {
          return true;
        }
      }
    }
    return result;
  }

  /**
   * 校验规则 必须包含字符和字母
   *
   * @return
   */
  private boolean isNumberAndLetters() {
    boolean result = false;


    String edit_str = mEditText.getText().toString();
    if (!TextUtils.isEmpty(edit_str)) {
      boolean matchesLetter = edit_str.matches(".*\\d.*") && edit_str.matches(".*[a-zA-Z].*");
      boolean matchesNumber = edit_str.matches(".*[0-9].*");
      boolean  specialCharacter = edit_str.matches(".*[`~!@#$%^&*()+=|{}':;',\\\\[\\\\].<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。，、？].*");
      result = matchesLetter && matchesNumber&specialCharacter;
    }

    return result;
  }

  /**
   * 根据邮箱校验是否包含@前部的字符
   *
   * @return
   */
  private boolean mailFormat() {
    boolean result = true;

    //如果未输入邮箱则不校验---不校验
    if (TextUtils.isEmpty(mail)){
      return true;
    }

    //如果输入邮箱未非法邮箱---不校验
    if (!CommonUtils.checkEmail(mail)){
      return true;
    }
    String[] split = mail.split(MAIL_TAG);
    //取邮箱前面部分校验
    String mailHeader = split[0];
    //健壮性校验，截取字符不能为空
    if (TextUtils.isEmpty(mailHeader)){
      return true;
    }
    String edit_str = mEditText.getText().toString();

    if (!TextUtils.isEmpty(edit_str)) {

      if (edit_str.contains(mailHeader)){
        result = false;
      }

      return result;
    }

    return result;
  }

  @BindingAdapter(value = "text_edit", requireAll = false)
  public static void setInlineTipEditText(InlineTipEditText2 inlineTipEditText, String str) {
    inlineTipEditText.setText(str);

  }

  @BindingAdapter(value = "hint_edit", requireAll = false)
  public static void setInlineTipHint(InlineTipEditText2 inlineTipEditText, String str) {
    inlineTipEditText.setHint(str);
  }

  private void setHint(String str) {
    mEditText.setHint(str);
  }

  public void setText(String str) {
    mEditText.setText(str);
  }


  @InverseBindingAdapter(attribute = "text_edit")
  public static String getInlineTipEditText(InlineTipEditText2 inlineTipEditText) {
    return inlineTipEditText.getInlineTipEditTextString();
  }

  public String getInlineTipEditTextString() {
    return mEditText.getText().toString();
  }


  public void addTextChangedListener(TextWatcher watcher) {
    mEditText.addTextChangedListener(watcher);
  }


  @InverseBindingAdapter(attribute = "text_edit", event = "philprogressAttrChanged")
  public static String getPhilProgress(InlineTipEditText2 seekBar) {
    return seekBar.getInlineTipEditTextString();
  }

  private static InverseBindingListener mInverseBindingListener;

  @BindingAdapter(value = {"philprogressAttrChanged"}, requireAll = false)
  public static void setPhilProgressAttrChanged(InlineTipEditText2 seekBar, InverseBindingListener inverseBindingListener) {
    if (inverseBindingListener == null) {

    } else {
      mInverseBindingListener = inverseBindingListener;
    }
  }

  public void clear() {
    mInverseBindingListener = null;
  }

  public void setMail(String email) {
    this.mail = email;
  }

  public interface TextChangedListenner {
    /**
     * when text changed
     *
     * @param str text
     */
    public void textChanged(String str);
  }

  public TextChangedListenner getmTextChangedListenner() {
    return mTextChangedListenner;
  }

  public void setmTextChangedListenner(TextChangedListenner mTextChangedListenner) {
    this.mTextChangedListenner = mTextChangedListenner;


    mEditText.addTextChangedListener(new TextWatcher() {
      @Override
      public void beforeTextChanged(CharSequence s, int start, int count, int after) {

      }

      @Override
      public void onTextChanged(CharSequence s, int start, int before, int count) {


      }

      @Override
      public void afterTextChanged(Editable s) {
        if (mTextChangedListenner != null) {
          mTextChangedListenner.textChanged(s.toString());
        }

      }
    });

  }

  public void setEditTextOnFocusChangeListener(OnFocusChangeListener l) {
    mOnFocusChangeListener = l;

  }

  public void setCvOnFocusChangeListener(CVOnFocusChangeListener cvOnFocusChangeListener) {
    this.cvOnFocusChangeListener = cvOnFocusChangeListener;
  }

  public void setPassWordType() {
    mEditText.setInputType(InputType.TYPE_CLASS_TEXT | InputType.TYPE_TEXT_VARIATION_PASSWORD);
  }

  public void showNormalMargin(){
    if (null!=editLayout){
      editLayout.setBackground(etShapeNomarl);
    }
  }

  public void showErrorMargin(){
    if (null!=editLayout){
      editLayout.setBackground(etShapeError);
    }
  }

  @Override
  public LayoutParams generateLayoutParams(AttributeSet attrs) {
    AutoSizeCompat.autoConvertDensityOfGlobal(getResources());
    AutoSizeCompat.autoConvertDensity(getResources(), 1334, false);
    return super.generateLayoutParams(attrs);
  }

  public interface CVOnFocusChangeListener {
    /**
     * Called when the focus state of a view has changed.
     *
     * @param v The view whose state has changed.
     * @param hasFocus The new focus state of v.
     */
    void checkEmailWithNet(View v, boolean hasFocus,String editText);
  }
}
