package com.chervon.libBase.ui.widget;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Rect;
import android.os.Handler;
import android.os.Message;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import java.util.ArrayList;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;

/**
 * 创建人：lwd
 * 时  间：2018/4/24 15:30
 */
public class DatePickerView extends View {

  private Context context;
  /**
   * 新增字段 控制是否首尾相接循环显示 默认为循环显示
   */
  private boolean loop = true;
  /**
   * text之间间距和minTextSize之比
   */
  public static final float MARGIN_ALPHA = 2.0f;
  /**
   * 自动回滚到中间的速度
   */
  public static final float SPEED = 6;
  private List<String> mDataList;
  /**
   * 选中的位置，这个位置是mDataList的中心位置，一直不变
   */
  private int mCurrentSelected;
  private Paint mPaint, nPaint;
  private float mMaxTextSize = 40;
  private float mMinTextSize = 10;
  private float mMaxTextAlpha = 255;
  private float mMinTextAlpha = 120;
  private int mViewHeight;
  private int mViewWidth;
  private float mLastDownY;


  /**
   * 滑动的距离
   */
  private float mMoveLen = 0;
  private boolean isInit = false;
  private boolean canScroll = true;
  private onSelectListener mSelectListener;
  private Timer timer;
  private MyTimerTask mTask;

  private Handler updateHandler = new Handler() {
    @Override
    public void handleMessage(Message msg) {
      // 增加阻尼效果
      float deceleration = 0.95f;
      mMoveLen *= deceleration;
      
      if (Math.abs(mMoveLen) < 0.5f) {
        mMoveLen = 0;
        if (mTask != null) {
          mTask.cancel();
          mTask = null;
          performSelect();
        }
      } else {
        // 使用更平滑的减速效果
        float direction = mMoveLen / Math.abs(mMoveLen);
        mMoveLen -= direction * Math.min(Math.abs(mMoveLen) * 0.2f, SPEED);
      }
      invalidate();
    }
  };

  public DatePickerView(Context context, AttributeSet attrs) {
    super(context, attrs);
    this.context = context;
    init();
  }

  public void setOnSelectListener(onSelectListener listener) {
    mSelectListener = listener;
  }

  private void performSelect() {
    if (mSelectListener != null) {
      mSelectListener.onSelect(mDataList.get(mCurrentSelected));
    }
  }

  public int getmCurrentSelected() {
    return mCurrentSelected;
  }

  public void setData(List<String> datas) {
    mDataList = datas;
    mCurrentSelected = datas.size() / 4;
    invalidate();
  }

  /**
   * 选择选中的item的index
   */
  public void setSelected(int selected) {
    mCurrentSelected = selected;
    if (loop) {
      int distance = mDataList.size() / 2 - mCurrentSelected;
      if (distance < 0) {
        for (int i = 0; i < -distance; i++) {
          moveHeadToTail();
          mCurrentSelected--;
        }
      } else if (distance > 0) {
        for (int i = 0; i < distance; i++) {
          moveTailToHead();
          mCurrentSelected++;
        }
      }
    }
    invalidate();
  }

  /**
   * 选择选中的内容
   */
  public void setSelected(String mSelectItem) {
    for (int i = 0; i < mDataList.size(); i++) {
      if (mDataList.get(i).equals(mSelectItem)) {
        setSelected(i);
        break;
      }
    }
  }

  private void moveHeadToTail() {
    if (loop) {
      String head = mDataList.get(0);
      mDataList.remove(0);
      mDataList.add(head);
    }
  }

  private void moveTailToHead() {
    if (loop) {
      String tail = mDataList.get(mDataList.size() - 1);
      mDataList.remove(mDataList.size() - 1);
      mDataList.add(0, tail);
    }
  }

  @Override
  protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
    super.onMeasure(widthMeasureSpec, heightMeasureSpec);
    mViewHeight = getMeasuredHeight();
    mViewWidth = getMeasuredWidth();
    // 按照View的高度计算字体大小
    mMaxTextSize = mViewHeight / 8f;
    mMinTextSize = mMaxTextSize / 1.5f;
    isInit = true;
    invalidate();
  }

  private void init() {
    timer = new Timer();
    mDataList = new ArrayList<>();
    //第一个paint(选中)
    mPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
    //mPaint.setStyle(Style.FILL);
    mPaint.setTextAlign(Paint.Align.CENTER);
    mPaint.setColor(Color.parseColor("#FF77BC1F"));
    //第二个paint(未选中)
    nPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
    //nPaint.setStyle(Style.FILL);
    nPaint.setTextAlign(Paint.Align.CENTER);
    nPaint.setColor(Color.parseColor("#999999"));
  }

  @Override
  protected void onDraw(Canvas canvas) {
    super.onDraw(canvas);
    // 根据index绘制view
    if (isInit) {
      drawData(canvas);
    }
  }

  private void drawData(Canvas canvas) {
    // 先绘制选中的text再往上往下绘制其余的text
    try {
    float scale = parabola(mViewHeight / 4.0f, mMoveLen);
    float size = (mMaxTextSize - mMinTextSize) * scale + mMinTextSize;
    mPaint.setTextSize(size);
    mPaint.setAlpha((int) ((mMaxTextAlpha - mMinTextAlpha) * scale + mMinTextAlpha));
    // text居中绘制，注意baseline的计算才能达到居中，y值是text中心坐标
    float x = (float) (mViewWidth / 2.0);
    float y = (float) (mViewHeight / 2.0 + mMoveLen);
    Paint.FontMetricsInt fmi = mPaint.getFontMetricsInt();
    float baseline = (float) (y - (fmi.bottom / 2.0 + fmi.top / 2.0));
    float wheelHeight = mViewHeight * 0.7f;
    float itemHeight = wheelHeight / 4; // 改为4等分，让间距变大
    
    // 只绘制分隔线，增加间距
    Paint linePaint = new Paint(Paint.ANTI_ALIAS_FLAG);
    linePaint.setColor(Color.parseColor("#E0E0E0"));
    linePaint.setStrokeWidth(2);
    canvas.drawLine(0, mViewHeight/2 - itemHeight/2, mViewWidth, mViewHeight/2 - itemHeight/2, linePaint);
    canvas.drawLine(0, mViewHeight/2 + itemHeight/2, mViewWidth, mViewHeight/2 + itemHeight/2, linePaint);

    if(mCurrentSelected>=mDataList.size()){
      mCurrentSelected=mDataList.size()-1;
    }
    canvas.drawText(mDataList.get(mCurrentSelected), x, baseline, mPaint);
    // 绘制上方data
    for (int i = 1; (mCurrentSelected - i) >= 0 && i <= 2; i++) {
      drawOtherText(canvas, i, -1);
    }

    // 绘制下方data
    for (int i = 1; (mCurrentSelected + i) < mDataList.size() && i <= 2; i++) {
      drawOtherText(canvas, i, 1);
    } }catch (Exception e){

    }

  }

  private float getDateTextHeight(Paint paint, String str) {

    Rect rect = new Rect();
    paint.getTextBounds(str, 0, str.length(), rect);
    int w = rect.width();
    return rect.height();
//        Paint.FontMetrics fontMetrics = paint.getFontMetrics();
//        return  fontMetrics.descent - fontMetrics.ascent;
//     //   return fontMetrics.bottom - fontMetrics.top;
  }

  /**
   * @param position 距离mCurrentSelected的差值
   * @param type     1表示向下绘制，-1表示向上绘制
   */
  private void drawOtherText(Canvas canvas, int position, int type) {
    float d = MARGIN_ALPHA * mMinTextSize * position + type * mMoveLen;
    float scale = parabola(mViewHeight / 4.0f, d);
    float size = (mMaxTextSize - mMinTextSize) * scale + mMinTextSize;
    nPaint.setTextSize(size);
    // 根据位置计算透明度，离中心越远越透明
    float alpha = (1 - Math.abs(position) * 0.3f) * mMaxTextAlpha;
    nPaint.setAlpha((int) alpha);
    float y = (float) (mViewHeight / 2.0 + type * d);
    Paint.FontMetricsInt fmi = nPaint.getFontMetricsInt();
    float baseline = (float) (y - (fmi.bottom / 2.0 + fmi.top / 2.0));
    canvas.drawText(mDataList.get(mCurrentSelected + type * position),
      (float) (mViewWidth / 2.0), baseline, nPaint);
  }

  /**
   * 抛物线
   *
   * @param zero 零点坐标
   * @param x    偏移量
   */
  private float parabola(float zero, float x) {
    float f = (float) (1 - Math.pow(x / zero, 2));
    return f < 0 ? 0 : f;
  }

  @Override
  public boolean onTouchEvent(MotionEvent event) {
    switch (event.getActionMasked()) {
      case MotionEvent.ACTION_DOWN:
        doDown(event);
        break;

      case MotionEvent.ACTION_MOVE:
        // 增加滑动灵敏度
        mMoveLen += (event.getY() - mLastDownY) * 1.2f;
        if (mMoveLen > MARGIN_ALPHA * mMinTextSize / 2) {
          if (!loop && mCurrentSelected == 0) {
            mLastDownY = event.getY();
            invalidate();
            return true;
          }
          if (!loop) {
            mCurrentSelected--;
          }
          // 往下滑超过离开距离
          moveTailToHead();
          mMoveLen = mMoveLen - MARGIN_ALPHA * mMinTextSize;
        } else if (mMoveLen < -MARGIN_ALPHA * mMinTextSize / 2) {
          if (mCurrentSelected == mDataList.size() - 1) {
            mLastDownY = event.getY();
            invalidate();
            return true;
          }
          if (!loop) {
            mCurrentSelected++;
          }
          // 往上滑超过离开距离
          moveHeadToTail();
          mMoveLen = mMoveLen + MARGIN_ALPHA * mMinTextSize;
        }
        mLastDownY = event.getY();
        invalidate();
        break;

      case MotionEvent.ACTION_UP:
        doUp();
        break;
    }
    return true;
  }

  private void doDown(MotionEvent event) {
    if (mTask != null) {
      mTask.cancel();
      mTask = null;
    }
    mLastDownY = event.getY();
  }

  private void doUp() {
    // 抬起手后mCurrentSelected的位置由当前位置move到中间选中位置
    if (Math.abs(mMoveLen) < 0.0001) {
      mMoveLen = 0;
      return;
    }
    if (mTask != null) {
      mTask.cancel();
      mTask = null;
    }
    mTask = new MyTimerTask(updateHandler);
    timer.schedule(mTask, 0, 10);
  }

  class MyTimerTask extends TimerTask {
    Handler handler;

    public MyTimerTask(Handler handler) {
      this.handler = handler;
    }

    @Override
    public void run() {
      handler.sendMessage(handler.obtainMessage());
    }
  }

  public interface onSelectListener {
    void onSelect(String text);
  }

  public void setCanScroll(boolean canScroll) {
    this.canScroll = canScroll;
  }

  @Override
  public boolean dispatchTouchEvent(MotionEvent event) {
    return canScroll && super.dispatchTouchEvent(event);
  }

  /**
   * 控制内容是否首尾相连
   */
  public void setIsLoop(boolean isLoop) {
    loop = isLoop;
  }

}
