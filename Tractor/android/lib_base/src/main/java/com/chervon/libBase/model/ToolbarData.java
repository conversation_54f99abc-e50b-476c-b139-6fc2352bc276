package com.chervon.libBase.model;

import android.view.View;

/**
 * @ProjectName: app
 * @Package: com.chervon.libBase.model
 * @ClassName: ToolbarData
 * @Description: Toolbar data class.
 * @Author: LangMeng
 * @CreateDate: 2022/4/22 19:34
 * @UpdateUser: LangMeng
 * @UpdateDate: 2022/4/22 19:34
 * @UpdateRemark: new class
 * @Version: 1.0
 */
public class ToolbarData {
  private String title;
  private android.view.View.OnClickListener click;
  private android.view.View.OnClickListener jumpNextClick;
  private android.view.View.OnClickListener titleClick;

  public ToolbarData(String title, View.OnClickListener click) {
    this.title = title;
    this.click = click;
  }


  public View.OnClickListener getTitleClick() {
    return titleClick;
  }

  public void setTitleClick(View.OnClickListener titleClick) {
    this.titleClick = titleClick;
  }

  public String getTitle() {
    return title;
  }

  public void setTitle(String title) {
    this.title = title;
  }

  public View.OnClickListener getClick() {
    return click;
  }

  public void setClick(View.OnClickListener click) {
    this.click = click;
  }

  public View.OnClickListener getJumpNextClick() {
    return jumpNextClick;
  }

  public void setJumpNextClick(View.OnClickListener jumpNextClick) {
    this.jumpNextClick = jumpNextClick;
  }

  public ToolbarData(View.OnClickListener click) {
    this.click = click;
  }
}

