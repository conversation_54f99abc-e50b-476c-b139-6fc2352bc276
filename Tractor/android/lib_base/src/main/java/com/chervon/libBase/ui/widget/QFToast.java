//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.chervon.libBase.ui.widget;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.View;
import android.widget.Toast;
/**
 * @ProjectName: app
 * @Package: com.chervon.libBase.ui.widget
 * @ClassName: QFToast
 * @Description: the custom of Toast
 * @Author: wangheng
 * @CreateDate: 2022/4/19 下午7:02
 * @UpdateUser: wangheng
 * @UpdateDate: 2022/4/19 下午7:02
 * @UpdateRemark: new
 * @Version: 1.0
 */
@SuppressLint({"ToastUseError"})
public class QFToast {
    public QFToast() {
    }

    public static Toast makeToast(Context context, String tips, int duration, int gravity, int offX, int offY) {
        return makeSimpleToast(context, tips, duration, false);
    }

    public static Toast makeToast(Context context, String tips, int duration, int gravity, int offX, int offY, boolean defaultBlackWhite) {
        Toast toast = makeSimpleToast(context, tips, duration, defaultBlackWhite);
        toast.setGravity(gravity, offX, offY);
        return toast;
    }

    public static Toast makeViewToast(Context context, View view, int duration, int gravity, int offX, int offY) {
        if (view == null) {
            return null;
        } else {
            Toast viewToast = new Toast(context.getApplicationContext());
            //ToastNougat.hook(viewToast);
            viewToast.setDuration(duration);
            viewToast.setGravity(gravity, offX, offY);
            viewToast.setView(view);
            return viewToast;
        }
    }

    public static void show(Context context, String text) {
        show(context, text, 0);
    }

    public static void show(Context context, String text, int duration) {
        show(context, text, duration, true);
    }

    public static void showByTheme(Context context, String text) {
        showByTheme(context, text, 0);
    }

    public static void showByTheme(Context context, String text, int duration) {
        show(context, text, duration, false);
    }

    private static void show(Context context, String text, int duration, boolean defaultBlackWhite) {
        if (context != null) {
            Toast toast = makeSimpleToast(context, text, duration, defaultBlackWhite);
            toast.show();
        }
    }


    public static void showIcon(Context context, String text, int icon, int duration) {
        showIcon(context, text, icon, duration, true);
    }



    public static void showIconByTheme(Context context, String text, int icon) {
        showIconByTheme(context, text, icon, 0);
    }

    public static void showIconByTheme(Context context, String text, int icon, int duration) {
        showIcon(context, text, icon, duration, false);
    }

    private static void showIcon(Context context, String text, int icon, int duration, boolean defaultBlackWhite) {
        if (context != null) {
            Toast toast = new Toast(context);
                toast.setDuration(duration);
                toast.show();
            }

    }

    private static Toast makeSimpleToast(Context context, String tips, int duration, boolean defaultBlackWhite) {
        Toast toast = Toast.makeText(context.getApplicationContext(), tips, duration);
        return toast;
    }
}
