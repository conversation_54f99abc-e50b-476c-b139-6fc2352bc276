package com.chervon.libBase.ui.widget;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Color;
import android.text.Editable;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.text.style.AbsoluteSizeSpan;
import android.text.style.ForegroundColorSpan;
import android.util.AttributeSet;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.chervon.libBase.R;

@SuppressLint("AppCompatCustomView")
public class PageNumView extends TextView {
    public PageNumView(Context context) {
        super(context);
    }

    public PageNumView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public PageNumView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public void setText(CharSequence text, BufferType type) {
        if(!TextUtils.isEmpty(text.toString())){
            Spannable span = new SpannableString(text);
            span.setSpan(new ForegroundColorSpan( getResources().getColor(R.color.colorButtonNormal)), 0,1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            span.setSpan(new ForegroundColorSpan(getResources().getColor(R.color.colorthird)), 1, text.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            //setText(span);
            super.setText(span, type);
        }else{
            super.setText(text, type);
        }


    }
}
