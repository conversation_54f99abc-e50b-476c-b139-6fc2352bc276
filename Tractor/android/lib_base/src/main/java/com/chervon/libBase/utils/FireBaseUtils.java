package com.chervon.libBase.utils;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.text.TextUtils;

import com.blankj.utilcode.util.LogUtils;
import com.chervon.libBase.BaseApplication;
import com.google.firebase.analytics.FirebaseAnalytics;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: 184862
 * @CreateDate: 2024/7/30
 * @UpdateDate: 2024/7/30
 */
public class FireBaseUtils {

    /**
     * 设备面板专用
     * @param bundle
     */
    public static void addPanelCrashLogEvent(Bundle bundle){
        final String PANEL_INFO = "panelInfo";
        if (null==bundle){
            return;
        }
        if (bundle.isEmpty()){
            return;
        }
        @SuppressLint("MissingPermission")
        FirebaseAnalytics firebaseAnalytics = FirebaseAnalytics.getInstance(BaseApplication.getInstance());
        firebaseAnalytics.logEvent(PANEL_INFO,bundle);
    }

    /**
     * 对于重要的异常信息进行收集
     * @param className 类名称
     * @param exceptionMaps
     */
    public static String methodKey = "method";
    public static String exceptionKey = "exception";

    public static void uploadException(String className,HashMap<String,String> exceptionMaps){
        final String exceptionCollection = "exceptionCollection";

        if (TextUtils.isEmpty(className)){
            return;
        }
        if (exceptionMaps.isEmpty()){
            return;
        }

        Bundle bundle = new Bundle();
        for (Map.Entry<String, String> exception : exceptionMaps.entrySet()) {
            bundle.putString(exception.getKey(),exception.getValue());
        }
        LogUtils.i(className,"-------"+exceptionCollection);
//        @SuppressLint("MissingPermission")
//        FirebaseAnalytics firebaseAnalytics = FirebaseAnalytics.getInstance(BaseApplication.getInstance());
//        firebaseAnalytics.logEvent(exceptionCollection,bundle);
    }

    /**
     * pre环境区分
     */
    public static void openFlavorsLogEvent(){
        final String flavors = "flavors";
        final String flavorsChervon = "chervon";
        final String flavorsThunderSoft = "thunderSoft";
        final String flavorsHadLinks = "hadLinks";
        Bundle bundle = new Bundle();
        bundle.putString(flavors,flavorsHadLinks);
        @SuppressLint("MissingPermission")
        FirebaseAnalytics firebaseAnalytics = FirebaseAnalytics.getInstance(BaseApplication.getInstance());
        firebaseAnalytics.logEvent(flavors,bundle);
    }
}
