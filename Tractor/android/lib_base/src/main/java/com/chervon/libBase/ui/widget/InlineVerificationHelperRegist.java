package com.chervon.libBase.ui.widget;

import static com.chervon.libBase.utils.CommonUtils.checkPassword;

import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.CheckBox;

import com.chervon.libBase.utils.CommonUtils;
import com.chervon.libBase.utils.DialogUtil;
import com.chervon.libBase.utils.Utils;
import com.chervon.libBase.utils.mlang.LanguageStrings;

import java.util.ArrayList;
import java.util.List;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleOobe.utils
 * @ClassName: InlineVerificationHelper
 * @Description: the Helper   of InlineVerification
 * @Author: wangheng
 * @CreateDate: 2022/6/18 下午7:02
 * @UpdateUser: wangheng
 * @UpdateDate: 2022/6/18 下午7:02
 * @UpdateRemark: new
 * @Version: 1.0
 */
public class InlineVerificationHelperRegist {
  private List<Checker> list = new ArrayList<>();
  private View btnNext;
  private String mail = null;
  private String editPassword = null;
  private static final String MAIL_TAG = "@";
  private boolean emailCheckResult = false;

  public InlineVerificationHelperRegist(View btnNext) {
    this.btnNext = btnNext;
  }

  public InlineVerificationHelperRegist buildEmailChecker(InlineTipEditTextRegist view) {
    return buildEmailChecker(view, null);
  }

  public boolean isEmailCheckResult() {
    return emailCheckResult;
  }

  public void setEmailCheckResult(boolean emailCheckResult) {
    this.emailCheckResult = emailCheckResult;
  }

  public String getEditPassword() {
    return editPassword;
  }

  public void setEditPassword(String editPassword) {
    this.editPassword = editPassword;
  }

  public InlineVerificationHelperRegist buildEmailChecker(InlineTipEditTextRegist view, EmailInputComplete listenner) {
    view.setEditTextOnFocusChangeListener(new View.OnFocusChangeListener() {
      @Override
      public void onFocusChange(View v, boolean hasFocus) {

        if (!hasFocus) {
          mail = view.getInlineTipEditTextString();
          if (TextUtils.isEmpty(mail)) {
            view.showInlineTip(InlineTipEditTextRegist.INPUT_TYPE_EMAIL,
              LanguageStrings.app_base_inputrequirederror_textview_text(),
              "", "");
            view.showErrorMargin();
          } else if (!checkEmail(view)) {
            view.showInlineTip(InlineTipEditTextRegist.INPUT_TYPE_EMAIL,
              LanguageStrings.getEnterCorrectMailbox(),
              "", "");
            view.showErrorMargin();
          } else {
            if (listenner != null) {
              listenner.complete(view.getInlineTipEditTextString());
            }
          }
        }else {
          emailCheckResult = false;
        }
      }
    });
    list.add(new Checker() {
      @Override
      public boolean check() {
        return checkEmail(view);
      }
    });
    return this;
  }


  public InlineVerificationHelperRegist buildFirstName(InlineTipEditTextRegist view) {
    return buildFirstNameChecker(view, null);
  }


  public InlineVerificationHelperRegist buildFirstNameChecker(InlineTipEditTextRegist view, EmailInputComplete listenner) {
    view.setEditTextOnFocusChangeListener(new View.OnFocusChangeListener() {
      @Override
      public void onFocusChange(View v, boolean hasFocus) {
        if (!hasFocus) {
          String firstName = view.getInlineTipEditTextString();
          if (TextUtils.isEmpty(firstName)) {
            view.showInlineTip(InlineTipEditTextRegist.INPUT_TYPE_FIRST_NAME, LanguageStrings.app_base_inputrequirederror_textview_text(), "", "");
            view.showErrorMargin();
          } else {
            if (!checkFirstName(view)) {

              view.showInlineTip(InlineTipEditTextRegist.INPUT_TYPE_FIRST_NAME, LanguageStrings.app_base_nameinputerror_textview_text(), "", "");
              view.showErrorMargin();
            } else {
              if (listenner != null) {
                listenner.complete(view.getInlineTipEditTextString());
              }

            }
          }
        }
      }
    });
    list.add(new Checker() {
      @Override
      public boolean check() {
        return checkFirstName(view);
      }
    });
    return this;
  }

  public InlineVerificationHelperRegist buildEmailExistChecker(InlineTipEditTextRegist view, boolean result, String worningText) {
    if (result) {
      view.showInlineTip(InlineTipEditTextRegist.INPUT_TYPE_FIRST_NAME, worningText, "", "");
    }

    list.add(new Checker() {
      @Override
      public boolean check() {
        return !result;
      }
    });
    return this;
  }

  public InlineVerificationHelperRegist buildEmailWornTip(InlineTipEditTextRegist view, boolean result, String worningText) {
    if (result) {
      view.showInlineTip(InlineTipEditTextRegist.INPUT_TYPE_EMAIL, worningText, "", "");
      view.showNormalMargin();
    } else {
      view.showInlineTip(InlineTipEditTextRegist.INPUT_TYPE_EMAIL, "", "", "");

    }

    return this;
  }

  public boolean checkFirstName(InlineTipEditTextRegist view) {
    String firstName = view.getInlineTipEditTextString();

    if (TextUtils.isEmpty(firstName)) {
      view.showNormalMargin();
      return false;
    }

    if (Utils.nameMatches(firstName)) {
      view.showInlineTip(InlineTipEditTextRegist.INPUT_TYPE_FIRST_NAME, null, null, null);
    }else {
      view.showNormalMargin();
    }

    return Utils.nameMatches(firstName);
  }


  public InlineVerificationHelperRegist buildLastName(InlineTipEditTextRegist view) {
    return buildLastNameChecker(view, null);
  }

  public InlineVerificationHelperRegist buildEmail(InlineTipEditTextRegist view) {
    return buildEmailChecker(view, null);
  }


  public InlineVerificationHelperRegist buildLastNameChecker(InlineTipEditTextRegist view, EmailInputComplete listener) {
    view.setEditTextOnFocusChangeListener(new View.OnFocusChangeListener() {
      @Override
      public void onFocusChange(View v, boolean hasFocus) {
        if (!hasFocus) {
          String lastName = view.getInlineTipEditTextString();
          if (TextUtils.isEmpty(lastName)) {
            view.showErrorMargin();
            view.showInlineTip(InlineTipEditTextRegist.INPUT_TYPE_LAST_NAME, LanguageStrings.app_base_inputrequirederror_textview_text(), "", "");
          } else {
            if (!checkLastName(view)) {
              view.showErrorMargin();
              view.showInlineTip(InlineTipEditTextRegist.INPUT_TYPE_LAST_NAME, LanguageStrings.app_base_nameinputerror_textview_text(), "", "");

            } else {
              if (listener != null) {
                listener.complete(view.getInlineTipEditTextString());
              }

            }
          }


        }
      }
    });
    list.add(new Checker() {
      @Override
      public boolean check() {
        return checkLastName(view);
      }
    });
    return this;
  }


  public boolean checkLastName(InlineTipEditTextRegist view) {
    String lastName = view.getInlineTipEditTextString();

    if (TextUtils.isEmpty(lastName)) {
      return false;
    }

    if (Utils.nameMatches(lastName)) {
      view.showInlineTip(InlineTipEditTextRegist.INPUT_TYPE_LAST_NAME, null, null, null);
    }

    return Utils.nameMatches(lastName);
  }


  private boolean checkEmail(InlineTipEditTextRegist view) {
    boolean isEmail = false;
    String email = view.getInlineTipEditTextString();
    if (!CommonUtils.checkEmail(email)) {
      disableBtnClick(false);
      return false;
    } else {
      isEmail = true;
//      view.showInlineTip(InlineTipEditTextRegist.INPUT_TYPE_EMAIL, null, null, null);
    }
    return isEmail;
  }

  public InlineVerificationHelperRegist buildPasswordChecker(InlineTipEditTextRegist view) {

    final boolean[] haFocusOn = {false};


    view.setEditTextOnFocusChangeListener(new View.OnFocusChangeListener() {
      @Override
      public void onFocusChange(View v, boolean hasFocus) {

        String password = view.getInlineTipEditTextString();
        editPassword = password;
        haFocusOn[0] = hasFocus;
        view.setMail(mail);
        if (hasFocus) {
          //在焦点内
          if (TextUtils.isEmpty(password)) {
            view.showInlineTip(InlineTipEditTextRegist.INPUT_TYPE_PWD_NORMAL,
              "· " + LanguageStrings.app_oobe_sign_up_password_char_and_spaces(),
              "· " + LanguageStrings.app_oobe_sign_up_password_confirm_numbers_and_letters(),
              "· " + LanguageStrings.app_signup_passwordcheckemail_textview_text());
          }
        } else {
          //在焦点外
          if (TextUtils.isEmpty(password)) {
            if (TextUtils.isEmpty(password)) {
              view.showInlineTip(InlineTipEditTextRegist.INPUT_TYPE_PWD_NORMAL,
                "· " + LanguageStrings.app_oobe_sign_up_password_char_and_spaces(),
                "· " + LanguageStrings.app_oobe_sign_up_password_confirm_numbers_and_letters(),
                "· " + LanguageStrings.app_signup_passwordcheckemail_textview_text());
            }
          } else {
            view.showInlineTip(InlineTipEditTextRegist.INPUT_TYPE_PWD,
              LanguageStrings.app_oobe_sign_up_password_char_and_spaces(),
              LanguageStrings.app_oobe_sign_up_password_confirm_numbers_and_letters(),
              LanguageStrings.app_signup_passwordcheckemail_textview_text());
          }
          view.showInTipsForMargin(InlineTipEditTextRegist.INPUT_TYPE_PWD,
                  LanguageStrings.app_oobe_sign_up_password_char_and_spaces(),
                  LanguageStrings.app_oobe_sign_up_password_confirm_numbers_and_letters(),
                  LanguageStrings.app_signup_passwordcheckemail_textview_text());
        }

      }
    });

    view.addTextChangedListener(new TextWatcher() {
      @Override
      public void beforeTextChanged(CharSequence s, int start, int count, int after) {

      }

      @Override
      public void onTextChanged(CharSequence s, int start, int before, int count) {

      }

      @Override
      public void afterTextChanged(Editable edit) {

        String password = view.getInlineTipEditTextString();
        //在焦点内
        if (TextUtils.isEmpty(password)) {
          if (haFocusOn[0]) {
            view.showInlineTip(InlineTipEditTextRegist.INPUT_TYPE_PWD_NORMAL,
              "· " + LanguageStrings.app_oobe_sign_up_password_char_and_spaces(),
              "· " + LanguageStrings.app_oobe_sign_up_password_confirm_numbers_and_letters(),
              "· " + LanguageStrings.app_signup_passwordcheckemail_textview_text());
          }
        } else {
          view.showInlineTip(InlineTipEditTextRegist.INPUT_TYPE_PWD,
            LanguageStrings.app_oobe_sign_up_password_char_and_spaces(),
            LanguageStrings.app_oobe_sign_up_password_confirm_numbers_and_letters(),
            LanguageStrings.app_signup_passwordcheckemail_textview_text());
        }
      }
    });


    list.add(new Checker() {
      @Override
      public boolean check() {
        return checkPasswordText(view);
      }
    });
    return this;
  }

  public InlineVerificationHelperRegist setEmail(String email) {
    this.mail = email;
    return this;
  }


  public InlineVerificationHelperRegist buildPasswordCheckerWithSameChecker2(InlineTipEditTextRegist view,
                                                                             InlineTipEditTextRegist confirmView,
                                                                             InlineTipEditTextRegist samechekerTipView) {

    view.setEditTextOnFocusChangeListener(new View.OnFocusChangeListener() {
      @Override
      public void onFocusChange(View v, boolean hasFocus) {


        if (!hasFocus) {
//          if (!checkPasswordText(view)) {
//
//            view.showInlineTip(InlineTipEditTextRegist.INPUT_TYPE_CONFIRM_PWD, LanguageStrings.app_forgetpasswordreset_passwordnotsame_textview_text(), null, null);
//          } else {
            String password;
            String confirmPassword;
            password = confirmView.getInlineTipEditTextString();
            confirmPassword = view.getInlineTipEditTextString();
            if (TextUtils.isEmpty(password)){
              samechekerTipView.showInlineTip(InlineTipEditTextRegist.INPUT_TYPE_CONFIRM_PWD,
                      LanguageStrings.app_forgetpasswordreset_passwordnotsame_textview_text(),
                      null,
                      null);
            }


            if (TextUtils.isEmpty(password) || TextUtils.isEmpty(confirmPassword)) {
              samechekerTipView.showInlineTip(InlineTipEditTextRegist.INPUT_TYPE_CONFIRM_PWD,
                      null,
                      null,
                      null);
            } else {
              if (confirmPassword.equals(password)) {

                samechekerTipView.showInlineTip(InlineTipEditTextRegist.INPUT_TYPE_CONFIRM_PWD,
                  null,
                  null,
                  null);

              } else {
                samechekerTipView.showInlineTip(InlineTipEditTextRegist.INPUT_TYPE_CONFIRM_PWD,
                  LanguageStrings.app_forgetpasswordreset_passwordnotsame_textview_text(),
                  null,
                  null);
              }
//            }

          }
        }
      }
    });


    list.add(new Checker() {
      @Override
      public boolean check() {
        return checkPasswordText(view);
      }
    });
    return this;
  }

  private void checkWhite(String password) {
    String error_header = LanguageStrings.app_regist_password_error_worning_header();
    boolean isMatch = Utils.passwordMatches(password);

    if (!isMatch) {
      String unMatchesString = Utils.passwordRegiesterMatchesString(password);
      error_header = error_header + unMatchesString;
      DialogUtil.showRegisterErrorDialog(btnNext.getContext(), error_header);

    }

  }


  public InlineVerificationHelperRegist buildPasswordCheckerWithSameChecker(InlineTipEditTextRegist view, InlineTipEditTextRegist confirmView, InlineTipEditTextRegist samechekerTipView) {

    view.setEditTextOnFocusChangeListener(new View.OnFocusChangeListener() {
      @Override
      public void onFocusChange(View v, boolean hasFocus) {
        if (!hasFocus) {
          if (!checkPasswordText(view)) {
            view.showInlineTip(InlineTipEditTextRegist.INPUT_TYPE_PWD,
              LanguageStrings.app_oobe_sign_up_password_char_and_spaces(),
              LanguageStrings.app_oobe_sign_up_password_confirm_numbers_and_letters(),
              LanguageStrings.app_signup_passwordcheckemail_textview_text());
          } else {
            String password;
            String confirmPassword;
            password = confirmView.getInlineTipEditTextString();
            confirmPassword = view.getInlineTipEditTextString();
            if (TextUtils.isEmpty(password) || TextUtils.isEmpty(confirmPassword)) {
              return;
            } else {
              if (confirmPassword.equals(password)) {
                samechekerTipView.showInlineTip(
                  InlineTipEditTextRegist.INPUT_TYPE_PWD,
                  null,
                  null, null);
              } else {
                samechekerTipView.showInlineTip(InlineTipEditTextRegist.INPUT_TYPE_PWD,
                  LanguageStrings.app_oobe_sign_up_password_char_and_spaces(),
                  LanguageStrings.app_oobe_sign_up_password_confirm_numbers_and_letters(),
                  LanguageStrings.app_signup_passwordcheckemail_textview_text());
              }
            }

          }
        }
      }
    });


    list.add(new Checker() {
      @Override
      public boolean check() {
        return checkPasswordText(view);
      }
    });
    return this;
  }


  public InlineVerificationHelperRegist buildAgreementChecker(CheckBox view) {
    list.add(new Checker() {
      @Override
      public boolean check() {
        return view.isChecked();
      }
    });
    return this;
  }


  private boolean checkPasswordText(InlineTipEditTextRegist view) {
    boolean isPassword = false;
    String password = view.getInlineTipEditTextString();
    if (checkPassword(password)) {
      view.showInlineTip(InlineTipEditTextRegist.INPUT_TYPE_CONFIRM_PWD, null, null, null);
      isPassword = true;
    } else {
      disableBtnClick(false);
      isPassword = false;
    }
    return isPassword;
  }


  public InlineVerificationHelperRegist buildPasswordSameChecker(InlineTipEditTextRegist passwordView, InlineTipEditTextRegist confirmView) {
    list.add(new Checker() {
      @Override
      public boolean check() {
        return checkPasswordSame(passwordView, confirmView);
      }
    });
    return this;
  }

  private boolean checkPasswordSame(InlineTipEditTextRegist passwordView, InlineTipEditTextRegist confirmView) {
    boolean isPasswordSame = false;
    String password;
    String confirmPassword;
    password = passwordView.getInlineTipEditTextString();
    confirmPassword = confirmView.getInlineTipEditTextString();
    if (TextUtils.isEmpty(password) || TextUtils.isEmpty(confirmPassword)) {
      return isPasswordSame;
    }
    if (confirmPassword.equals(password)) {
      isPasswordSame = true;
//      if (isPasswordSame) {
//        checkWhite(password);
//      }
      confirmView.showInlineTip(InlineTipEditTextRegist.INPUT_TYPE_CONFIRM_PWD, null, null, null);
    } else {
      confirmView.showInlineTip(InlineTipEditTextRegist.INPUT_TYPE_CONFIRM_PWD, LanguageStrings.app_forgetpasswordreset_passwordnotsame_textview_text(), null, null);
    }
    return isPasswordSame;
  }


  private void disableBtnClick(boolean b) {

    btnNext.setEnabled(b);
    btnNext.setClickable(b);
  }

  public void checkInline() {

    if (!isEmailCheckResult()){

      disableBtnClick(false);
      return;
    }

    for (int i = 0; i < list.size(); i++) {
      boolean checkRes = list.get(i).check();
      if (checkRes && (i == list.size() - 1)) {

        if (!TextUtils.isEmpty(editPassword)) {
          if (mailFormat(editPassword)) {
            disableBtnClick(true);
          }
        }


      } else {
        if (!checkRes) {
          disableBtnClick(false);
          break;
        }
      }
    }
  }



  interface Checker {
    /**
     * check
     *
     * @return return is true or false
     */
    public boolean check();
  }

  public interface EmailInputComplete {
    /**
     * check
     *
     * @return return is true or false
     */
    public boolean complete(String str);
  }

  public void clear() {
    btnNext = null;
  }


  /**
   * 根据邮箱校验是否包含@前部的字符
   *
   * @return
   */
  private boolean mailFormat(String password) {
    boolean result = true;

    if (TextUtils.isEmpty(password)) {
      return false;
    }

    //如果未输入邮箱则不校验---不校验
    if (TextUtils.isEmpty(mail)) {
      return false;
    }

    //如果输入邮箱未非法邮箱---不校验
    if (!CommonUtils.checkEmail(mail)) {
      return false;
    }
    String[] split = mail.split(MAIL_TAG);
    //取邮箱前面部分校验
    String mailHeader = split[0];
    //健壮性校验，截取字符不能为空
    if (TextUtils.isEmpty(mailHeader)) {
      return false;
    }

    if (password.contains(mailHeader)) {
      result = false;
    }

    return result;
  }
}
