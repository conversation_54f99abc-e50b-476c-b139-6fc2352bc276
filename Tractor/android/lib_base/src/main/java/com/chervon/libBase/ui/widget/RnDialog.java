package com.chervon.libBase.ui.widget;

import android.view.Display;
import android.view.Gravity;
import android.view.WindowManager;

import com.afollestad.materialdialogs.MaterialDialog;

/**
 * @ProjectName: app
 * @Package: com.chervon.libBase.ui.widget
 * @ClassName: RnDialog
 * @Description: 类描述
 * @Author: name
 * @CreateDate: 2022/8/4 下午2:07
 * @UpdateUser: 更新者
 * @UpdateDate: 2022/8/4 下午2:07
 * @UpdateRemark: 更新说明
 * @Version: 1.0
 */
public class RnDialog extends MaterialDialog {
    protected RnDialog(Builder builder) {
        super(builder);
    }

    @Override
    public void onContentChanged() {
        super.onContentChanged();
        // 解决横竖屏切换的适配问题
        Display display =  this.getOwnerActivity().getWindowManager().getDefaultDisplay();
        WindowManager.LayoutParams params = this.getWindow().getAttributes();
        params.width = display.getWidth();
        this.getWindow().setAttributes(params);
        this.getWindow().setGravity(Gravity.BOTTOM);
    }

}
