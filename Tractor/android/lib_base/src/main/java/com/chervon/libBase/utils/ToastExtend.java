package com.chervon.libBase.utils;

import android.content.Context;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.LayoutRes;

import com.blankj.utilcode.util.ToastUtils;
import com.chervon.libBase.R;
import com.chervon.libBase.utils.mlang.LanguageStrings;

/**
 * @ProjectName: app
 * @Package: com.chervon.libBase.utils
 * @ClassName: ToastExtend
 * @Description: Toast扩展
 * @Author: LangMeng
 * @CreateDate: 2022/7/4 11:45
 * @UpdateUser: LangMeng
 * @UpdateDate: 2022/7/4 11:45
 * @UpdateRemark: new class
 * @Version: 1.0
 */
public class ToastExtend {

    /**
     * @method showSuccess
     * @description 成功弹窗
     * @date: 2022/6/23 14:07
     * @author: <PERSON><PERSON>eng
     * @return void
     */
    public static void showSuccess() {
        com.blankj.utilcode.util.ToastUtils make = ToastUtils.make();
        make.setGravity(Gravity.CENTER, 0, 0);
        make.setDurationIsLong(false);
        View view = getView(R.layout.toast_success_layout);
        TextView content = view.findViewById(R.id.toastContent);
        content.setText(LanguageStrings.app_setting_success_textview_text());
        make.show(view);
    }

    /**
     * @method getView
     * @description get view
     * @date: 2022/7/4 11:48
     * @author: LangMeng
     * @param layoutId
     * @return android.view.View
     */
    private static View getView(@LayoutRes final int layoutId) {
        LayoutInflater inflate =
                (LayoutInflater) Utils.getApp().getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        //noinspection ConstantConditions
        return inflate.inflate(layoutId, null);
    }
}
