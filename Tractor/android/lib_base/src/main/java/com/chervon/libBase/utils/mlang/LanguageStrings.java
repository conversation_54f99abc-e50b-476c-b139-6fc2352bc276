package com.chervon.libBase.utils.mlang;

import com.chervon.libBase.R;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleUserCenter.data
 * @ClassName: LanguageStrings
 * @Description: 多语言字符类，维护最新的语言字符
 * @Author: langmeng
 * @CreateDate: 2022/8/24 15:17
 * @UpdateUser: wuxd
 * @UpdateDate: 2024/6/18
 * @UpdateRemark: 添加欧洲crm需求
 * @Version: 1.1
 */
public class LanguageStrings {


  public static String appSystemauthorizationReceive() {
    return MyLang.getString("app_systemauthorization_receive_textview_text", R.string.app_systemauthorization_receive_textview_text);
  }

  public static String app_OTAguide_cannotupragecode4_textview_text() {
    return MyLang.getString("app_OTAguide_cannotupragecode4_textview_text", R.string.app_OTAguide_cannotupragecode4_textview_text);
  }

  public static String app_OTAguide_cannotupragecode3_textview_text() {
    return MyLang.getString("app_OTAguide_cannotupragecode3_textview_text", R.string.app_OTAguide_cannotupragecode3_textview_text);
  }

  public static String app_OTAguide_cannotupragecode2_textview_text() {
    return MyLang.getString("app_OTAguide_cannotupragecode2_textview_text", R.string.app_OTAguide_cannotupragecode2_textview_text);
  }

  public static String appUserDetailAlbumTextviewText() {
    return MyLang.getString("app_userDetail_album_textview_text", R.string.app_userDetail_album_textview_text);
  }

  public static String appUserDetailTakephotoTextviewText() {
    return MyLang.getString("app_userDetail_takephoto_textview_text", R.string.app_userDetail_takephoto_textview_text);
  }

  public static String appDevicelistDeviceofflineTextviewText() {
    return MyLang.getString("app_devicelist_deviceoffline_textview_text", R.string.app_devicelist_deviceoffline_textview_text);
  }


  public static String appDeviceqrcodescanTitleTextviewText() {
    return MyLang.getString("app_deviceqrcodescan_title_textview_text", R.string.app_deviceqrcodescan_title_textview_text);
  }

  public static String appNetworkconfigwificonnectFaileddescriptionTextviewText() {
    return MyLang.getString("app_networkconfigwificonnect_faileddescription_textview_text", R.string.app_networkconfigwificonnect_faileddescription_textview_text);
  }

  public static String appRegistTitleTextviewText() {
    return MyLang.getString("app_regist_title_textview_text", R.string.app_regist_title_textview_text);
  }

  public static String appNetworkconfigwifiinputAccountInputviewPlaceholder() {
    return MyLang.getString("app_networkconfigwifiinput_account_inputview_placeholder", R.string.app_networkconfigwifiinput_account_inputview_placeholder);
  }

  public static String appNetworkconfigwifiinputPasswordInputviewPlaceholder() {
    return MyLang.getString("app_networkconfigwifiinput_password_inputview_placeholder", R.string.app_networkconfigwifiinput_password_inputview_placeholder);
  }


  public static String appDeviceqrcodescanProductnotsametoast() {
    return MyLang.getString("app_deviceqrcodescan_productnotsametoast_textview_text", R.string.app_deviceqrcodescan_productnotsametoast_textview_text);
  }

  public static String app_deviceregist_use_inputview_placeholder() {
    return MyLang.getString("app_deviceregist_use_inputview_placeholder", R.string.app_deviceregist_use_inputview_placeholder);
  }

  public static String appDeviceregistactionsheetUseTextview() {
    return MyLang.getString("app_deviceregistactionsheet_use_textview_text", R.string.app_deviceregistactionsheet_use_textview_text);
  }

  public static String appPrivacyagreementrevokeCancel() {
    return MyLang.getString("app_privacyagreementrevoke_cancel_button_text", R.string.app_privacyagreementrevoke_cancel_button_text);
  }

  public static String appPrivacyagreementrevokeRevoke() {
    return MyLang.getString("app_privacyagreementrevoke_revoke_button_text", R.string.app_privacyagreementrevoke_revoke_button_text);
  }

  public static String appBaseHaveNotNetWork() {
    return MyLang.getString("app_base_havenotnetwork_textview_text", R.string.app_base_havenotnetwork_textview_text);
  }

  public static String appBaseLogininvalidText() {
    return MyLang.getString("app_base_logininvalid_textview_text", R.string.app_base_logininvalid_textview_text);
  }


  public static String app_OTAguide_cannotupragecode1_textview_text() {
    return MyLang.getString("app_OTAguide_cannotupragecode1_textview_text", R.string.app_OTAguide_cannotupragecode1_textview_text);
  }
  public static String app_OTAguide_cannotupragecode6_textview_text() {
    return MyLang.getString("app_OTAguide_cannotupragecode6_textview_text", R.string.app_OTAguide_cannotupragecode6_textview_text);
  }

  public static String app_OTAguide_cannotupragecode5_textview_text() {
    return MyLang.getString("app_OTAguide_cannotupragecode5_textview_text", R.string.app_OTAguide_cannotupragecode5_textview_text);
  }

  public static String app_OTAguide_cannotupragecode8_textview_text() {
    return MyLang.getString("app_OTAguide_cannotupragecode8_textview_text", R.string.app_OTAguide_cannotupragecode8_textview_text);
  }


  public static String appDevicecodeAssemblyserialnumber() {
    return MyLang.getString("app_devicecode_assemblyserialnumber_textview_text", R.string.app_devicecode_assemblyserialnumber_textview_text);
  }

  public static String app_devicemoreinfo_deletedevicemessage_textview_text() {
    return MyLang.getString("app_devicemoreinfo_deletedevicemessage_textview_text", R.string.app_devicemoreinfo_deletedevicemessage_textview_text);
  }

  public static String app_devicelist_deletedevicealerttitle_textview_text() {
    return MyLang.getString("app_devicelist_deletedevicealerttitle_textview_text", R.string.app_devicelist_deletedevicealerttitle_textview_text);
  }

  public static String getdealersLocationtips() {
    return MyLang.getString("app_productdealers_locationtips_textview_text", R.string.app_productdealers_locationtips_textview_text);
  }


  public static String getPassword() {
    return MyLang.getString("app_networkconfigwifiinput_password_inputview_placeholder", R.string.app_networkconfigwifiinput_password_inputview_placeholder);
  }

  public static String getConfirmPassword() {
    return MyLang.getString("app_regist_passwordconfirm_inputview_placeholder", R.string.app_regist_passwordconfirm_inputview_placeholder);
  }

  public static String getUpperLogin() {
    return MyLang.getString("app_loginguide_login_button_text", R.string.app_loginguide_login_button_text);
  }

  public static String app_login_login_button_text() {
    return MyLang.getString("app_login_login_button_text", R.string.app_login_login_button_text);
  }

  public static String getForgetpassword() {
    return MyLang.getString("app_login_forget_button_text", R.string.app_login_forget_button_text);
  }

  public static String getSignup() {
    return MyLang.getString("app_login_regist_button_text", R.string.app_login_regist_button_text);
  }

  public static String getConfirm() {
    return MyLang.getString("app_base_confirm_button_text", R.string.app_base_confirm_button_text);
  }

  public static String getEmailaddresslogin() {
    return MyLang.getString("app_login_account_inputview_placeholder", R.string.app_login_account_inputview_placeholder);
  }

  public static String getEmailaddressRegist() {
    return MyLang.getString("app_regist_email_inputview_placeholder", R.string.app_regist_email_inputview_placeholder);
  }

  public static String getEmailaddressForgetpassword() {
    return MyLang.getString("app_forgetpassword_email_inputview_placeholder", R.string.app_forgetpassword_email_inputview_placeholder);
  }

  public static String getRegistNext() {
    return MyLang.getString("app_regist_next_button_text", R.string.app_regist_next_button_text);
  }

  public static String getForgetpasswordNext() {
    return MyLang.getString("app_forgetpassword_next_button_text", R.string.app_forgetpassword_next_button_text);
  }

  public static String getDevicesNext() {
    return MyLang.getString("app_devicesninput_helpnext_button_text", R.string.app_devicesninput_helpnext_button_text);
  }

  public static String getUpperSignUp() {
    return MyLang.getString("app_loginguide_regist_button_text", R.string.app_loginguide_regist_button_text);
  }

  public static String app_regist_firstname_inputview_placeholder() {
    return MyLang.getString("app_regist_firstname_inputview_placeholder", R.string.app_regist_firstname_inputview_placeholder);
  }

  public static String app_regist_lastname_inputview_placeholder() {
    return MyLang.getString("app_regist_lastname_inputview_placeholder", R.string.app_regist_lastname_inputview_placeholder);
  }

  public static String getRegistverifycodedone() {
    return MyLang.getString("app_registverifycode_done_button_text", R.string.app_registverifycode_done_button_text);
  }

  public static String getForgetpasswordreset_done() {
    return MyLang.getString("app_forgetpasswordreset_done_button_text", R.string.app_forgetpasswordreset_done_button_text);
  }

  public static String getSearchdone() {
    return MyLang.getString("app_deviceproductsearch_done_button_text", R.string.app_deviceproductsearch_done_button_text);
  }


  public static String getConnectdone() {
    return MyLang.getString("app_networkconfigblutoothconnect_done_button_text", R.string.app_networkconfigblutoothconnect_done_button_text);
  }

  public static String app_networkconfigwificonnect_notregistdevice_button_text() {
    return MyLang.getString("app_networkconfigwificonnect_notregistdevice_button_text", R.string.app_networkconfigwificonnect_notregistdevice_button_text);
  }

  public static String getModifypassworddone() {
    return MyLang.getString("app_modifypassword_done_button_text", R.string.app_modifypassword_done_button_text);
  }

  public static String getOtadone() {
    return MyLang.getString("app_OTA_done_button_text", R.string.app_OTA_done_button_text);
  }

  public static String app_tabbar_user_textview_text() {
    return MyLang.getString("app_tabbar_user_textview_text", R.string.app_tabbar_user_textview_text);
  }

  public static String app_tabbar_home_textview_text() {
    return MyLang.getString("app_tabbar_home_textview_text", R.string.app_tabbar_home_textview_text);
  }


  public static String app_devicesninput_sn_inputview_placeholder() {
    return MyLang.getString("app_devicesninput_sn_inputview_placeholder", R.string.app_devicesninput_sn_inputview_placeholder);
  }

  public static String getCodescanSncode() {
    return MyLang.getString("app_deviceqrcodescan_inputsn_button_text", R.string.app_deviceqrcodescan_inputsn_button_text);
  }

  public static String getCodescanSncodeNotice() {
    return MyLang.getString("app_deviceqrcodescan_notice_textview_text", R.string.app_deviceqrcodescan_notice_textview_text);
  }

  public static String app_devicelist_emptyadd_button_text() {
    return MyLang.getString("app_devicelist_emptyadd_button_text", R.string.app_devicelist_emptyadd_button_text);
  }

  public static String getDevicesearchNotice() {
    return MyLang.getString("app_devicesearch_notice_textview_text", R.string.app_devicesearch_notice_textview_text);
  }

  public static String getUseanothermethod() {
    return MyLang.getString("app_deviceadd_useranothermethod_textview_text", R.string.app_deviceadd_useranothermethod_textview_text);
  }

  public static String getScanqrcode() {
    return MyLang.getString("app_deviceadd_scanqrcode_button_text", R.string.app_deviceadd_scanqrcode_button_text);
  }

  public static String getPrivacyPolicy() {
    return MyLang.getString("app_login_privacy_textview_text", R.string.app_login_privacy_textview_text);
  }

  public static String getUseragreement() {
    return MyLang.getString("app_login_agreement_textview_text", R.string.app_login_agreement_textview_text);
  }

  public static String getUseragreementPart() {
    return MyLang.getString("app_login_agreementprivacypart_textview_text", R.string.app_login_agreementprivacypart_textview_text) + " ";
  }

  public static String getUseragreementAnd() {
    return MyLang.getString("app_login_agreementprivacyand_textview_text", R.string.app_login_agreementprivacyand_textview_text);
  }

  public static String getRegistPasswordInput() {
    return MyLang.getString("app_regist_password_inputview_placeholder", R.string.app_regist_password_inputview_placeholder);
  }

  public static String getChangePassword() {
    return MyLang.getString("app_userDetail_changePassword_textview_text", R.string.app_userDetail_changePassword_textview_text);
  }

  public static String getOriginalPassword() {
    return MyLang.getString("app_checkpassword_inputpassword_inputview_placeholder", R.string.app_checkpassword_inputpassword_inputview_placeholder);
  }

  public static String getRegistLoginButton() {
    return MyLang.getString("app_regist_login_button_text", R.string.app_regist_login_button_text);
  }

  public static String getEnterCorrectMailbox() {
    return MyLang.getString("app_login_account_textview_text", R.string.app_login_account_textview_text);
  }

  public static String app_forgetpasswordverifycode_sendagain_button_text() {
    return MyLang.getString("app_forgetpasswordverifycode_sendagain_button_text", R.string.app_forgetpasswordverifycode_sendagain_button_text);
  }

  public static String getDevicelistwelcome() {
    return MyLang.getString("app_devicelist_emptywelcome_textview_text", R.string.app_devicelist_emptywelcome_textview_text);
  }

  public static String getLoginwelcome() {
    return MyLang.getString("app_loginguide_wellcom_textview_text", R.string.app_loginguide_wellcom_textview_text);
  }


  public static String getLogincopyright() {
    return MyLang.getString("app_loginguide_copyright_textview_text", R.string.app_loginguide_copyright_textview_text);
  }

  public static String getLoginguide() {
    return MyLang.getString("app_loginguide_guide_textview_text", R.string.app_loginguide_guide_textview_text);
  }

  public static String getLoginCheckagreement() {
    return MyLang.getString("app_login_checkagreement_textview_text", R.string.app_login_checkagreement_textview_text);
  }

  public static String getLoginAgreementprivacy() {
    return MyLang.getString("app_regist_agreementprivacy_textview_text", R.string.app_regist_agreementprivacy_textview_text);
  }

  public static String getDevicesninput() {
    return MyLang.getString("app_devicesninput_title_textview_text", R.string.app_devicesninput_title_textview_text);
  }

  public static String getDeviceQrcodescanHelp() {
    return MyLang.getString("app_deviceqrcodescan_help_button_text", R.string.app_deviceqrcodescan_help_button_text);
  }

  public static String getDeviceinputHelp() {
    return MyLang.getString("app_devicesninput_help_button_text", R.string.app_devicesninput_help_button_text);
  }

  public static String getDeviceSearchHistory() {
    return MyLang.getString("app_deviceproductsearch_history_textview_text", R.string.app_deviceproductsearch_history_textview_text);
  }

  public static String getClearsearchhistory() {
    return MyLang.getString("app_deviceproductsearch_deletealert_textview_text", R.string.app_deviceproductsearch_deletealert_textview_text);
  }

  public static String getDeviceConnection() {
    return MyLang.getString("app_networkconfigblutoothconnect_stepconnecting_textview_text", R.string.app_networkconfigblutoothconnect_stepconnecting_textview_text);
  }

  public static String getDeviceConnectionSuccess() {
    return MyLang.getString("app_networkconfigblutoothconnect_stepconnectsuccess_textview_text", R.string.app_networkconfigblutoothconnect_stepconnectsuccess_textview_text);
  }

  public static String getStepbinding() {
    return MyLang.getString("app_networkconfigblutoothconnect_stepbinding_textview_text", R.string.app_networkconfigblutoothconnect_stepbinding_textview_text);
  }

  public static String getAppNetworkconfigblutoothconnectStepbindsuccess() {
    return MyLang.getString("app_networkconfigblutoothconnect_stepbindsuccess_textview_text", R.string.app_networkconfigblutoothconnect_stepbindsuccess_textview_text);
  }

  public static String getWificonnectFailed() {
    return MyLang.getString("app_networkconfigwificonnect_failed_textview_text", R.string.app_networkconfigwificonnect_failed_textview_text);
  }

  public static String getNoticeconnectfaied() {
    return MyLang.getString("app_networkconfigwificonnect_noticeconnectfaied_textview_text", R.string.app_networkconfigwificonnect_noticeconnectfaied_textview_text);
  }

  public static String getOtaretry() {
    return MyLang.getString("app_OTA_retry_button_text", R.string.app_OTA_retry_button_text);
  }

  public static String getBlutoothconnectRetry() {
    return MyLang.getString("app_networkconfigblutoothconnect_retry_button_text", R.string.app_networkconfigblutoothconnect_retry_button_text);
  }

  public static String getWificonnectRetry() {
    return MyLang.getString("app_networkconfigwificonnect_retry_button_text", R.string.app_networkconfigwificonnect_retry_button_text);
  }

  public static String getBlutoothconnectTitle() {
    return MyLang.getString("app_networkconfigblutoothconnect_title_textview_text", R.string.app_networkconfigblutoothconnect_title_textview_text);
  }

  public static String getBlutoothconnectNotice() {
    return MyLang.getString("app_networkconfigblutoothconnect_notice_textview_text", R.string.app_networkconfigblutoothconnect_notice_textview_text);
  }

  public static String getBlutoothconnectDescription() {
    return MyLang.getString("app_networkconfigblutoothconnect_notice_textview_text", R.string.app_networkconfigblutoothconnect_notice_textview_text);
  }

  public static String getStepsynchronizing() {
    return MyLang.getString("app_networkconfigwificonnect_stepsynchronizing_textview_text", R.string.app_networkconfigwificonnect_stepsynchronizing_textview_text);
  }

  public static String getStepconnecting() {
    return MyLang.getString("app_networkconfigwificonnect_stepconnecting_textview_text", R.string.app_networkconfigwificonnect_stepconnecting_textview_text);
  }

  public static String getWificonnectStepbinding() {
    return MyLang.getString("app_networkconfigwificonnect_stepbinding_textview_text", R.string.app_networkconfigwificonnect_stepbinding_textview_text);
  }


  public static String getWifiinputjoin() {
    return MyLang.getString("app_networkconfigwifiinput_join_button_text", R.string.app_networkconfigwifiinput_join_button_text);
  }

  public static String app_networkconfigwificonnect_noticeconnecting_textview_text() {
    return MyLang.getString("app_networkconfigwificonnect_noticeconnecting_textview_text", R.string.app_networkconfigwificonnect_noticeconnecting_textview_text);
  }

  public static String getWifinoticeconnectsuccess() {
    return MyLang.getString("app_networkconfigwificonnect_noticeconnectsuccess_textview_text", R.string.app_networkconfigwificonnect_noticeconnectsuccess_textview_text);
  }

  public static String getNetworkconfigconfirmed() {
    return MyLang.getString("app_networkconfigguide_confirmed_button_text", R.string.app_networkconfigguide_confirmed_button_text);
  }

  public static String app_base_selectwifi_button_text() {
    return MyLang.getString("app_base_selectwifi_button_text", R.string.app_base_selectwifi_button_text);
  }

  public static String getConfiginitialize() {
    return MyLang.getString("app_networkconfigguide_initialize_textview_text", R.string.app_networkconfigguide_initialize_textview_text);
  }

  public static String getFoundedtextview() {
    return MyLang.getString("app_devicesearchresult_founded_textview_text", R.string.app_devicesearchresult_founded_textview_text);
  }

  public static String getSearchresultDescription() {
    return MyLang.getString("app_devicesearchresult_description_textview_text", R.string.app_devicesearchresult_description_textview_text);
  }

  public static String getAdddeviceCancel() {
    return MyLang.getString("app_smartadddevice_cancel_button_text", R.string.app_smartadddevice_cancel_button_text);
  }

  public static String getProductsearchCancel() {
    return MyLang.getString("app_deviceproductsearch_cancel_button_text", R.string.app_deviceproductsearch_cancel_button_text);
  }

  public static String getBaseCancel() {
    return MyLang.getString("app_base_cancel_button_text", R.string.app_base_cancel_button_text);
  }

  public static String getSettingCancel() {
    return MyLang.getString("app_setting_cancel_button_text", R.string.app_setting_cancel_button_text);
  }

  public static String getUserDetailCancel() {
    return MyLang.getString("app_userDetail_cancel_textview_text", R.string.app_userDetail_cancel_textview_text);
  }

  public static String getLogoffCancel() {
    return MyLang.getString("app_logoff_cancel_button_text", R.string.app_logoff_cancel_button_text);
  }

  public static String getDeviceRegistDone() {
    return MyLang.getString("app_deviceregistsuccess_done_button_text", R.string.app_deviceregistsuccess_done_button_text);
  }

  public static String app_deviceregistsuccess_more_button_text() {
    return MyLang.getString("app_deviceregistsuccess_more_button_text", R.string.app_deviceregistsuccess_more_button_text);
  }

  public static String app_deviceregist_location_inputview_placeholder() {
    return MyLang.getString("app_deviceregist_location_inputview_placeholder", R.string.app_deviceregist_location_inputview_placeholder);
  }

  public static String app_deviceregist_date_inputview_placeholder() {
    return MyLang.getString("app_deviceregist_date_inputview_placeholder", R.string.app_deviceregist_date_inputview_placeholder);
  }

  public static String app_deviceregist_surveypurchaseroption_firstname_textview_text() {
    return MyLang.getString("app_deviceregist_surveypurchaseroption_firstname_textview_text", R.string.app_deviceregist_surveypurchaseroption_firstname_textview_text);
  }

  public static String app_deviceregist_surveypurchaseroption_lastname_textview_text() {
    return MyLang.getString("app_deviceregist_surveypurchaseroption_lastname_textview_text", R.string.app_deviceregist_surveypurchaseroption_lastname_textview_text);
  }

  public static String app_deviceregist_surveypurchaseroption_address_textview_text() {
    return MyLang.getString("app_deviceregist_surveypurchaseroption_address_textview_text", R.string.app_deviceregist_surveypurchaseroption_address_textview_text);
  }

  public static String app_deviceregist_surveypurchaseroption_postcode_textview_text() {
    return MyLang.getString("app_deviceregist_surveypurchaseroption_postcode_textview_text", R.string.app_deviceregist_surveypurchaseroption_postcode_textview_text);
  }

  public static String app_deviceregist_surveypurchaseroption_country_textview_text() {
    return MyLang.getString("app_deviceregist_surveypurchaseroption_country_textview_text", R.string.app_deviceregist_surveypurchaseroption_country_textview_text);
  }

  public static String getDeviceRegistCountryTitle() {
    return MyLang.getString("app_deviceregist_surveypurchaseroption_countrytitle_textview_text", R.string.app_deviceregist_surveypurchaseroption_countrytitle_textview_text);
  }

  public static String app_deviceregist_surveypurchaseroption_province_textview_text() {
    return MyLang.getString("app_deviceregist_surveypurchaseroption_province_textview_text", R.string.app_deviceregist_surveypurchaseroption_province_textview_text);
  }

  public static String getDeviceRegistProvinceTitle() {
    return MyLang.getString("app_deviceregist_surveypurchaseroption_provincetitle_textview_text", R.string.app_deviceregist_surveypurchaseroption_provincetitle_textview_text);
  }

  public static String app_deviceregist_surveycontactoption_phone_textview_text() {
    return MyLang.getString("app_deviceregist_surveycontactoption_phone_textview_text",
      R.string.app_deviceregist_surveycontactoption_phone_textview_text);
  }


  public static String app_deviceregist_surveycontactoption_email_textview_text() {
    return MyLang.getString("app_deviceregist_surveycontactoption_email_textview_text", R.string.app_deviceregist_surveycontactoption_email_textview_text);
  }

  public static String getDeviceRegistpurchase() {
    return MyLang.getString("app_deviceregist_surveypurchaser_textview_text", R.string.app_deviceregist_surveypurchaser_textview_text);
  }

  public static String getContactInfo() {
    return MyLang.getString("app_deviceregist_surveycontact_textview_text", R.string.app_deviceregist_surveycontact_textview_text);
  }

  public static String getParticipate() {
    return MyLang.getString("app_deviceregist_surveyagree_textview_text", R.string.app_deviceregist_surveyagree_textview_text);
  }

  public static String getReasonPurchasedProduct() {
    return MyLang.getString("app_deviceregist_surveybuyreason_textview_text", R.string.app_deviceregist_surveybuyreason_textview_text);
  }

  public static String getReasonOtherproduct() {
    return MyLang.getString("app_deviceregist_surveyotherproduct_textview_text", R.string.app_deviceregist_surveyotherproduct_textview_text);
  }

  public static String getOtherproductWantBuy() {
    return MyLang.getString("app_deviceregist_surveywantbuy_textview_text", R.string.app_deviceregist_surveywantbuy_textview_text);
  }

  public static String getOtheBrandUse() {
    return MyLang.getString("app_deviceregist_surveyotherband_textview_text", R.string.app_deviceregist_surveyotherband_textview_text);
  }

  public static String getDeviceRegistUse() {
    return MyLang.getString("app_deviceregistsuccess_use_textview_text", R.string.app_deviceregistsuccess_use_textview_text);
  }

  public static String getDeviceRegistSn() {
    return MyLang.getString("app_deviceregistsuccess_sn_textview_text", R.string.app_deviceregistsuccess_sn_textview_text);
  }

  public static String getDeviceRegistSuccessDate() {
    return MyLang.getString("app_deviceregistactionsheet_date_textview_text", R.string.app_deviceregistactionsheet_date_textview_text);
  }

  public static String getDeviceRegistSuccessPlace() {
    return MyLang.getString("app_deviceregistsuccess_location_textview_text", R.string.app_deviceregistsuccess_location_textview_text);
  }

  public static String getDeviceRegistCity() {
    return MyLang.getString("app_deviceregist_city_textview_text", R.string.app_deviceregist_city_textview_text);
  }

  public static String getLocationTitle() {
    return MyLang.getString("app_systemauthorization_locationtitle_textview_text", R.string.app_systemauthorization_locationtitle_textview_text);
  }

  public static String getLocationContent() {
    return MyLang.getString("app_systemauthorization_locationdescription_textview_text", R.string.app_systemauthorization_locationdescription_textview_text);
  }

  public static String getBluetoothtitle() {
    return MyLang.getString("app_systemauthorization_bluetoothtitle_textview_text", R.string.app_systemauthorization_bluetoothtitle_textview_text);
  }

  public static String getBluetoothContent() {
    return MyLang.getString("app_systemauthorization_bluetoothdescription_textview_text", R.string.app_systemauthorization_bluetoothdescription_textview_text);
  }

  public static String getCameraTitle() {
    return MyLang.getString("app_systemauthorization_cameratitle_textview_text", R.string.app_systemauthorization_cameratitle_textview_text);
  }

  public static String getCameraContent() {
    return MyLang.getString("app_systemauthorization_cameradescription_textview_text", R.string.app_systemauthorization_cameradescription_textview_text);
  }

  public static String getphotoTitle() {
    return MyLang.getString("app_systemauthorization_albumtitle_textview_text", R.string.app_systemauthorization_albumtitle_textview_text);
  }

  public static String getphotoContent() {
    return MyLang.getString("app_systemauthorization_albumdescription_textview_text", R.string.app_systemauthorization_albumdescription_textview_text);
  }

  public static String app_usercenter_acount_textview_text() {
    return MyLang.getString("app_usercenter_acount_textview_text", R.string.app_usercenter_acount_textview_text);
  }

  public static String app_usercenter_dealer_location_textview_text() {
    return MyLang.getString("app_productdealers_title_textview_text", R.string.app_productdealers_title_textview_text);
  }

  public static String app_usercenter_help_center_textview_text() {
    return MyLang.getString("app_usercenter_help_textview_text", R.string.app_usercenter_help_textview_text);
  }
  public static String app_helpcenterdetail_title_textview_text() {
    return MyLang.getString("app_helpcenterdetail_title_textview_text", R.string.app_helpcenterdetail_title_textview_text);
  }



  public static String app_userDetail_email_textview_text() {
    return MyLang.getString("app_userDetail_email_textview_text", R.string.app_userDetail_email_textview_text);
  }

  public static String app_userDetail_changePassword_textview_text() {
    return MyLang.getString("app_userDetail_changePassword_textview_text", R.string.app_userDetail_changePassword_textview_text);
  }

  public static String app_messagesetting_title_textview_text() {
    return MyLang.getString("app_messagesetting_title_textview_text", R.string.app_messagesetting_title_textview_text);
  }

  public static String app_usercenter_message_textview_text() {
    return MyLang.getString("app_usercenter_message_textview_text", R.string.app_usercenter_message_textview_text);
  }

  public static String app_setting_language_textview_text() {
    return MyLang.getString("app_setting_language_textview_text", R.string.app_setting_language_textview_text);
  }


  public static String app_language_title_textview_text() {
    return MyLang.getString("app_language_title_textview_text", R.string.app_language_title_textview_text);
  }

  public static String app_setting_cache_textview_text() {
    return MyLang.getString("app_setting_cache_textview_text", R.string.app_setting_cache_textview_text);
  }

  public static String app_setting_version_textview_text() {
    return MyLang.getString("app_setting_version_textview_text", R.string.app_setting_version_textview_text);
  }

  public static String app_logoff_logoff_textview_text() {
    return MyLang.getString("app_logoff_logoff_textview_text", R.string.app_logoff_logoff_textview_text);
  }

  public static String app_setting_privacy_textview_text() {
    return MyLang.getString("app_setting_privacy_textview_text", R.string.app_setting_privacy_textview_text);
  }

  public static String app_privacy_title_textview_text() {
    return MyLang.getString("app_privacy_title_textview_text", R.string.app_privacy_title_textview_text);
  }

  public static String app_setting_findDevice_textview_text() {
    return MyLang.getString("app_setting_findDevice_textview_text", R.string.app_setting_findDevice_textview_text);
  }

  public static String app_setting_finddevicedescribe_textview_text() {
    return MyLang.getString("app_setting_finddevicedescribe_textview_text", R.string.app_setting_finddevicedescribe_textview_text);
  }

  public static String app_setting_logout_textview_text() {
    return MyLang.getString("app_setting_logout_textview_text", R.string.app_setting_logout_textview_text);
  }

  public static String app_userDetail_accountsettings_textview_text() {
    return MyLang.getString("app_userDetail_accountsettings_textview_text", R.string.app_userDetail_accountsettings_textview_text);
  }

  public static String app_privacy_cancelServices_textview_text() {
    return MyLang.getString("app_privacy_cancelServices_textview_text", R.string.app_privacy_cancelServices_textview_text);
  }

  public static String app_privacy_withdraw_authorize_textview_text() {
    return MyLang.getString("app_privacy_withdraw_authorize_textview_text", R.string.app_privacy_withdraw_authorize_textview_text);
  }

  public static String app_setting_title_textview_text() {
    return MyLang.getString("app_setting_title_textview_text", R.string.app_setting_title_textview_text);
  }

  public static String app_usercenter_setting_textview_text() {
    return MyLang.getString("app_usercenter_setting_textview_text", R.string.app_usercenter_setting_textview_text);
  }

  public static String app_userinfo_account_textview_text() {
    return MyLang.getString("app_userinfo_account_textview_text", R.string.app_userinfo_account_textview_text);
  }


  public static String app_OTA_upgrade_textview_text() {
    return MyLang.getString("app_OTA_upgrade_textview_text", R.string.app_OTA_upgrade_textview_text);
  }

  public static String app_OTAInfo_upgrade_textview_text() {
    return MyLang.getString("app_OTAInfo_upgrade_textview_text", R.string.app_OTAInfo_upgrade_textview_text);
  }

  public static String app_OTAguide_upgrade_button_text() {
    return MyLang.getString("app_OTAguide_upgrade_button_text", R.string.app_OTAguide_upgrade_button_text);
  }


  public static String app_OTAguide_upgrade_textview_text() {
    return MyLang.getString("app_OTAguide_upgrade_textview_text", R.string.app_OTAguide_upgrade_textview_text);
  }

  public static String app_OTAhistory_upgradehistory_textview_text() {
    return MyLang.getString("app_OTAhistory_upgradehistory_textview_text", R.string.app_OTAhistory_upgradehistory_textview_text);
  }

  public static String app_OTA_equipmentupgrade_textview_text() {
    return MyLang.getString("app_OTA_equipmentupgrade_textview_text", R.string.app_OTA_equipmentupgrade_textview_text);
  }

  public static String app_language_done_button_text() {
    return MyLang.getString("app_language_done_button_text", R.string.app_language_done_button_text);
  }

  public static String app_logoff_content_textview_text() {
    return MyLang.getString("app_logoff_content_textview_text", R.string.app_logoff_content_textview_text);
  }

  public static String app_logoff_description_textview_text() {
    return MyLang.getString("app_logoff_description_textview_text", R.string.app_logoff_description_textview_text);
  }

  public static String app_logoff_confirm_button_text() {
    return MyLang.getString("app_logoff_confirm_button_text", R.string.app_logoff_confirm_button_text);
  }

  public static String app_usercenter_services_textview_text() {
    return MyLang.getString("app_usercenter_services_textview_text", R.string.app_usercenter_services_textview_text);
  }

  public static String app_usercenter_dealer_textview_text() {
    return MyLang.getString("app_usercenter_dealer_textview_text", R.string.app_usercenter_dealer_textview_text);
  }

  public static String app_helpcenter_title_textview_text(){
    return MyLang.getString("app_helpcenter_title_textview_text", R.string.app_helpcenter_title_textview_text);
  }

  public static String app_privacy_privacyPolicy_textview_text() {
    return MyLang.getString("app_privacy_privacyPolicy_textview_text", R.string.app_privacy_privacyPolicy_textview_text);
  }

  public static String app_privacy_userAgreement_textview_text() {
    return MyLang.getString("app_privacy_userAgreement_textview_text", R.string.app_privacy_userAgreement_textview_text);
  }

  public static String app_userDetail_profile_textview_text() {
    return MyLang.getString("app_userDetail_profile_textview_text", R.string.app_userDetail_profile_textview_text);
  }

  public static String app_userDetail_name_textview_text() {
    return MyLang.getString("app_userDetail_name_textview_text", R.string.app_userDetail_name_textview_text);
  }

  public static String app_userinfo_firstname_inputview_placeholder() {
    return MyLang.getString("app_userinfo_firstname_inputview_placeholder", R.string.app_userinfo_firstname_inputview_placeholder);
  }

  public static String app_userinfo_lastname_inputview_placeholder() {
    return MyLang.getString("app_userinfo_lastname_inputview_placeholder", R.string.app_userinfo_lastname_inputview_placeholder);
  }

  public static String app_userinfo_areacode_textview_text() {
    return MyLang.getString("app_userinfo_areacode_textview_text", R.string.app_userinfo_areacode_textview_text);
  }

  public static String app_userinfo_selectgender_textview_text() {
    return MyLang.getString("app_userinfo_selectgender_textview_text", R.string.app_userinfo_selectgender_textview_text);
  }

  public static String app_base_datapickerok_button_text() {
    return MyLang.getString("app_base_datapickerok_button_text", R.string.app_base_datapickerok_button_text);
  }

  public static String app_setting_logoutalert_textview_text() {
    return MyLang.getString("app_setting_logoutalert_textview_text", R.string.app_setting_logoutalert_textview_text);
  }

  public static String app_setting_cancel_button_text() {
    return MyLang.getString("app_setting_cancel_button_text", R.string.app_setting_cancel_button_text);
  }

  public static String app_setting_logout_button_text() {
    return MyLang.getString("app_setting_logout_button_text", R.string.app_setting_logout_button_text);
  }

  public static String app_logoff_confirm_textview_text() {
    return MyLang.getString("app_logoff_confirm_textview_text", R.string.app_logoff_confirm_textview_text);
  }


  public static String app_logoff_cancel_button_text() {
    return MyLang.getString("app_logoff_cancel_button_text", R.string.app_logoff_cancel_button_text);
  }

  public static String app_base_cancel_button_text() {
    return MyLang.getString("app_base_cancel_button_text", R.string.app_base_cancel_button_text);
  }

  public static String app_OTAInfo_cancel_button_text() {
    return MyLang.getString("app_OTAInfo_cancel_button_text", R.string.app_OTAInfo_cancel_button_text);
  }

  public static String app_logoff_ok_button_text() {
    return MyLang.getString("app_logoff_ok_button_text", R.string.app_logoff_ok_button_text);
  }

  public static String app_deviceqrcodescan_helpok_button_text() {
    return MyLang.getString("app_deviceqrcodescan_helpok_button_text", R.string.app_deviceqrcodescan_helpok_button_text);
  }

  public static String app_devicelist_deletedevicealertok_button_text() {
    return MyLang.getString("app_devicelist_deletedevicealertok_button_text", R.string.app_devicelist_deletedevicealertok_button_text);
  }

  public static String app_setting_success_textview_text() {
    return MyLang.getString("app_setting_success_textview_text", R.string.app_setting_success_textview_text);
  }

  public static String app_setting_success_text() {
    return MyLang.getString("app_setting_success_text", R.string.app_setting_success_text);
  }

  public static String app_modifyavator_title_textview_text() {
    return MyLang.getString("app_modifyavator_title_textview_text", R.string.app_modifyavator_title_textview_text);
  }

  public static String app_userinfo_save_button_text() {
    return MyLang.getString("app_userinfo_save_button_text", R.string.app_userinfo_save_button_text);
  }

  public static String app_modifyavator_modify_textview_text() {
    return MyLang.getString("app_modifyavator_modify_textview_text", R.string.app_modifyavator_modify_textview_text);
  }

  public static String app_userinfo_male_textview_text() {
    return MyLang.getString("app_userinfo_male_textview_text", R.string.app_userinfo_male_textview_text);
  }

  public static String app_userinfo_female_textview_text() {
    return MyLang.getString("app_userinfo_female_textview_text", R.string.app_userinfo_female_textview_text);
  }

  public static String app_smartadddevice_descriptionfirst_textview_text() {
    return MyLang.getString("app_smartadddevice_descriptionfirst_textview_text", R.string.app_smartadddevice_descriptionfirst_textview_text);
  }

  public static String app_smartadddevice_descriptionsecond_textview_text() {
    return MyLang.getString("app_smartadddevice_descriptionsecond_textview_text", R.string.app_smartadddevice_descriptionsecond_textview_text);
  }

  public static String app_smartadddevice_connect_button_text() {
    return MyLang.getString("app_smartadddevice_connect_button_text", R.string.app_smartadddevice_connect_button_text);
  }

  public static String app_smartadddevice_cancel_button_text() {
    return MyLang.getString("app_smartadddevice_cancel_button_text", R.string.app_smartadddevice_cancel_button_text);
  }

  public static String app_OTA_back_button_text() {
    return MyLang.getString("app_OTA_back_button_text", R.string.app_OTA_back_button_text);
  }

  public static String app_OTA_upgradesuccessful_textview_text() {
    return MyLang.getString("app_OTA_upgradesuccessful_textview_text", R.string.app_OTA_upgradesuccessful_textview_text);
  }

  public static String app_OTA_upgradefailed_textview_text() {
    return MyLang.getString("app_OTA_upgradefailed_textview_text", R.string.app_OTA_upgradefailed_textview_text);
  }

  public static String app_OTA_failedinfo_textview_text() {
    return MyLang.getString("app_OTA_failedinfo_textview_text", R.string.app_OTA_failedinfo_textview_text);
  }

  public static String app_OTA_newversion_textview_text() {
    return MyLang.getString("app_OTA_newversion_textview_text", R.string.app_OTA_newversion_textview_text);
  }

  public static String app_OTA_cancelUpgrade_textview_text() {
    return MyLang.getString("app_OTA_cancelUpgrade_textview_text", R.string.app_OTA_cancelUpgrade_textview_text);
  }

  public static String app_OTAInfo_cancelUpgrade_textview_text() {
    return MyLang.getString("app_OTAInfo_cancelUpgrade_textview_text", R.string.app_OTAInfo_cancelUpgrade_textview_text);
  }

  public static String app_OTAhistory_versionhistoryfirst_textview_text() {
    return MyLang.getString("app_OTAhistory_versionhistoryfirst_textview_text", R.string.app_OTAhistory_versionhistoryfirst_textview_text);
  }

  public static String app_OTAhistory_versionhistorysecond_textview_text() {
    return MyLang.getString("app_OTAhistory_versionhistorysecond_textview_text", R.string.app_OTAhistory_versionhistorysecond_textview_text);
  }


  public static String app_messagesetting_receive_textview_text() {
    return MyLang.getString("app_messagesetting_receive_textview_text", R.string.app_messagesetting_receive_textview_text);
  }

  public static String app_messagesetting_systemmessages_textview_text() {
    return MyLang.getString("app_messagesetting_systemmessages_textview_text", R.string.app_messagesetting_systemmessages_textview_text);
  }

  public static String app_messagesetting_systemtip_textview_text() {
    return MyLang.getString("app_messagesetting_systemtip_textview_text", R.string.app_messagesetting_systemtip_textview_text);
  }

  public static String app_messagesetting_device_textview_text() {
    return MyLang.getString("app_messagesetting_device_textview_text", R.string.app_messagesetting_device_textview_text);
  }

  public static String app_messagesetting_marketing_textview_text() {
    return MyLang.getString("app_messagesetting_marketing_textview_text", R.string.app_messagesetting_marketing_textview_text);
  }

  public static String app_messagesetting_devicesubtitle_textview_text() {
    return MyLang.getString("app_messagesetting_devicesubtitle_textview_text", R.string.app_messagesetting_devicesubtitle_textview_text);
  }

  public static String app_messagesetting_permissiontip_textview_text() {
    return MyLang.getString("app_messagesetting_permissiontip_textview_text", R.string.app_messagesetting_permissiontip_textview_text);
  }



  public static String app_messagesetting_marketingsubtitle_textview_text() {
    return MyLang.getString("app_messagesetting_marketingsubtitle_textview_text", R.string.app_messagesetting_marketingsubtitle_textview_text);
  }

  public static String app_messagecenter_title_textview_text() {
    return MyLang.getString("app_messagecenter_title_textview_text", R.string.app_messagecenter_title_textview_text);
  }

  public static String app_messagecenter_devicemessagecontent_textview_text() {
    return MyLang.getString("app_messagecenter_devicemessagecontent_textview_text", R.string.app_messagecenter_devicemessagecontent_textview_text);
  }

  public static String app_messagecenter_messagelistdelete_textview_text() {
    return MyLang.getString("app_messagecenter_messagelistdelete_textview_text", R.string.app_messagecenter_messagelistdelete_textview_text);
  }

  public static String app_messagecenter_messagelistsystemtitle_textview_text() {
    return MyLang.getString("app_messagecenter_messagelistsystemtitle_textview_text", R.string.app_messagecenter_messagelistsystemtitle_textview_text);
  }

  public static String app_messagecenter_messagelistmarketingtitle_textview_text() {
    return MyLang.getString("app_messagecenter_messagelistmarketingtitle_textview_text", R.string.app_messagecenter_messagelistmarketingtitle_textview_text);
  }

  public static String app_messagecenter_messagedetailtitle_textview_text() {
    return MyLang.getString("app_messagecenter_messagedetailtitle_textview_text", R.string.app_messagecenter_messagedetailtitle_textview_text);
  }

  public static String app_channelname_system_textview_text() {
    return MyLang.getString("app_channelname_system_textview_text", R.string.app_channelname_system_textview_text);
  }

  public static String app_channelname_marketing_textview_text() {
    return MyLang.getString("app_channelname_marketing_textview_text", R.string.app_channelname_marketing_textview_text);
  }

  public static String app_channelname_device_textview_text() {
    return MyLang.getString("app_channelname_device_textview_text", R.string.app_channelname_device_textview_text);
  }

  public static String app_version_upgradetitle_textview_text() {
    return MyLang.getString("app_version_upgradetitle_textview_text", R.string.app_version_upgradetitle_textview_text);
  }

  public static String app_version_upgrade_button_text() {
    return MyLang.getString("app_version_upgrade_button_text", R.string.app_version_upgrade_button_text);
  }

  public static String app_version_title_textview_text() {
    return MyLang.getString("app_version_title_textview_text", R.string.app_version_title_textview_text);
  }



  public static String app_about_subtitle_textview_text() {
    return MyLang.getString("app_about_subtitle_textview_text", R.string.app_about_subtitle_textview_text);
  }

  public static String app_about_tip_textview_text() {
    return MyLang.getString("app_about_tip_textview_text", R.string.app_about_tip_textview_text);
  }

  public static String app_version_latestversion_textview_text() {
    return MyLang.getString("app_version_latestversion_textview_text", R.string.app_version_latestversion_textview_text);
  }

  public static String app_version_currentversion_textview_text() {
    return MyLang.getString("app_version_currentversion_textview_text", R.string.app_version_currentversion_textview_text);
  }

  public static String app_about_content_textview_text() {
    return MyLang.getString("app_about_content_textview_text", R.string.app_about_content_textview_text);
  }

  public static String app_about_currentversion_textview_text() {
    return MyLang.getString("app_about_currentversion_textview_text", R.string.app_about_currentversion_textview_text);
  }

  public static String app_setting_aboutus_textview_text() {
    return MyLang.getString("app_setting_aboutus_textview_text", R.string.app_setting_aboutus_textview_text);
  }

  public static String app_systemauthorization_title_textview_text() {
    return MyLang.getString("app_systemauthorization_title_textview_text", R.string.app_systemauthorization_title_textview_text);
  }

  public static String app_messagealert_tosee_button_text() {
    return MyLang.getString("app_messagealert_tosee_button_text", R.string.app_messagealert_tosee_button_text);
  }

  public static String privacy_agreement_revoke_title() {
    return MyLang.getString("app_privacyagreementrevoke_title_textview_text", R.string.app_privacyagreementrevoke_title_textview_text);
  }

  public static String appPrivacyTitle() {
    return MyLang.getString("app_privacy_title_textview_text", R.string.app_privacy_title_textview_text);
  }

  public static String app_setting_systempermissionimanagement_textview_text() {

    return MyLang.getString("app_setting_systempermissionimanagement_textview_text", R.string.app_setting_systempermissionimanagement_textview_text);


  }

  public static String app_OTAInfo_currentversion_textview_text() {
    return MyLang.getString("app_OTAInfo_currentversion_textview_text", R.string.app_OTAInfo_currentversion_textview_text);
  }

  public static String app_OTAInfo_latestversion_textview_text() {
    return MyLang.getString("app_OTAInfo_latestversion_textview_text", R.string.app_OTAInfo_latestversion_textview_text);
  }

  public static String app_OTAInfo_upgrade_button_text() {
    return MyLang.getString("app_OTAInfo_upgrade_button_text", R.string.app_OTAInfo_upgrade_button_text);
  }

  public static String deviceQrcodeScanHelpDefaultText() {
    return MyLang.getString("app_deviceqrcodescan_helpdefault_textview_text", R.string.app_deviceqrcodescan_helpdefault_textview_text);
  }

  public static String privacyAgreementDetailDate() {
    return MyLang.getString("app_privacyagreementdetail_date_textview_text", R.string.app_privacyagreementdetail_date_textview_text);
  }

  public static String privacyagreementuserDatetext() {
    return MyLang.getString("app_privacyagreementuser_date_textview_text", R.string.app_privacyagreementuser_date_textview_text);
  }


  public static String containerDevicedonotuseToast() {
    return MyLang.getString("app_container_devicedonotusetoast_textview_text", R.string.app_container_devicedonotusetoast_textview_text);
  }

  public static String app_devicelist_mytool_textview_text() {
    return MyLang.getString("app_devicelist_mytool_textview_text", R.string.app_devicelist_mytool_textview_text);
  }

  public static String deviceListAddButton() {
    return MyLang.getString("app_devicelist_addtool_button_text", R.string.app_devicelist_addtool_button_text);
  }

  public static String app_setting_clearcachetip_textview_text() {
    return MyLang.getString("app_setting_clearcache_textview_text", R.string.app_setting_clearcache_textview_text);
  }

  public static String app_setting_clearcachecancle_button_text() {
    return MyLang.getString("app_setting_clearcachecancle_button_text", R.string.app_setting_clearcachecancle_button_text);
  }

  public static String app_setting_clearcacheclearnow_button_text() {
    return MyLang.getString("app_setting_clearcacheclearnow_button_text", R.string.app_setting_clearcacheclearnow_button_text);
  }

  public static String app_setting_clearcacheloading_textview_text() {
    return MyLang.getString("app_setting_clearcacheloading_textview_text", R.string.app_setting_clearcacheloading_textview_text);
  }

  public static String app_OTAhistory_currentversion_textview_text() {
    return MyLang.getString("app_OTAhistory_currentversion_textview_text", R.string.app_OTAhistory_currentversion_textview_text);
  }

  public static String app_userinfo_inputnickname_textview_text() {
    return MyLang.getString("app_userinfo_inputnickname_textview_text", R.string.app_userinfo_inputnickname_textview_text);
  }

  public static String app_userinfo_inputage_textview_text() {
    return MyLang.getString("app_userinfo_inputage_textview_text", R.string.app_userinfo_inputage_textview_text);
  }

  public static String app_userinfo_choosegender_textview_text() {
    return MyLang.getString("app_userinfo_choosegender_textview_text", R.string.app_userinfo_choosegender_textview_text);
  }

  public static String app_userinfo_chooseareacode_textview_text() {
    return MyLang.getString("app_userinfo_chooseareacode_textview_text", R.string.app_userinfo_chooseareacode_textview_text);
  }

  public static String app_userinfo_inputmobilephone_textview_text() {
    return MyLang.getString("app_userinfo_inputmobilephone_textview_text", R.string.app_userinfo_inputmobilephone_textview_text);
  }

  public static String app_userinfo_inputaddress_textview_text() {
    return MyLang.getString("app_userinfo_inputaddress_textview_text", R.string.app_userinfo_inputaddress_textview_text);
  }

  public static String app_userinfo_inputcountry_textview_text() {
    return MyLang.getString("app_userinfo_inputcountry_textview_text", R.string.app_userinfo_inputcountry_textview_text);
  }

  public static String app_userinfo_inputzipcode_textview_text() {
    return MyLang.getString("app_userinfo_inputzipcode_textview_text", R.string.app_userinfo_inputzipcode_textview_text);
  }


  public static String app_deviceregist_sn_inputview_placeholder() {
    return MyLang.getString("app_deviceregist_sn_inputview_placeholder", R.string.app_deviceregist_sn_inputview_placeholder);
  }

  public static String appDeviceregistTitletext() {
    return MyLang.getString("app_deviceregist_title_textview_text", R.string.app_deviceregist_title_textview_text);
  }

  public static String appDeviceregistsuccessTitleTextviewText() {
    return MyLang.getString("app_deviceregistsuccess_title_textview_text", R.string.app_deviceregistsuccess_title_textview_text);
  }

  public static String app_devicecode_modeno_textview_text() {
    return MyLang.getString("app_devicecode_modeno_textview_text", R.string.app_devicecode_modeno_textview_text);
  }

  public static String app_deviceregist_devicemodelno_textview_text() {
    return MyLang.getString("app_deviceregist_devicemodelno_textview_text", R.string.app_deviceregist_devicemodelno_textview_text);
  }

  public static String appcheckpasswordtitle() {
    return MyLang.getString("app_checkpassword_title_textview_text", R.string.app_checkpassword_title_textview_text);
  }

  public static String appModifyPasswordTitle() {
    return MyLang.getString("app_modifypassword_title_textview_text", R.string.app_modifypassword_title_textview_text);
  }

  public static String appfittinglistTitle() {
    return MyLang.getString("app_fittinglist_title_textview_text", R.string.app_fittinglist_title_textview_text);
  }


  public static String appForgetpasswordsuccessSubtextTextviewText() {
    return MyLang.getString("app_forgetpasswordsuccess_subtext_textview_text", R.string.app_forgetpasswordsuccess_subtext_textview_text);
  }

  public static String app_networkconfigblutoothconnect_registdevice_button_text() {
    return MyLang.getString("app_networkconfigblutoothconnect_registdevice_button_text", R.string.app_networkconfigblutoothconnect_registdevice_button_text);
  }

  public static String appNetworkConfigBlutoothconnectNotregistdevice() {
    return MyLang.getString("app_networkconfigblutoothconnect_notregistdevice_button_text", R.string.app_networkconfigblutoothconnect_notregistdevice_button_text);
  }




  public static String appForgetpasswordsuccessMaintext() {
    return MyLang.getString("app_forgetpasswordsuccess_maintext_textview_text", R.string.app_forgetpasswordsuccess_maintext_textview_text);
  }


  public static String app_OTAhistory_note_textview_text() {
    return MyLang.getString("app_OTAhistory_note_textview_text", R.string.app_OTAhistory_note_textview_text);
  }

  public static String app_OTAInfo_versionhistory_button_text() {
    return MyLang.getString("app_OTAInfo_versionhistory_button_text", R.string.app_OTAInfo_versionhistory_button_text);
  }

  public static String app_OTAInfo_upgradecontent_textview_text() {
    return MyLang.getString("app_OTAInfo_upgradecontent_textview_text", R.string.app_OTAInfo_upgradecontent_textview_text);
  }

  public static String appRegistNameTip() {
    return MyLang.getString("app_regist_firstnameinputerror_textview_text", R.string.app_regist_firstnameinputerror_textview_text);
  }

  public static String app_OTA_download_textview_text() {
    return MyLang.getString("app_OTA_download_textview_text", R.string.app_OTA_download_textview_text);
  }

  public static String app_OTA_upgrading_textview_text() {
    return MyLang.getString("app_OTA_upgrading_textview_text", R.string.app_OTA_upgrading_textview_text);
  }

  public static String app_OTA_keepconnected_textview_text() {
    return MyLang.getString("app_OTA_keepconnected_textview_text", R.string.app_OTA_keepconnected_textview_text);
  }

  public static String appPasswordInlineTip() {
    return MyLang.getString("app_login_password_textview_text", R.string.app_login_password_textview_text);
  }

  public static String app_forgetpasswordreset_passwordnotsame_textview_text() {
    return MyLang.getString("app_forgetpasswordreset_passwordnotsame_textview_text", R.string.app_forgetpasswordreset_passwordnotsame_textview_text);
  }

  public static String appRegistEmailInlineTip() {
    return MyLang.getString("app_regist_email_textview_text", R.string.app_regist_email_textview_text);
  }

  public static String app_networkconfig_bluetooth_permission_tip() {
    return MyLang.getString("app_networkconfig_bluetooth_permission_tip", R.string.app_networkconfig_bluetooth_permission_tip);
  }

  public static String appBluetoothOpenTip() {
    return MyLang.getString("app_devicesearch_notopenbluetoothdescription_textview_text", R.string.app_devicesearch_notopenbluetoothdescription_textview_text);
  }

  public static String appDeviceaddSelectbycategory() {
    return MyLang.getString("app_deviceadd_selectbycategory_textview_text", R.string.app_deviceadd_selectbycategory_textview_text);
  }

  public static String app_devicesearch_notfounddescription_textview_text() {
    return MyLang.getString("app_devicesearch_notfounddescription_textview_text", R.string.app_devicesearch_notfounddescription_textview_text);
  }

  public static String appDevicelistEmptydescription() {
    return MyLang.getString("app_devicelist_emptydescription_textview_text", R.string.app_devicelist_emptydescription_textview_text);
  }

  public static String app_messagelist_nomessage_textview_text() {
    return MyLang.getString("app_messagelist_nomessage_textview_text", R.string.app_messagelist_nomessage_textview_text);
  }

  public static String app_forgetpasswordverifycode_havesendfirst_textview_text() {
    return MyLang.getString("app_forgetpasswordverifycode_havesendfirst_textview_text", R.string.app_forgetpasswordverifycode_havesendfirst_textview_text);
  }

  public static String appRegistverifycodeSecondText() {
    return MyLang.getString("app_registverifycode_havesendsecond_textview_text", R.string.app_registverifycode_havesendsecond_textview_text);
  }

  public static String appDeviceregistDevicesnText() {
    return MyLang.getString("app_deviceregist_devicesn_textview_text", R.string.app_deviceregist_devicesn_textview_text);
  }

  public static String app_userinfo_nickname_inputview_placeholder() {
    return MyLang.getString("app_userinfo_nickname_inputview_placeholder", R.string.app_userinfo_nickname_inputview_placeholder);
  }

  public static String app_userinfo_age_inputview_placeholder() {
    return MyLang.getString("app_userinfo_age_inputview_placeholder", R.string.app_userinfo_age_inputview_placeholder);
  }

  public static String app_userinfo_gender_inputview_placeholder() {
    return MyLang.getString("app_userinfo_gender_inputview_placeholder", R.string.app_userinfo_gender_inputview_placeholder);
  }

  public static String app_userinfo_mobilenumber_inputview_placeholder() {
    return MyLang.getString("app_userinfo_mobilenumber_inputview_placeholder", R.string.app_userinfo_mobilenumber_inputview_placeholder);
  }

  public static String app_userinfo_address_inputview_placeholder() {
    return MyLang.getString("app_userinfo_address_inputview_placeholder", R.string.app_userinfo_address_inputview_placeholder);
  }

  public static String app_userinfo_country_inputview_placeholder() {
    return MyLang.getString("app_userinfo_country_inputview_placeholder", R.string.app_userinfo_country_inputview_placeholder);
  }

  public static String app_userinfo_zipcode_inputview_placeholder() {
    return MyLang.getString("app_userinfo_zipcode_inputview_placeholder", R.string.app_userinfo_zipcode_inputview_placeholder);
  }

  public static String app_deviceregist_another_inputview_placeholder() {
    return MyLang.getString("app_deviceregist_another_inputview_placeholder", R.string.app_deviceregist_another_inputview_placeholder);
  }


  public static String app_deviceregistactionsheet_receipt_textview_text() {
    return MyLang.getString("app_deviceregistactionsheet_receipt_textview_text", R.string.app_deviceregistactionsheet_receipt_textview_text);
  }

  public static String appDeviceregistRegisterButtonText() {
    return MyLang.getString("app_deviceregist_register_button_text", R.string.app_deviceregist_register_button_text);
  }

  public static String appDeviceregistfailedText() {
    return MyLang.getString("app_deviceregistfailed_failed_textview_text", R.string.app_deviceregistfailed_failed_textview_text);
  }

  public static String appDeviceregistfailedRetry() {
    return MyLang.getString("app_deviceregistfailed_retry_button_text", R.string.app_deviceregistfailed_retry_button_text);
  }

  public static String appDeviceregistfailedBack() {
    return MyLang.getString("app_deviceregistfailed_back_button_text", R.string.app_deviceregistfailed_back_button_text);
  }

  public static String appOTAguideUpgradeTip() {
    return MyLang.getString("app_OTAguide_upgradetip_textview_text", R.string.app_OTAguide_upgradetip_textview_text);
  }

  public static String appNetworkconfigblutoothconnectSuccess() {
    return MyLang.getString("app_networkconfigblutoothconnect_connectsuccess_textview_text", R.string.app_networkconfigblutoothconnect_connectsuccess_textview_text);
  }

  public static String appNetworkconfigblutoothconnectConnectfaileddescription() {
    return MyLang.getString("app_networkconfigblutoothconnect_connectfaileddescription_textview_text", R.string.app_networkconfigblutoothconnect_connectfaileddescription_textview_text);
  }

  public static String app_base_next_button_text() {
    return MyLang.getString("app_base_next_button_text", R.string.app_base_next_button_text);
  }

  public static String app_enter_your_question() {
    return MyLang.getString("app_helpcenter_question_textview_text", R.string.app_helpcenter_question_textview_text);
  }


  public static String app_device_part_detail_textview_text() {
    return MyLang.getString("app_fittinghelp_buy_button_text", R.string.app_fittinghelp_buy_button_text);
  }

  public static String app_devicemoreinfo_deletedevice_textview_text() {
    return MyLang.getString("app_devicemoreinfo_deletedevice_textview_text", R.string.app_devicemoreinfo_deletedevice_textview_text);
  }

  public static String appHelpcenterDetailRecommen() {
    return MyLang.getString("app_helpcenterdetail_recommen_textview_text", R.string.app_helpcenterdetail_recommen_textview_text);
  }

  public static String appHelpcenterSearchNodata() {
    return MyLang.getString("app_helpcentersearch_nodata_textview_text", R.string.app_helpcentersearch_nodata_textview_text);
  }

  public static String app_producthelp_manual_button_text() {
    return MyLang.getString("app_producthelp_manual_button_text", R.string.app_producthelp_manual_button_text);
  }

  public static String app_producthelp_video_button_text() {
    return MyLang.getString("app_producthelp_video_button_text", R.string.app_producthelp_video_button_text);
  }

  public static String app_producthelp_detail_button_text() {
    return MyLang.getString("app_producthelp_detail_button_text", R.string.app_producthelp_detail_button_text);
  }

  public static String app_producthelp_techspec_button_text() {
    return MyLang.getString("app_producthelp_techspec_button_text", R.string.app_producthelp_techspec_button_text);
  }

  public static String app_producthelp_faq_button_text() {
    return MyLang.getString("app_producthelp_faq_button_text", R.string.app_producthelp_faq_button_text);

  }

  public static String appMessagecenterDevicemessageerror() {
    return MyLang.getString("app_messagecenter_devicemessageerror_textview_text", R.string.app_messagecenter_devicemessageerror_textview_text);

  }


  public static String app_producthelp_textview_text() {
    return MyLang.getString("app_producthelp_title_textview_text", R.string.app_producthelp_title_textview_text);
  }


  public static String app_devicemoreinfo_name_textview_text() {
    return MyLang.getString("app_devicemoreinfo_name_textview_text", R.string.app_devicemoreinfo_name_textview_text);
  }

  public static String app_devicemoreinfo_regist_textview_text() {
    return MyLang.getString("app_devicemoreinfo_regist_textview_text", R.string.app_devicemoreinfo_regist_textview_text);
  }

  public static String app_devicemoreinfo_product_textview_text() {
    return MyLang.getString("app_devicemoreinfo_product_textview_text", R.string.app_devicemoreinfo_product_textview_text);
  }

  public static String app_devicemoreinfo_codes_textview_text() {
    return MyLang.getString("app_devicemoreinfo_codes_textview_text", R.string.app_devicecode_title_textview_text);
  }

  public static String app_fittinghelp_textview_text() {
    return MyLang.getString("app_fittinghelp_title_textview_text", R.string.app_fittinghelp_title_textview_text);
  }

  public static String app_deviceboard_manual_button_text() {
    return MyLang.getString("app_deviceboard_manual_button_text", R.string.app_deviceboard_manual_button_text);
  }

  public static String app_deviceboard_info_button_text() {
    return MyLang.getString("app_deviceboard_info_button_text", R.string.app_deviceboard_info_button_text);
  }

  public static String app_productdealers_dataempty_textview_text() {
    return MyLang.getString("app_productdealers_dataempty_textview_text", R.string.app_productdealers_dataempty_textview_text);
  }

  public static String getHiString() {
    return MyLang.getString("app_devicelist_hi_textview_text", R.string.app_devicelist_hi_textview_text);
  }

  public static String appDevicerenameToastTextview() {
    return MyLang.getString("app_devicerename_toast_textview_text", R.string.app_devicerename_toast_textview_text);
  }

  public static String appPrivacyagreementuserTitle() {
    return MyLang.getString("app_privacyagreementuser_title_textview_text", R.string.app_privacyagreementuser_title_textview_text);
  }

  public static String appPrivacyagreementuserPrivacyagreement() {
    return MyLang.getString("app_privacyagreementuser_privacyagreement_textview_text", R.string.app_privacyagreementuser_privacyagreement_textview_text);
  }

  public static String appPrivacyagreementuserUseragreement() {
    return MyLang.getString("app_privacyagreementuser_useragreement_textview_text", R.string.app_privacyagreementuser_useragreement_textview_text);
  }

  public static String appPrivacyagreementuserRevokeagreement() {
    return MyLang.getString("app_privacyagreementuser_revokeagreement_textview_text", R.string.app_privacyagreementuser_revokeagreement_textview_text);
  }

  public static String appPrivacyagreementuserCcelaccount() {
    return MyLang.getString("app_privacyagreementuser_cancelaccount_textview_text", R.string.app_privacyagreementuser_cancelaccount_textview_text);
  }

  public static String appDevicemoreinfoTitleText() {
    return MyLang.getString("app_devicemoreinfo_title_textview_text", R.string.app_devicemoreinfo_title_textview_text);
  }

  public static String appDevicerenameTitleText() {
    return MyLang.getString("app_devicerename_title_textview_text", R.string.app_devicerename_title_textview_text);
  }


  public static String app_usercenter_feedback_textview_text() {
    return MyLang.getString("app_usercenter_feedback_textview_text", R.string.app_usercenter_feedback_textview_text);

  }


  public static String getFeedbackDeviceTitle() {
    return MyLang.getString("app_feedback_problems_textview_text", R.string.app_feedback_problems_textview_text);

  }

  public static String getFeedbackAppissues() {
    return MyLang.getString("app_feedback_appissues_textview_text", R.string.app_feedback_appissues_textview_text);

  }

  public static String getFeedbackQuestionTitle() {
    return MyLang.getString("app_feedbackquestions_title_textview_text", R.string.app_feedbackquestions_title_textview_text);


  }

  public static String getQuestionDescriptionTitle() {
    return MyLang.getString("app_feedbackquestions_questiondescription_textview_text", R.string.app_feedbackquestions_questiondescription_textview_text);

  }

  public static String getImageOptional() {
    return MyLang.getString("app_feedbackquestions_imageoptional_textview_text", R.string.app_feedbackquestions_imageoptional_textview_text);

  }

  public static String getMobilePhoneNumberStr() {
    return MyLang.getString("app_feedbackquestions_mobilephone_textview_text", R.string.app_feedbackquestions_mobilephone_textview_text);

  }

  public static String getFeedbackPhonehint() {


    return MyLang.getString("app_feedbackquestions_entermobilephone_textview_text", R.string.app_feedbackquestions_entermobilephone_textview_text);

  }

  public static String getFeedbackSubmitButtonStr() {

    return MyLang.getString("app_feedbackquestions_submit_button_text", R.string.app_feedbackquestions_submit_button_text);

  }


  public static String getFeedBackHistoryTitleString() {
    return MyLang.getString("app_feedbackhistory_title_textview_text", R.string.app_feedbackhistory_title_textview_text);

  }

  public static String getFeedbackHistoryEditString() {
    return MyLang.getString("app_feedbackhistory_edit_button_text", R.string.app_feedbackhistory_edit_button_text);
  }

  public static String getFeedbackHistoryCancelString() {
    return MyLang.getString("app_feedbackhistory_cancel_button_text", R.string.app_feedbackhistory_cancel_button_text);
  }


  public static String getFeedbackHistoryDeleteString() {
    return MyLang.getString("app_feedbackhistory_delete_button_text", R.string.app_feedbackhistory_delete_button_text);
  }


  public static String getFeedbackdetailsimageoptionalString() {
    return MyLang.getString("app_feedbackdetails_imageoptional_textview_text", R.string.app_feedbackdetails_imageoptional_textview_text);
  }

  public static String getFeedbackdetailswhatreplytoString() {
    return MyLang.getString("app_feedbackdetails_whatreplyto_textview_text", R.string.app_feedbackdetails_whatreplyto_textview_text);
  }

  public static String getFeedbackdetailreplyString() {
    return MyLang.getString("app_feedbackdetails_reply_button_text", R.string.app_feedbackdetails_reply_button_text);

  }

  public static String getFeedbackdetailsTitleString() {

    return MyLang.getString("app_feedbackdetails_title_textview_text", R.string.app_feedbackdetails_title_textview_text);

  }

  public static String getFeedbackMoreString() {

    return MyLang.getString("app_feedback_more_button_text", R.string.app_feedback_more_button_text);

  }

  public static String app_feedbackdetails_publish_button_text() {
    return MyLang.getString("app_feedbackdetails_publish_button_text", R.string.app_feedbackdetails_publish_button_text);

  }

  public static String app_feedbackdetails_submittedpatient_textview_text() {
    return MyLang.getString("app_feedbackdetails_submittedpatient_textview_text", R.string.app_feedbackdetails_submittedpatient_textview_text);

  }

  public static String app_feedbackquestions_questiondescriptionhint_textview_text() {
    return MyLang.getString("app_feedbackquestions_questiondescriptionhint_textview_text", R.string.app_feedbackquestions_questiondescriptionhint_textview_text);

  }


  public static String app_feedbackquestions_enteradescriptiontip_textview_text() {
    return MyLang.getString("app_feedbackquestions_enteradescriptiontip_textview_text", R.string.app_feedbackquestions_enteradescriptiontip_textview_text);


  }

  public static String app_feedbackquestions_submitfailed_textview_text() {
    return MyLang.getString("app_feedbackquestions_submitfailed_textview_text", R.string.app_feedbackquestions_submitfailed_textview_text);


  }


  public static String app_feedback_noequipment_textview_text() {
    return MyLang.getString("app_feedback_noequipment_textview_text", R.string.app_feedback_noequipment_textview_text);

  }

  public static String app_feedbackhistory_nocontent_textview_text() {
    return MyLang.getString("app_feedbackhistory_nocontent_textview_text", R.string.app_feedbackhistory_nocontent_textview_text);

  }

  public static String app_feedbackhistory_confirmdelete_textview_text() {
    return MyLang.getString("app_feedbackhistory_confirmdelete_textview_text", R.string.app_feedbackhistory_confirmdelete_textview_text);


  }

  public static String getClassificationOfissues() {
    return MyLang.getString("app_feedbackquestions_classification_textview_text", R.string.app_feedbackquestions_classification_textview_text);


  }


  public static String getSystemPermissionAllowed() {

    return MyLang.getString("app_systemauthorization_allowed_textview_text", R.string.app_systemauthorization_allowed_textview_text);
  }

  public static String getSystemPermissionGrant() {

    return MyLang.getString("app_systemauthorization_grant_textview_text", R.string.app_systemauthorization_grant_textview_text);
  }


  public static String getUserManualStri() {
    return MyLang.getString("app_base_showfile_textview_text", R.string.app_base_showfile_textview_text);

  }

  //TODO新增多语言
  public static String getAppCommomLogOut() {
    return MyLang.getString("app_commom_log_outt", R.string.app_commom_log_out);
  }


  public static String app_deviceregistactionsheet_location_textview_text() {
    return MyLang.getString("app_deviceregistactionsheet_location_textview_text", R.string.app_deviceregistactionsheet_location_textview_text);
  }

  public static String getAppMessagedetailSuggestiontitleTextviewText() {
    return MyLang.getString("app_messagedetail_suggestiontitle_textview_text", R.string.app_messagedetail_suggestiontitle_textview_text);
  }

  public static String app_messagedetail_viewmore_textview_text() {
    return MyLang.getString("app_messagedetail_viewmore_textview_text", R.string.app_messagedetail_viewmore_textview_text);
  }

  public static String getAppDeviceregistReceiptInputviewPlaceholder() {
    return MyLang.getString("app_deviceregist_receipt_inputview_placeholder", R.string.app_deviceregist_receipt_inputview_placeholder);
  }

  public static String getAppDeviceqrcodescanCameraAuthorizedalerttitle() {
    return MyLang.getString("app_deviceqrcodescan_cameraAuthorizedalerttitle_textview_text", R.string.app_deviceqrcodescan_cameraAuthorizedalerttitle_textview_text);
  }

  public static String getAppDeviceqrcodescanCameraAuthorizedalertmessage() {
    return MyLang.getString("app_deviceqrcodescan_cameraAuthorizedalertmessage_textview_text", R.string.app_deviceqrcodescan_cameraAuthorizedalertmessage_textview_text);
  }

  public static String app_modifyavator_gosetting_textview_text() {
    return MyLang.getString("app_modifyavator_gosetting_textview_text", R.string.app_modifyavator_gosetting_textview_text);
  }


  public static String getAppNetworkconfigblutoothconnectConnectfailed() {
    return MyLang.getString("app_networkconfigblutoothconnect_connectfailed_textview_text", R.string.app_networkconfigblutoothconnect_connectfailed_textview_text);
  }

  //  public static String getLocationTitle() {
//    return MyLang.getString("app_systemauthorization_locationtitle_textview_text", R.string.app_systemauthorization_locationtitle_textview_text);
//  }
  public static String getAppNetworkconfigwificonnectStepsynchronizesuccess() {
    return MyLang.getString("app_networkconfigwificonnect_stepsynchronizesuccess_textview_text", R.string.app_networkconfigwificonnect_stepsynchronizesuccess_textview_text);
  }


  public static String getAppNetworkconfigwificonnectStepconnectsuccess() {
    return MyLang.getString("app_networkconfigwificonnect_stepconnectsuccess_textview_text", R.string.app_networkconfigwificonnect_stepconnectsuccess_textview_text);
  }


  public static String getAppNetworkconfigwificonnectStepbindsuccess() {
    return MyLang.getString("app_networkconfigwificonnect_stepbindsuccess_textview_text", R.string.app_networkconfigwificonnect_stepbindsuccess_textview_text);
  }

  public static String getAppMessagecenterDialogButtonClose() {
    return MyLang.getString("app_messagecenter_dialog_button_close_text", R.string.app_messagecenter_dialog_button_close_text);
  }

  public static String app_setting_unit_textview_text() {
    return MyLang.getString("app_setting_unit_textview_text", R.string.app_setting_unit_textview_text);
  }

  public static String app_unit_metric_textview_text() {
    return MyLang.getString("app_unit_metric_textview_text", R.string.app_unit_metric_textview_text);
  }

  public static String app_unit_imperial_textview_text() {
    return MyLang.getString("app_unit_imperial_textview_text", R.string.app_unit_imperial_textview_text);
  }

  public static String app_base_paste_button_text() {
    return MyLang.getString("app_base_paste_button_text", R.string.app_base_paste_button_text);
  }

  public static String app_networkconfigwifiinput_notice_textview_text() {
    return MyLang.getString("app_networkconfigwifiinput_notice_textview_text", R.string.app_networkconfigwifiinput_notice_textview_text);
  }

  public static String app_notiotbindresult_title_textview_text() {
    return MyLang.getString("app_notiotbindresult_title_textview_text", R.string.app_notiotbindresult_title_textview_text);
  }



  public static String app_notiotbindresult_sucess_textview_text() {
    return MyLang.getString("app_notiotbindresult_sucess_textview_text", R.string.app_notiotbindresult_sucess_textview_text);
  }

  public static String app_networkconfignotiot_sncode_isnull_textview_text() {
    return MyLang.getString("app_networkconfignotiot_sncode_isnull_textview_text", R.string.app_networkconfignotiot_sncode_isnull_textview_text);
  }

  public static String app_notiotbindresult_fail_textview_text() {
    return MyLang.getString("app_notiotbindresult_fail_textview_text", R.string.app_notiotbindresult_fail_textview_text);
  }

  public static String app_notiotbindresult_failnotice_textview_text() {
    return MyLang.getString("app_notiotbindresult_failnotice_textview_text",
      R.string.app_notiotbindresult_failnotice_textview_text);
  }

  public static String app_notiotbindresult_register_button_text() {
    return MyLang.getString("app_notiotbindresult_register_button_text",
      R.string.app_notiotbindresult_register_button_text);
  }

  public static String app_deviceboard_registinfo_button_text() {
    return MyLang.getString("app_deviceboard_registinfo_button_text",
      R.string.app_deviceboard_registinfo_button_text);
  }

  public static String app_notiotbindresult_done_button_text() {
    return MyLang.getString("app_notiotbindresult_done_button_text",
      R.string.app_notiotbindresult_done_button_text);
  }

  public static String app_notiotbindresult_retry_button_text() {
    return MyLang.getString("app_notiotbindresult_retry_button_text",
      R.string.app_notiotbindresult_retry_button_text);
  }




  public static String app_networkconfigwificonnect_connectsuccess_textview_text() {
    return MyLang.getString("app_networkconfigwificonnect_connectsuccess_textview_text", R.string.app_networkconfigwificonnect_connectsuccess_textview_text);
  }

  public static String app_base_nodata_textview_text() {
    return MyLang.getString("app_base_nodata_textview_text", R.string.app_base_nodata_textview_text);
  }

  public static String app_base_nonetwork_textview_text() {
    return MyLang.getString("app_base_nonetwork_textview_text", R.string.app_base_nonetwork_textview_text);
  }

  public static String app_messagedetail_error_textview_text() {
    return MyLang.getString("app_messagedetail_error_textview_text", R.string.app_messagedetail_error_textview_text);
  }


  public static String app_deviceregist_surveydetail_textview_text() {
    return MyLang.getString("app_deviceregist_surveydetail_textview_text", R.string.app_deviceregist_surveydetail_textview_text);
  }

  public static String app_deviceregist_lost_receipt_textview_text() {
    return MyLang.getString("app_deviceregist_lost_receipt_textview_text", R.string.app_deviceregist_lost_receipt_textview_text);
  }

  public static String app_feedback_historyunread_textview_text() {
    return MyLang.getString("app_feedback_historyunread_textview_text", R.string.app_feedback_historyunread_textview_text);
  }

  public static String app_devicemoreinfo_unregisted_textview_text() {
    return MyLang.getString("app_devicemoreinfo_unregisted_textview_text", R.string.app_devicemoreinfo_unregisted_textview_text);
  }

  public static String app_devicemoreinfo_registed_textview_text() {
    return MyLang.getString("app_devicemoreinfo_registed_textview_text", R.string.app_devicemoreinfo_registed_textview_text);
  }
  public static String app_devicemanagepartdetail_cancel_button_text() {
    return MyLang.getString("app_base_cancel_button_text", R.string.app_base_cancel_button_text);
  }
  public static String app_devicemanagepartdetail_confirm_button_text() {
    return MyLang.getString("app_devicemanagepartdetail_confirm_button_text", R.string.app_devicemanagepartdetail_confirm_button_text);
  }
  public static String app_devicemanagepartdetail_dialog_textview_text() {
    return MyLang.getString("app_devicemanagepartdetail_dialog_textview_text", R.string.app_devicemanagepartdetail_dialog_textview_text);
  }

  public static String app_devicemanagepartdetail_maintenance_textview_text() {
    return MyLang.getString("app_devicemanageaccessorie_maintenance_textview_text", R.string.app_devicemanageaccessorie_maintenance_textview_text);
  }
  public static String app_devicemanagepartdetail_nextmaintenance_textview_text() {
    return MyLang.getString("app_devicemanagepartdetail_nextmaintenance_textview_text", R.string.app_devicemanagepartdetail_nextmaintenance_textview_text);
  }

  public static String app_devicemanagepartdetail_maintenancetip_textview_text() {
    return MyLang.getString("app_devicemanagepartdetail_maintenancetip_textview_text", R.string.app_devicemanagepartdetail_maintenancetip_textview_text);
  }
  public static String app_devicemanagepartdetail_reset_button_text() {
    return MyLang.getString("app_devicemanagepartdetail_reset_button_text", R.string.app_devicemanagepartdetail_reset_button_text);
  }

  public static String app_devicemanagepartdetail_detail_textview_text() {
    return MyLang.getString("app_devicemanagepartdetail_detail_textview_text", R.string.app_devicemanagepartdetail_detail_textview_text);
  }
  public static String app_devicemanageaccessorie_maintenance_textview_text() {
    return MyLang.getString("app_devicemanageaccessorie_maintenance_textview_text", R.string.app_devicemanageaccessorie_maintenance_textview_text);
  }
  public static String app_devicemanageaccessorie_maintenancein_textview_text() {
    return MyLang.getString("app_devicemanageaccessorie_maintenancein_textview_text", R.string.app_devicemanageaccessorie_maintenancein_textview_text);
  }

  public static String app_devicemanageaccessorie_workinghours_textview_text() {
    return MyLang.getString("app_devicemanageaccessorie_workinghours_textview_text", R.string.app_devicemanageaccessorie_workinghours_textview_text);
  }

  public static String app_devicemanageaccessorie_days_textview_text() {
    return MyLang.getString("app_devicemanageaccessorie_days_textview_text", R.string.app_devicemanageaccessorie_days_textview_text);
  }

  public static String app_devicerename_rule_textview_text() {
    return MyLang.getString("app_devicerename_rule_textview_text", R.string.app_devicerename_rule_textview_text);
  }
  public static String app_no_iot_panel_rename_success() {
    return MyLang.getString("app_no_iot_panel_rename_success", R.string.app_no_iot_panel_rename_success);
  }


  public static String app_no_iot_panel_rename_fail() {
    return MyLang.getString("app_no_iot_panel_rename_success", R.string.app_no_iot_panel_rename_success);
  }


  public static String getWificonnectStepbinded() {
    return MyLang.getString("app_networkconfigwificonnect_stepbinded_textview_text", R.string.app_networkconfigwificonnect_stepbinded_textview_text);
  }
  public static String getStepsynchronized() {
    return MyLang.getString("app_networkconfigwificonnect_stepsynchronized_textview_text", R.string.app_networkconfigwificonnect_stepsynchronized_textview_text);
  }

  public static String getStepconnected() {
    return MyLang.getString("app_networkconfigwificonnect_stepconnected_textview_text", R.string.app_networkconfigwificonnect_stepconnected_textview_text);
  }


  public static String app_oobe_sign_up_password_char_and_spaces() {
    return MyLang.getString("app_oobe_sign_up_password_char_and_spaces", R.string.app_oobe_sign_up_password_char_and_spaces);
  }

  public static String app_oobe_sign_up_password_confirm_numbers_and_letters() {
    return MyLang.getString("app_oobe_sign_up_password_confirm_numbers_and_letters", R.string.app_oobe_sign_up_password_confirm_numbers_and_letters);
  }

  public static String app_regist_password_error_worning_header() {
    return MyLang.getString("app_regist_password_error_worning_header", R.string.app_regist_password_error_worning_header);
  }

  public static String app_deviceregistsuccess_success_textview_text(){
     return MyLang.getString("app_deviceregistsuccess_success_textview_text", R.string.app_deviceregistsuccess_success_textview_text);
  }

  public static String app_product_only_available_ego_fleet_app(){
    return MyLang.getString("app_product_only_available_ego_fleet_app", R.string.app_product_only_available_ego_fleet_app);
  }

  public static String app_usercenter_usernamedefault_textview_text(){
    return MyLang.getString("app_usercenter_usernamedefault_textview_text", R.string.app_usercenter_usernamedefault_textview_text);
  }

  public static String app_usercenter_profile_textview_text(){
    return MyLang.getString("app_usercenter_profile_textview_text", R.string.app_usercenter_profile_textview_text);
  }

  public static String app_userDetail_accountcancel_textview_text(){
    return MyLang.getString("app_userDetail_accountcancel_textview_text", R.string.app_userDetail_accountcancel_textview_text);
  }

  public static String app_forgetpassword_description_textview_text(){
    return MyLang.getString("app_forgetpassword_description_textview_text", R.string.app_forgetpassword_description_textview_text);
  }

  public static String app_OTAguide_offline_textview_text(){
    return MyLang.getString("app_OTAguide_offline_textview_text", R.string.app_OTAguide_offline_textview_text);
  }

  public static String app_accountverification_title_textview_text(){
    return MyLang.getString("app_accountverification_title_textview_text", R.string.app_accountverification_title_textview_text);
  }
  public static String app_devicelist_turnonheat_button_text(){
    return MyLang.getString("app_devicelist_turnonheat_button_text", R.string.app_devicelist_turnonheat_button_text);
  }
  public static String app_devicelist_turnoffheat_button_text(){
    return MyLang.getString("app_devicelist_turnoffheat_button_text", R.string.app_devicelist_turnoffheat_button_text);
  }

  public static String app_signup_passwordcheckemail_textview_text(){
    return MyLang.getString("app_signup_passwordcheckemail_textview_text", R.string.app_signup_passwordcheckemail_textview_text);
  }


  public static String app_moresolutions_title_textview_text(){
    return MyLang.getString("app_moresolutions_title_textview_text", R.string.app_moresolutions_title_textview_text);
  }
  public static String app_moresolutions_subtitle_textview_text(){
    return MyLang.getString("app_moresolutions_subtitle_textview_text", R.string.app_moresolutions_subtitle_textview_text);
  }
  public static String app_moresolutions_solution1_textview_text(){
    return MyLang.getString("app_moresolutions_solution1_textview_text", R.string.app_moresolutions_solution1_textview_text);
  }
  public static String app_moresolutions_solution2_textview_text(){
    return MyLang.getString("app_moresolutions_solution2_textview_text", R.string.app_moresolutions_solution2_textview_text);
  }
  public static String app_moresolutions_moresolutions_textview_text(){
    return MyLang.getString("app_moresolutions_moresolutions_textview_text", R.string.app_moresolutions_moresolutions_textview_text);
  }
  public static String app_usermanual_title_textview_text(){
    return MyLang.getString("app_usermanual_title_textview_text", R.string.app_usermanual_title_textview_text);
  }
  public static String app_moresolutions_moresolutionsor_textview_text(){
    return MyLang.getString("app_moresolutions_moresolutionsor_textview_text", R.string.app_moresolutions_moresolutionsor_textview_text);
  }

  public static String app_base_ok_button_text(){
    return MyLang.getString("app_base_ok_button_text", R.string.app_base_ok_button_text);
  }
  public static String app_base_notopenbluetoothalert_textview_text(){
    return MyLang.getString("app_base_notopenbluetoothalert_textview_text", R.string.app_base_notopenbluetoothalert_textview_text);
  }


  public static String app_networkconfigwifiinput_title_textview_text(){
    return MyLang.getString("app_networkconfigwifiinput_title_textview_text", R.string.app_networkconfigwifiinput_title_textview_text);
  }

  public static String app_networkconfigwificonnect_title_textview_text(){
    return MyLang.getString("app_networkconfigwificonnect_title_textview_text", R.string.app_networkconfigwificonnect_title_textview_text);
  }


  public static String app_devicelist_night_textview_text(){
    return MyLang.getString("app_devicelist_night_textview_text", R.string.app_devicelist_night_textview_text);
  }

  public static String app_devicelist_evening_textview_text(){
    return MyLang.getString("app_devicelist_evening_textview_text", R.string.app_devicelist_evening_textview_text);
  }

  public static String app_devicelist_afternoon_textview_text(){
    return MyLang.getString("app_devicelist_afternoon_textview_text", R.string.app_devicelist_afternoon_textview_text);
  }
  public static String app_devicelist_morning_textview_text(){
    return MyLang.getString("app_devicelist_morning_textview_text", R.string.app_devicelist_morning_textview_text);
  }


  public static String app_forgetpassword_textview_text(){
    return MyLang.getString("app_forgetpassword_textview_text", R.string.app_forgetpassword_textview_text);
  }


  public static String app_privacyagreementrevoke_noticedetail_textview_text(){
    return MyLang.getString("app_privacyagreementrevoke_noticedetail_textview_text", R.string.app_privacyagreementrevoke_noticedetail_textview_text);
  }

  public static String app_privacyagreementrevoke_confirmtitle_textview_text(){
    return MyLang.getString("app_privacyagreementrevoke_confirmtitle_textview_text", R.string.app_privacyagreementrevoke_confirmtitle_textview_text);
  }
  public static String app_privacyagreementrevoke_confirmtdetail_textview_text(){
    return MyLang.getString("app_privacyagreementrevoke_confirmtdetail_textview_text", R.string.app_privacyagreementrevoke_confirmtdetail_textview_text);
  }
  public static String app_deviceqrcodescan_flashlight_button_text(){
    return MyLang.getString("app_deviceqrcodescan_flashlight_button_text", R.string.app_deviceqrcodescan_flashlight_button_text);
  }

  public static String app_productdealers_visitwebsite_button_text(){
    return MyLang.getString("app_productdealers_visitwebsite_button_text", R.string.app_productdealers_visitwebsite_button_text);
  }

  public static String app_productdealers_directions_button_text(){
    return MyLang.getString("app_productdealers_directions_button_text", R.string.app_productdealers_directions_button_text);
  }

  public static String app_login_title_textview_text(){
    return MyLang.getString("app_login_title_textview_text", R.string.app_login_title_textview_text);
  }

  public static String app_about_title_textview_text(){
    return MyLang.getString("app_about_title_textview_text", R.string.app_about_title_textview_text);
  }

  public static String app_checkpassword_done_button_text(){
    return MyLang.getString("app_checkpassword_done_button_text", R.string.app_checkpassword_done_button_text);
  }

  public static String app_deviceregist_phone_textview_text(){
    return MyLang.getString("app_deviceregist_phone_textview_text", R.string.app_deviceregist_phone_textview_text);
  }

  public static String app_login_password_inputview_placeholder(){
    return MyLang.getString("app_login_password_inputview_placeholder", R.string.app_login_password_inputview_placeholder);
  }

  public static String app_messagesetting_system_textview_text(){
    return MyLang.getString("app_messagesetting_system_textview_text", R.string.app_messagesetting_system_textview_text);
  }

  public static String app_devicemoreinfo_deletedevicetitle_textview_text(){
    return MyLang.getString("app_devicemoreinfo_deletedevicetitle_textview_text", R.string.app_devicemoreinfo_deletedevicetitle_textview_text);
  }

  public static String app_base_refreshheaderloading_textview_text(){
    return MyLang.getString("app_base_refreshheaderloading_textview_text", R.string.app_base_refreshheaderloading_textview_text);
  }

  public static String app_deviceproductsearch_history_inputview_placeholder(){
    return MyLang.getString("app_deviceproductsearch_history_inputview_placeholder", R.string.app_deviceproductsearch_history_inputview_placeholder);
  }

  public static String app_base_servererror_textview_text(){
    return MyLang.getString("app_base_servererror_textview_text", R.string.app_base_servererror_textview_text);
  }

  public static String app_forgetpasswordsuccess_title_textview_text(){
    return MyLang.getString("app_forgetpasswordsuccess_title_textview_text", R.string.app_forgetpasswordsuccess_title_textview_text);
  }

  public static String app_devicesearch_add_textview_text(){
    return MyLang.getString("app_devicesearch_add_textview_text", R.string.app_devicesearch_add_textview_text);
  }

  public static String app_devicesearch_closetodevice_textview_text(){
    return MyLang.getString("app_devicesearch_closetodevice_textview_text", R.string.app_devicesearch_closetodevice_textview_text);
  }
  public static String app_deviceboard_fitting_button_text(){
    return MyLang.getString("app_deviceboard_fitting_button_text", R.string.app_deviceboard_fitting_button_text);
  }

  public static String app_modifyavator_save_button_text(){
    return MyLang.getString("app_modifyavator_save_button_text", R.string.app_modifyavator_save_button_text);
  }

  public static String app_setting_message_textview_text(){
    return MyLang.getString("app_setting_message_textview_text", R.string.app_setting_message_textview_text);
  }
  public static String app_fittinglist_dataempty_textview_text(){
    return MyLang.getString("app_fittinglist_dataempty_textview_text", R.string.app_fittinglist_dataempty_textview_text);
  }

  public static String app_devicecode_serialnumber_textview_text(){
    return MyLang.getString("app_devicecode_serialnumber_textview_text", R.string.app_devicecode_serialnumber_textview_text);
  }

  public static String app_devicecode_deviceid_textview_text(){
    return MyLang.getString("app_devicecode_deviceid_textview_text", R.string.app_devicecode_deviceid_textview_text);
  }

  public static String app_devicecode_firmwareversion_textview_text(){
    return MyLang.getString("app_devicecode_firmwareversion_textview_text", R.string.app_devicecode_firmwareversion_textview_text);
  }

  public static String app_scan_failed_tryagain_text(){
    return MyLang.getString("app_scan_failed_tryagain_text", R.string.app_scan_failed_tryagain_text);
  }

  public static String app_connect_fail_tips_text(){
    return MyLang.getString("app_connect_fail_tips_text", R.string.app_connect_fail_tips_text);
  }
  public static String app_base_done_button_text(){
    return MyLang.getString("app_base_done_button_text", R.string.app_base_done_button_text);
  }


  public static String app_modifypassword_passwordsame_textview_text(){
    return MyLang.getString("app_modifypassword_passwordsame_textview_text", R.string.app_modifypassword_passwordsame_textview_text);
  }

  public static String app_checkverifycode_is_invalid_text(){
    return MyLang.getString("app_checkverifycode_is_invalid_text", R.string.app_checkverifycode_is_invalid_text);
  }

  public static String app_password_comfirm_mismatch_text(){
    return MyLang.getString("app_password_comfirm_mismatch_text", R.string.app_password_comfirm_mismatch_text);
  }
  public static String app_base_email_format_text(){
    return MyLang.getString("app_base_email_format_text", R.string.app_base_email_format_text);
  }
  public static String app_verifycode_sendtoomuch_text(){
    return MyLang.getString("app_verifycode_sendtoomuch_text", R.string.app_verifycode_sendtoomuch_text);
  }
  public static String app_forget_confirm_email_text(){
    return MyLang.getString("app_forget_confirm_email_text", R.string.app_forget_confirm_email_text);
  }


  public static String app_deviceregist_receiptupload_textview_text(){
    return MyLang.getString("app_deviceregist_receiptupload_textview_text", R.string.app_deviceregist_receiptupload_textview_text);
  }
  public static String app_deviceregist_survey_textview_text(){
    return MyLang.getString("app_deviceregist_survey_textview_text", R.string.app_deviceregist_survey_textview_text);
  }

  public static String app_base_inputrequirederror_textview_text(){
    return MyLang.getString("app_base_inputrequirederror_textview_text", R.string.app_base_inputrequirederror_textview_text);
  }
  public static String app_base_nameinputerror_textview_text(){
    return MyLang.getString("app_base_nameinputerror_textview_text", R.string.app_base_nameinputerror_textview_text);
  }

  public static String app_networkconfigwificonnect_registdevice_button_text(){
    return MyLang.getString("app_networkconfigwificonnect_registdevice_button_text", R.string.app_networkconfigwificonnect_registdevice_button_text);
  }

  public static String app_select_wifistatus_connected_text(){
    return MyLang.getString("app_select_wifistatus_connected_text", R.string.app_select_wifistatus_connected_text);
  }

  public static String app_messagecenter_detail_processingsuggestions_text(){
    return MyLang.getString("app_messagecenter_detail_processingsuggestions_text", R.string.app_messagecenter_detail_processingsuggestions_text);
  }

  public static String app_privacyagreementrevoke_noticedetailtitle_textview(){
    return MyLang.getString("app_privacyagreementrevoke_noticedetailtitle_textview", R.string.app_privacyagreementrevoke_noticedetailtitle_textview);
  }


  public static String app_base_pictureselect_other_text(){
    return MyLang.getString("app_base_pictureselect_other_text", R.string.app_base_pictureselect_other_text);
  }
  public static String app_deviceregist_surveynavtitle_textview_text(){
    return MyLang.getString("app_deviceregist_surveynavtitle_textview_text", R.string.app_deviceregist_surveynavtitle_textview_text);
  }

  public static String app_messagecenterbanner_now_button_text(){
    return MyLang.getString("app_messagecenterbanner_now_button_text", R.string.app_messagecenterbanner_now_button_text);
  }

  public static String app_forgetpasswordsuccess_maintext_textview_text(){
    return MyLang.getString("app_forgetpasswordsuccess_maintext_textview_text", R.string.app_forgetpasswordsuccess_maintext_textview_text);
  }


  public static String app_deviceboard_moretocome_button_text(){
    return MyLang.getString("app_deviceboard_moretocome_button_text", R.string.app_deviceboard_moretocome_button_text);
  }

  public static String app_device_regist_invalidserialnumber_text(){
    return MyLang.getString("app_device_regist_invalidserialnumber_text", R.string.app_device_regist_invalidserialnumber_text);
  }

  public static String app_deviceregistsuccess_ifkit_textview_text(){
    return MyLang.getString("app_deviceregistsuccess_ifkit_textview_text",R.string.app_deviceregistsuccess_ifkit_textview_text);
  }

  public static String app_base_locationpermissionalert_textview_text(){
    return MyLang.getString("app_base_locationpermissionalert_textview_text",R.string.app_base_locationpermissionalert_textview_text);
  }

  public static String app_base_gpsswitchalert_textview_text(){
    return MyLang.getString("app_base_gpsswitchalert_textview_text",R.string.app_base_gpsswitchalert_textview_text);
  }

  public static String app_selectwifi_mynetworks_textview_text(){
    return MyLang.getString("app_selectwifi_mynetworks_textview_text",R.string.app_selectwifi_mynetworks_textview_text);
  }

  public static String app_selectwifi_othernetworks_textview_text(){
    return MyLang.getString("app_selectwifi_othernetworks_textview_text",R.string.app_selectwifi_othernetworks_textview_text);
  }
  public static String app_messagecenter_systemmessage_textview_text(){
    return MyLang.getString("app_messagecenter_systemmessage_textview_text",R.string.app_messagecenter_systemmessage_textview_text);
  }

  public static String app_messagecenter_marketingmessage_textview_text(){
    return MyLang.getString("app_messagecenter_marketingmessage_textview_text",R.string.app_messagecenter_marketingmessage_textview_text);
  }

  public static String app_container_rnbundlenameerror_textview_text(){
    return MyLang.getString("app_container_rnbundlenameerror_textview_text",R.string.app_container_rnbundlenameerror_textview_text);
  }

  public static String app_deviceregist_giftwithresidential_textview_text() {
    return MyLang.getString("app_deviceregist_giftwithresidential_textview_text", R.string.app_deviceregist_giftwithresidential_textview_text);
  }

  public static String app_evaluatedialog_no_textview_text() {
    return MyLang.getString("app_evaluatedialog_no_textview_text", R.string.app_evaluatedialog_no_textview_text);
  }

  public static String app_evaluatedialog_yes_textview_text() {
    return MyLang.getString("app_evaluatedialog_yes_textview_text", R.string.app_evaluatedialog_yes_textview_text);
  }
  public static String app_evaluatedialog_content_textview_text() {
    return MyLang.getString("app_evaluatedialog_content_textview_text", R.string.app_evaluatedialog_content_textview_text);
  }
  public static String app_regist_emailisexists_textview_text() {
    return MyLang.getString("app_regist_emailisexists_textview_text", R.string.app_regist_emailisexists_textview_text);
  }
  public static String app_upgradedialog_content_textview_text() {
    return MyLang.getString("app_upgradedialog_content_textview_text", R.string.app_upgradedialog_content_textview_text);
  }
  public static String app_upgradedialog_later_textview_text() {
    return MyLang.getString("app_upgradedialog_later_textview_text", R.string.app_upgradedialog_later_textview_text);
  }
  public static String app_upgradedialog_upgrade_textview_text() {
    return MyLang.getString("app_upgradedialog_upgrade_textview_text", R.string.app_upgradedialog_upgrade_textview_text);
  }

  //EU_CRM
  public static String app_registaccountype_domestictitle_textview_text() {
    return MyLang.getString("app_registaccountype_domestictitle_textview_text", R.string.app_registaccountype_domestictitle_textview_text);
  }
  public static String app_registaccountype_domesticdescription_textview_text() {
    return MyLang.getString("app_registaccountype_domesticdescription_textview_text",R.string.app_registaccountype_domesticdescription_textview_text);
  }
  public static String app_registaccountype_commercialtitle_textview_text() {
    return MyLang.getString("app_registaccountype_commercialtitle_textview_text",R.string.app_registaccountype_commercialtitle_textview_text);
  }
  public static String app_registaccountype_commercialdescription_textview_text() {
    return MyLang.getString("app_registaccountype_commercialdescription_textview_text",R.string.app_registaccountype_commercialdescription_textview_text);
  }
  public static String app_registcommercialtype_done_button_text() {
    return MyLang.getString("app_registcommercialtype_done_button_text",R.string.app_registcommercialtype_done_button_text);
  }
  public static String app_registcommercialtype_description_textview_text() {
    return MyLang.getString("app_registcommercialtype_description_textview_text",R.string.app_registcommercialtype_description_textview_text);
  }
  public static String app_registselectcountry_title_textview_text() {
    return MyLang.getString("app_registselectcountry_title_textview_text",R.string.app_registselectcountry_title_textview_text);
  }
  public static String app_registselectcountry_selectcountry_textview_text() {
    return MyLang.getString("app_registselectcountry_selectcountry_textview_text",R.string.app_registselectcountry_selectcountry_textview_text);
  }
  public static String app_registaddressinfo_street_inputview_placeholder() {
    return MyLang.getString("app_registaddressinfo_street_inputview_placeholder",R.string.app_registaddressinfo_street_inputview_placeholder);
  }
  public static String app_registaddressinfo_city_inputview_placeholder() {
    return MyLang.getString("app_registaddressinfo_city_inputview_placeholder",R.string.app_registaddressinfo_city_inputview_placeholder);
  }
  public static String app_registaddressinfo_county_inputview_placeholder() {
    return MyLang.getString("app_registaddressinfo_county_inputview_placeholder",R.string.app_registaddressinfo_county_inputview_placeholder);
  }
  public static String app_registaddressinfo_postcode_inputview_placeholder() {
    return MyLang.getString("app_registaddressinfo_postcode_inputview_placeholder",R.string.app_registaddressinfo_postcode_inputview_placeholder);
  }
  public static String app_registeu_firstname_inputview_placeholder() {
    return MyLang.getString("app_registeu_firstname_inputview_placeholder",R.string.app_registeu_firstname_inputview_placeholder);
  }
  public static String app_registeu_lastname_inputview_placeholder() {
    return MyLang.getString("app_registeu_lastname_inputview_placeholder",R.string.app_registeu_lastname_inputview_placeholder);
  }
  public static String app_registeu_email_inputview_placeholder() {
    return MyLang.getString("app_registeu_email_inputview_placeholder",R.string.app_registeu_email_inputview_placeholder);
  }
  public static String app_registeu_password_inputview_placeholder() {
    return MyLang.getString("app_registeu_password_inputview_placeholder",R.string.app_registeu_password_inputview_placeholder);
  }
  public static String app_registeu_passwordconfirm_inputview_placeholder() {
    return MyLang.getString("app_registeu_passwordconfirm_inputview_placeholder",R.string.app_registeu_passwordconfirm_inputview_placeholder);
  }
  public static String app_regist_phone_inputview_placeholder() {
    return MyLang.getString("app_regist_phone_inputview_placeholder",R.string.app_regist_phone_inputview_placeholder);
  }
  public static String app_registeu_email_textview_text() {
    return MyLang.getString("app_registeu_email_textview_text",R.string.app_registeu_email_textview_text);
  }
  public static String app_registeu_login_button_text() {
    return MyLang.getString("app_registeu_login_button_text",R.string.app_registeu_login_button_text);
  }
  public static String app_logineu_description_textview_text() {
    return MyLang.getString("app_logineu_description_textview_text",R.string.app_logineu_description_textview_text);
  }
  public static String app_logineu_link_textview_text() {
    return MyLang.getString("app_logineu_link_textview_text",R.string.app_logineu_link_textview_text);
  }
  public static String app_deviceregisteu_receipt_input_placeholder() {
    return MyLang.getString("app_deviceregisteu_receipt_input_placeholder",R.string.app_deviceregisteu_receipt_input_placeholder);
  }
  public static String app_deviceregisteu_purchasedate_input_placeholder() {
    return MyLang.getString("app_deviceregisteu_purchasedate_input_placeholder",R.string.app_deviceregisteu_purchasedate_input_placeholder);
  }
  public static String app_deviceregisteu_kit_textview_text() {
    return MyLang.getString("app_deviceregisteu_kit_textview_text",R.string.app_deviceregisteu_kit_textview_text);
  }
  public static String app_deviceregisteu_yes_textview_text() {
    return MyLang.getString("app_deviceregisteu_yes_textview_text",R.string.app_deviceregisteu_yes_textview_text);
  }
  public static String app_deviceregisteu_no_textview_text() {
    return MyLang.getString("app_deviceregisteu_no_textview_text",R.string.app_deviceregisteu_no_textview_text);
  }
  public static String app_deviceregisteu_modelnumber_textview_text() {
    return MyLang.getString("app_deviceregisteu_modelnumber_textview_text",R.string.app_deviceregisteu_modelnumber_textview_text);
  }
  public static String app_deviceregisteu_devicesn_textview_text() {
    return MyLang.getString("app_deviceregisteu_devicesn_textview_text",R.string.app_deviceregisteu_devicesn_textview_text);
  }
  public static String app_deviceregisteu_sn_input_placeholder() {
    return MyLang.getString("app_deviceregisteu_sn_input_placeholder",R.string.app_deviceregisteu_sn_input_placeholder);
  }
  public static String app_deviceregisteu_regist_button_text() {
    return MyLang.getString("app_deviceregisteu_regist_button_text",R.string.app_deviceregisteu_regist_button_text);
  }
  public static String app_deviceregisteu_sninputerror_textview_text() {
    return MyLang.getString("app_deviceregisteu_sninputerror_textview_text",R.string.app_deviceregisteu_sninputerror_textview_text);
  }
  public static String app_devicelist_alertmessage_textview_text() {
    return MyLang.getString("app_devicelist_alertmessage_textview_text",R.string.app_devicelist_alertmessage_textview_text);
  }
  public static String app_devicelist_alerttip_textview_text() {
    return MyLang.getString("app_devicelist_alerttip_textview_text",R.string.app_devicelist_alerttip_textview_text);
  }
  public static String app_devicelist_alerttip_button_text() {
    return MyLang.getString("app_devicelist_alerttip_button_text",R.string.app_devicelist_alerttip_button_text);
  }

  public static String app_devicelist_toconnect_textview_text() {
    return MyLang.getString("app_devicelist_toconnect_textview_text",R.string.app_devicelist_toconnect_textview_text);
  }

  public static String app_deviceinfo_copytoast_textview_text() {
    return MyLang.getString("app_deviceinfo_copytoast_textview_text", R.string.app_deviceinfo_copytoast_textview_text);
  }


  public static String app_dealer_findstoresnear_textview_text() {
    return MyLang.getString("app_dealer_findstoresnear_textview_text", R.string.app_dealer_findstoresnear_textview_text);
  }

  public static String app_dealer_findstoresnearzipcode_textview_text() {
    return MyLang.getString("app_dealer_findstoresnearzipcode_textview_text", R.string.app_dealer_findstoresnearzipcode_textview_text);
  }

  public static String app_dealer_distance_textview_text() {
    return MyLang.getString("app_dealer_distance_textview_text", R.string.app_dealer_distance_textview_text);
  }

  public static String app_dealer_zipcodenotfound_textview_text() {
    return MyLang.getString("app_dealer_zipcodenotfound_textview_text", R.string.app_dealer_zipcodenotfound_textview_text);
  }
  public static String app_noiotpanelch7000orpowerstationtips_textview_text() {
    return MyLang.getString("app_noiotpanelch7000orpowerstationtips_textview_text", R.string.app_noiotpanelch7000orpowerstationtips_textview_text);
  }


  public static String app_networkconfigwifiinput_ssidoverflow_textview_text() {
    return MyLang.getString("app_networkconfigwifiinput_ssidoverflow_textview_text", R.string.app_networkconfigwifiinput_ssidoverflow_textview_text);
  }

  public static String app_networkconfigwifiinput_wifipasswordoverflow_textview_text() {
    return MyLang.getString("app_networkconfigwifiinput_wifipasswordoverflow_textview_text", R.string.app_networkconfigwifiinput_wifipasswordoverflow_textview_text);
  }

  public static String app_registverifycode_havesendfirst_textview_text() {
    return MyLang.getString("app_registverifycode_havesendfirst_textview_text", R.string.app_registverifycode_havesendfirst_textview_text);
  }

  public static String app_deviceregist_choosefile_button_text() {
    return MyLang.getString("app_deviceregist_choosefile_button_text", R.string.app_deviceregist_choosefile_button_text);
  }

  public static String app_deviceregist_illegalfile_textview_text() {
    return MyLang.getString("app_deviceregist_illegalfile_textview_text", R.string.app_deviceregist_illegalfile_textview_text);
  }

  public static String app_deviceadd_inputserialnumber_textview_text() {
    return MyLang.getString("app_deviceadd_inputserialnumber_textview_text", R.string.app_deviceadd_inputserialnumber_textview_text);
  }


  public static String app_deviceregist_snregistered_textview_text() {
    return MyLang.getString("app_deviceregist_snregistered_textview_text", R.string.app_deviceregist_snregistered_textview_text);
  }
  public static String app_base_cameraAuthorizedAlertTitle_textview_text() {
    return MyLang.getString("app_base_cameraAuthorizedAlertTitle_textview_text", R.string.app_base_cameraAuthorizedAlertTitle_textview_text);
  }

  public static String app_base_cameraAuthorizedAlertContent_textview_text() {
    return MyLang.getString("app_base_cameraAuthorizedAlertContent_textview_text", R.string.app_base_cameraAuthorizedAlertContent_textview_text);
  }


  public static String app_base_albumAuthorizedAlertTitle_textview_text() {
    return MyLang.getString("app_base_albumAuthorizedAlertTitle_textview_text", R.string.app_base_albumAuthorizedAlertTitle_textview_text);
  }


  public static String app_base_albumAuthorizedAlertContent_textview_text() {
    return MyLang.getString("app_base_albumAuthorizedAlertContent_textview_text", R.string.app_base_albumAuthorizedAlertContent_textview_text);
  }


  public static String app_deviceadd_productscompatibledescribe_textview_text() {
    return MyLang.getString("app_deviceadd_productscompatibledescribe_textview_text", R.string.app_deviceadd_productscompatibledescribe_textview_text);
  }

  public static String app_intentFeedbackAlertContent_textview_text() {
    return MyLang.getString("app_intentFeedbackAlertContent_textview_text", R.string.app_intentFeedbackAlertContent_textview_text);
  }

  public static String app_intentFeedbackAlertContinue_textview_text() {
    return MyLang.getString("app_intentFeedbackAlertContinue_textview_text", R.string.app_intentFeedbackAlertContinue_textview_text);
  }

  public static String app_intentFeedbackAlertCancel_textview_text() {
    return MyLang.getString("app_intentFeedbackAlertCancel_textview_text", R.string.app_intentFeedbackAlertCancel_textview_text);
  }

  public static String app_feedbackDetail_edit_textview_text() {
    return MyLang.getString("app_feedbackDetail_edit_textview_text", R.string.app_feedbackDetail_edit_textview_text);
  }

  public static String app_feedbackDetail_closethisthread_textview_text() {
    return MyLang.getString("app_feedbackDetail_closethisthread_textview_text", R.string.app_feedbackDetail_closethisthread_textview_text);
  }


  //share
  public static String app_usercenter_sharedevice_textview_text() {
    return MyLang.getString("app_usercenter_sharedevice_textview_text",R.string.app_usercenter_sharedevice_textview_text);
  }
  public static String app_sharedevice_message_textview_text() {
    return MyLang.getString("app_sharedevice_message_textview_text",R.string.app_sharedevice_message_textview_text);
  }
  public static String app_sharedevice_share_textview_text() {
    return MyLang.getString("app_sharedevice_share_textview_text",R.string.app_sharedevice_share_textview_text);
  }
  public static String app_sharedevice_accept_textview_text() {
    return MyLang.getString("app_sharedevice_accept_textview_text",R.string.app_sharedevice_accept_textview_text);
  }
  public static String app_sharedevice_sharewith_textview_text() {
    return MyLang.getString("app_sharedevice_sharewith_textview_text",R.string.app_sharedevice_sharewith_textview_text);
  }
  public static String app_sharedevice_users_textview_text() {
    return MyLang.getString("app_sharedevice_users_textview_text",R.string.app_sharedevice_users_textview_text);
  }
  public static String app_sharedevice_notshared_textview_text() {
    return MyLang.getString("app_sharedevice_notshared_textview_text",R.string.app_sharedevice_notshared_textview_text);
  }
  public static String app_sharedevice_sharedby_textview_text() {
    return MyLang.getString("app_sharedevice_sharedby_textview_text",R.string.app_sharedevice_sharedby_textview_text);
  }
  public static String app_sharedevice_accepted_textview_text() {
    return MyLang.getString("app_sharedevice_accepted_textview_text",R.string.app_sharedevice_accepted_textview_text);
  }
  public static String app_sharedevice_pending_textview_text() {
    return MyLang.getString("app_sharedevice_pending_textview_text",R.string.app_sharedevice_pending_textview_text);
  }
  public static String app_sharedevice_expired_textview_text() {
    return MyLang.getString("app_sharedevice_expired_textview_text",R.string.app_sharedevice_expired_textview_text);
  }
  public static String app_sharedevice_expirein_textview_text() {
    return MyLang.getString("app_sharedevice_expirein_textview_text",R.string.app_sharedevice_expirein_textview_text);
  }
  public static String app_sharedevice_days_textview_text() {
    return MyLang.getString("app_sharedevice_days_textview_text",R.string.app_sharedevice_days_textview_text);
  }
  public static String app_sharedevice_deviceempty_textview_text() {
    return MyLang.getString("app_sharedevice_deviceempty_textview_text",R.string.app_sharedevice_deviceempty_textview_text);
  }
  public static String app_sharedevice_shareempty_textview_text() {
    return MyLang.getString("app_sharedevice_shareempty_textview_text",R.string.app_sharedevice_shareempty_textview_text);
  }
  public static String app_sharedevice_shared_textview_text() {
    return MyLang.getString("app_sharedevice_shared_textview_text",R.string.app_sharedevice_shared_textview_text);
  }
  public static String app_sharedevice_noshared_textview_text() {
    return MyLang.getString("app_sharedevice_noshared_textview_text",R.string.app_sharedevice_noshared_textview_text);
  }
  public static String app_sharedevice_enteremail_textview_text() {
    return MyLang.getString("app_sharedevice_enteremail_textview_text",R.string.app_sharedevice_enteremail_textview_text);
  }
  public static String app_sharedevice_emailhint_textview_text() {
    return MyLang.getString("app_sharedevice_emailhint_textview_text",R.string.app_sharedevice_emailhint_textview_text);
  }
  public static String app_sharedevice_emailerror1_textview_text() {
    return MyLang.getString("app_sharedevice_emailerror1_textview_text",R.string.app_sharedevice_emailerror1_textview_text);
  }
  public static String app_sharedevice_emailerror2_textview_text() {
    return MyLang.getString("app_sharedevice_emailerror2_textview_text",R.string.app_sharedevice_emailerror2_textview_text);
  }
  public static String app_sharedevice_emailerror3_textview_text() {
    return MyLang.getString("app_sharedevice_emailerror3_textview_text",R.string.app_sharedevice_emailerror3_textview_text);
  }
  public static String app_sharedevice_invitemessage_textview_text() {
    return MyLang.getString("app_sharedevice_invitemessage_textview_text",R.string.app_sharedevice_invitemessage_textview_text);
  }
  public static String app_sharedevice_invite_textview_text() {
    return MyLang.getString("app_sharedevice_invite_textview_text",R.string.app_sharedevice_invite_textview_text);
  }
  public static String app_sharedevice_dialog1_textview_text() {
    return MyLang.getString("app_sharedevice_dialog1_textview_text",R.string.app_sharedevice_dialog1_textview_text);
  }
  public static String app_sharedevice_dialog2_textview_text() {
    return MyLang.getString("app_sharedevice_dialog2_textview_text",R.string.app_sharedevice_dialog2_textview_text);
  }
  public static String app_sharedevice_dialog3_textview_text() {
    return MyLang.getString("app_sharedevice_dialog3_textview_text",R.string.app_sharedevice_dialog3_textview_text);
  }
  public static String app_devicelist_shared_textview_text() {
    return MyLang.getString("app_devicelist_shared_textview_text",R.string.app_devicelist_shared_textview_text);
  }
  public static String app_sharedevice_accept_button_text() {
    return MyLang.getString("app_sharedevice_accept_button_text",R.string.app_sharedevice_accept_button_text);
  }
  public static String app_sharedevice_empty_button_text() {
    return MyLang.getString("app_sharedevice_empty_button_text",R.string.app_sharedevice_empty_button_text);
  }
  public static String app_sharedevice_sharesuccess_textview_text() {
    return MyLang.getString("app_sharedevice_sharesuccess_textview_text",R.string.app_sharedevice_sharesuccess_textview_text);
  }
  public static String app_sharedevice_invitesuccess_textview_text() {
    return MyLang.getString("app_sharedevice_invitesuccess_textview_text",R.string.app_sharedevice_invitesuccess_textview_text);
  }

  public static String app_tabbar_explore_textview_text() {
    return MyLang.getString("app_tabbar_explore_textview_text", R.string.app_tabbar_explore_textview_text);
  }

  public static String app_deviceregistpromote_title_textview_text() {
    return MyLang.getString("app_deviceregistpromote_title_textview_text", R.string.app_deviceregistpromote_title_textview_text);
  }

  public static String app_deviceregistpromote_shopnow_textview_text() {
    return MyLang.getString("app_deviceregistpromote_shopnow_textview_text", R.string.app_deviceregistpromote_shopnow_textview_text);
  }


  public static String app_datepicker_month1_textview_text() {
    return MyLang.getString("app_datepicker_month1_textview_text", R.string.app_datepicker_month1_textview_text);
  }

  public static String app_datepicker_month2_textview_text() {
    return MyLang.getString("app_datepicker_month2_textview_text", R.string.app_datepicker_month2_textview_text);
  }

  public static String app_datepicker_month3_textview_text() {
    return MyLang.getString("app_datepicker_month3_textview_text", R.string.app_datepicker_month3_textview_text);
  }

  public static String app_datepicker_month4_textview_text() {
    return MyLang.getString("app_datepicker_month4_textview_text", R.string.app_datepicker_month4_textview_text);
  }

  public static String app_datepicker_month5_textview_text() {
    return MyLang.getString("app_datepicker_month5_textview_text", R.string.app_datepicker_month5_textview_text);
  }

  public static String app_datepicker_month6_textview_text() {
    return MyLang.getString("app_datepicker_month6_textview_text", R.string.app_datepicker_month6_textview_text);
  }

  public static String app_datepicker_month7_textview_text() {
    return MyLang.getString("app_datepicker_month7_textview_text", R.string.app_datepicker_month7_textview_text);
  }

  public static String app_datepicker_month8_textview_text() {
    return MyLang.getString("app_datepicker_month8_textview_text", R.string.app_datepicker_month8_textview_text);
  }

  public static String app_datepicker_month9_textview_text() {
    return MyLang.getString("app_datepicker_month9_textview_text", R.string.app_datepicker_month9_textview_text);
  }

  public static String app_datepicker_month10_textview_text() {
    return MyLang.getString("app_datepicker_month10_textview_text", R.string.app_datepicker_month10_textview_text);
  }

  public static String app_datepicker_month11_textview_text() {
    return MyLang.getString("app_datepicker_month11_textview_text", R.string.app_datepicker_month11_textview_text);
  }

  public static String app_datepicker_month12_textview_text() {
    return MyLang.getString("app_datepicker_month12_textview_text", R.string.app_datepicker_month12_textview_text);
  }

  public static String app_appversiondialog_content_textview_text() {
    return MyLang.getString("app_appversiondialog_content_textview_text", R.string.app_appversiondialog_content_textview_text);
  }

  public static String app_appversiondialog_later_textview_text() {
    return MyLang.getString("app_appversiondialog_later_textview_text", R.string.app_appversiondialog_later_textview_text);
  }

  public static String app_appversiondialog_upgrade_textview_text() {
    return MyLang.getString("app_appversiondialog_upgrade_textview_text", R.string.app_appversiondialog_upgrade_textview_text);
  }


  public static String app_closefeedbackdialog_content_textview_text() {
    return MyLang.getString("app_closefeedbackdialog_content_textview_text", R.string.app_closefeedbackdialog_content_textview_text);
  }


  public static String app_closefeedbackdialog_cancel_textview_text() {
    return MyLang.getString("app_closefeedbackdialog_cancel_textview_text", R.string.app_closefeedbackdialog_cancel_textview_text);
  }


  public static String app_closefeedbackdialog_close_textview_text() {
    return MyLang.getString("app_closefeedbackdialog_close_textview_text", R.string.app_closefeedbackdialog_close_textview_text);
  }

  public static String app_closefeedbackdialog_title_textview_text() {
    return MyLang.getString("app_closefeedbackdialog_title_textview_text", R.string.app_closefeedbackdialog_title_textview_text);
  }

}
