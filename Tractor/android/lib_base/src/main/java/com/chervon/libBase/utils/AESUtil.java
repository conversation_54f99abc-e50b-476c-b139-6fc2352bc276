package com.chervon.libBase.utils;


import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.Key;
import java.security.Security;
import java.util.Arrays;


import com.blankj.utilcode.util.EncodeUtils;
import com.blankj.utilcode.util.LogUtils;
import com.google.android.gms.common.util.Base64Utils;

import org.bouncycastle.jce.provider.BouncyCastleProvider;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;

import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * Http parmes encode.
 */
public class AESUtil {

    private static  String password ="NoesisHomePosite";

    private static final String EncryptAlg ="AES";

    private static final String Cipher_Mode="AES/ECB/PKCS7Padding";

    private static final String Encode="UTF-8";

    private static final String Key_Encode="UTF-8";

    /**
     * AES/ECB/PKCS7Padding encryption
     * @param content
     * @return AES encrypted and then transferred to Base64
     * @throws Exception
     */
    public static String aesPKCS7PaddingEncrypt(String content) throws Exception {
        try {
            Cipher cipher = Cipher.getInstance(Cipher_Mode);
            byte[] realKey = password.getBytes(StandardCharsets.UTF_8);
            cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(realKey,EncryptAlg));
            byte[] data=cipher.doFinal(content.getBytes(Encode));
            String result= EncodeUtils.base64Encode2String(data);

            return result;
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("AES encryption failed\n" +
                    "\n：content=" +content +" key="+password);
        }
    }

    /**
     * AES/ECB/PKCS7Padding 解密
     * @param content
     * @return 先转base64 再解密
     * @throws Exception
     */
    public static String aesPKCS7PaddingDecrypt(String content) throws Exception {
        try {
            byte[] decodeBytes= EncodeUtils.base64Decode(content);
            Cipher cipher = Cipher.getInstance(Cipher_Mode);
            byte[] realKey = password.getBytes(StandardCharsets.UTF_8);

            cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(realKey,EncryptAlg));
            byte[] realBytes=cipher.doFinal(decodeBytes);
            return new String(realBytes, Encode);
        } catch (Exception e) {
            throw new Exception("AES decryption failed：Aescontent = " +e.fillInStackTrace(),e);
        }
    }

    /**
     *Process the key: if the length of the key is not enough, fill it with the specified paddingchar;
     *The space character can be used here, or 0 can be used here. The details can be changed according to the actual project requirements
     * @param key
     * @return
     * @throws Exception
     */
    public static byte[] getSecretKey(String key) throws Exception{
        final byte paddingChar=' ';

        byte[] realKey = new byte[16];
        byte[] byteKey = key.getBytes("UTF-8");
        for (int i =0;i<realKey.length;i++){
            if (i<byteKey.length){
                realKey[i] = byteKey[i];
            }else {
                realKey[i] = paddingChar;
            }
        }
        return realKey;
    }

    public static String getPassword() {
        return password;
    }

    public static void setPassword(String password) {
        AESUtil.password = password;
    }


    public static String getMD5(String info)
    {
        try
        {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            md5.update(info.getBytes("UTF-8"));
            byte[] encryption = md5.digest();

            StringBuffer strBuf = new StringBuffer();
            for (int i = 0; i < encryption.length; i++)
            {
                if (Integer.toHexString(0xff & encryption[i]).length() == 1)
                {
                    strBuf.append("0").append(Integer.toHexString(0xff & encryption[i]));
                }
                else
                {
                    strBuf.append(Integer.toHexString(0xff & encryption[i]));
                }
            }

            return strBuf.toString();
        }
        catch (NoSuchAlgorithmException e)
        {
            return "";
        }
        catch (UnsupportedEncodingException e)
        {
            return "";
        }
    }

        /**
         * 算法名称
         */
        private final static String KEY_ALGORITHM = "AES";
        /**
         * 加解密算法/模式/填充方式
         */
        private final static String DEFAULT_CIPHER_ALGORITHM = "AES/CBC/PKCS7Padding";
        private final static String IV_STR = "PKCS7Paddingqubh";

        /**
         * 加密方法
         *
         * @param content  要加密的字符串
         * @param password 加密密钥
         * @return
         */
        public static String encrypt(String content, String password) {
            try {
                int base = 16;
                // 如果密钥不足16位，那么就补足.  这个if 中的内容很重要
                byte[] keyBytes = password.getBytes();
                if (keyBytes.length % base != 0) {
                    int groups = keyBytes.length / base + (keyBytes.length % base != 0 ? 1 : 0);
                    byte[] temp = new byte[groups * base];
                    Arrays.fill(temp, (byte) 0);
                    System.arraycopy(keyBytes, 0, temp, 0, keyBytes.length);
                    keyBytes = temp;
                }
                // 初始化
                Security.addProvider(new BouncyCastleProvider());
                // 转化成JAVA的密钥格式
                Key key = new SecretKeySpec(keyBytes, KEY_ALGORITHM);
                // 初始化cipher
                Cipher cipher = null;
                cipher = Cipher.getInstance(DEFAULT_CIPHER_ALGORITHM, "BC");

                byte[] encryptedText = null;
                cipher.init(Cipher.ENCRYPT_MODE, key, new IvParameterSpec(IV_STR.getBytes("utf-8")));
                byte[] byteContent = content.getBytes("utf-8");
                encryptedText = cipher.doFinal(byteContent);
                return  EncodeUtils.base64Encode2String(encryptedText);
            } catch (Exception ex) {
                LogUtils.e("encrypt -->aes 加密失败:{}", ex.getMessage());
            }
            return null;
        }


    public static String decryptQf(String content, String password) {
        byte[] decryptedText = null;
        int base = 16;
        try {
            // 如果密钥不足16位，那么就补足.  这个if 中的内容很重要
            byte[] keyBytes = password.getBytes();
            if (keyBytes.length % base != 0) {
                int groups = keyBytes.length / base + (keyBytes.length % base != 0 ? 1 : 0);
                byte[] temp = new byte[groups * base];
                Arrays.fill(temp, (byte) 0);
                System.arraycopy(keyBytes, 0, temp, 0, keyBytes.length);
                keyBytes = temp;
            }
            // 初始化
            Security.addProvider(new BouncyCastleProvider());
            // 转化成JAVA的密钥格式
            Key key = new SecretKeySpec(keyBytes, KEY_ALGORITHM);
            // 初始化cipher
            Cipher cipher = Cipher.getInstance(DEFAULT_CIPHER_ALGORITHM, "BC");
            cipher.init(Cipher.DECRYPT_MODE, key, new IvParameterSpec(IV_STR.getBytes("utf-8")));
            decryptedText = cipher.doFinal(EncodeUtils.base64Decode(content));
            return new String(decryptedText, "utf-8");
        } catch (Exception e) {
            LogUtils.e("decrypt -->aes 解密失败:{}", e.getMessage());
            return null;
        }
    }

  public static String decrypt(String content, String password) {
    byte[] decryptedText = null;
    int base = 16;
    try {
      // 如果密钥不足16位，那么就补足.  这个if 中的内容很重要
      byte[] keyBytes = password.getBytes();
      if (keyBytes.length % base != 0) {
        int groups = keyBytes.length / base + (keyBytes.length % base != 0 ? 1 : 0);
        byte[] temp = new byte[groups * base];
        Arrays.fill(temp, (byte) 0);
        System.arraycopy(keyBytes, 0, temp, 0, keyBytes.length);
        keyBytes = temp;
      }
      // 初始化
      Security.addProvider(new BouncyCastleProvider());
      // 转化成JAVA的密钥格式
      Key key = new SecretKeySpec(keyBytes, KEY_ALGORITHM);
      // 初始化cipher
      Cipher cipher = Cipher.getInstance(DEFAULT_CIPHER_ALGORITHM, "BC");
      cipher.init(Cipher.DECRYPT_MODE, key, new IvParameterSpec(IV_STR.getBytes("utf-8")));
      decryptedText = cipher.doFinal(EncodeUtils.base64Decode(content));
      return new String(decryptedText, "utf-8");
    } catch (Exception e) {
      return null;
    }
  }

}
