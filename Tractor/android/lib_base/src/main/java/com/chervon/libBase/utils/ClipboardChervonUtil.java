package com.chervon.libBase.utils;

import android.app.Activity;
import android.content.ClipData;
import android.content.ClipDescription;
import android.content.ClipboardManager;
import android.content.Context;

import com.chervon.libBase.BaseApplication;


/**
 * @Author: 184862
 * @CreateDate: 2023/10/27
 * @description 粘贴板相关
 * @UpdateDate: 2023/10/27
 */

public class ClipboardChervonUtil {


  private ClipboardChervonUtil() {
    throw new UnsupportedOperationException("u can't instantiate me...");
  }



  /**
   * Copy the text to clipboard.
   *
   * @param label The label.
   * @param text  The text.
   */
  public static void copyText(final CharSequence label, final CharSequence text) {
    ClipboardManager cm = (ClipboardManager) BaseApplication.getInstance().getSystemService(Context.CLIPBOARD_SERVICE);
    //noinspection ConstantConditions
    cm.setPrimaryClip(ClipData.newPlainText(label, text));
  }

  /**
   * Clear the clipboard.
   */
  public static void clear() {
    ClipboardManager cm = (ClipboardManager) BaseApplication.getInstance().getSystemService(Context.CLIPBOARD_SERVICE);
    //noinspection ConstantConditions
    cm.setPrimaryClip(ClipData.newPlainText(null, ""));
  }



  /**
   * Return the text for clipboard.
   *
   * @return the text for clipboard
   */
  public static CharSequence getText() {
    ClipboardManager cm =  (ClipboardManager) BaseApplication.getInstance().getSystemService(Context.CLIPBOARD_SERVICE);
    //noinspection ConstantConditions
    ClipData clip = cm.getPrimaryClip();
    if (clip != null && clip.getItemCount() > 0) {
      CharSequence text = clip.getItemAt(0).coerceToText( BaseApplication.getInstance());
      if (text != null) {
        return text;
      }
    }
    return "";
  }

  /**
   * Add the clipboard changed listener.
   */
  public static void addChangedListener(final ClipboardManager.OnPrimaryClipChangedListener listener) {
    ClipboardManager cm =  (ClipboardManager) BaseApplication.getInstance().getSystemService(Context.CLIPBOARD_SERVICE);
    //noinspection ConstantConditions
    cm.addPrimaryClipChangedListener(listener);
  }

  /**
   * Remove the clipboard changed listener.
   */
  public static void removeChangedListener(final ClipboardManager.OnPrimaryClipChangedListener listener) {
    ClipboardManager cm = (ClipboardManager) Utils.getApp().getSystemService(Context.CLIPBOARD_SERVICE);
    //noinspection ConstantConditions
    cm.removePrimaryClipChangedListener(listener);
  }
}

