package com.chervon.libBase.utils;

import android.annotation.SuppressLint;
import android.content.Context;
import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.net.NetworkInfo;
import android.os.Build;
import android.telephony.TelephonyManager;

import androidx.annotation.RequiresApi;

import com.blankj.utilcode.util.LogUtils;
import com.chervon.libBase.BaseApplication;

/**
 * @Author: 184862
 * @CreateDate: 2024/6/20
 * @UpdateDate: 2024/6/20
 * 网络状态获取最新工具类适配Android M以上
 */
public class CVNNetworkUtils {
  //无法获取未知网络
  public static final int NETWORK_UNABLE_GET = -2;
  //网络未连接
  public static final int NETWORK_UNLINK = -1;
  //其他网络
  public static final int NETWORK_UNKONW = 0;
  //WIFI网络
  public static final int NETWORK_WIFI = 1;
  public static final int NETWORK_2G = 2;
  public static final int NETWORK_3G = 3;
  public static final int NETWORK_4G = 4;
  public static final int NETWORK_5G = 5;

  public static int getNetworkType() {

    // 获取 ConnectivityManager 实例
    ConnectivityManager connectivityManager = (ConnectivityManager) BaseApplication.getInstance().getSystemService(Context.CONNECTIVITY_SERVICE);
    if (connectivityManager != null) {
      // 获取活跃网络信息
      NetworkInfo networkInfo = connectivityManager.getActiveNetworkInfo();

      if (networkInfo != null && networkInfo.isConnected()) {
        // 根据不同的 API 版本获取网络类型
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
          // 使用 NetworkCapabilities 获取网络类型
          getNetworkTypeAboveM(connectivityManager);
        } else {
          // 使用 NetworkInfo 获取网络类型（仅适用于 Android M 及以下版本）
          getNetworkTypeBelowM(networkInfo);
        }
      } else {
        return NETWORK_UNLINK;
      }
    } else {
      return NETWORK_UNABLE_GET;
    }

    return NETWORK_UNABLE_GET;
  }


  // Android M 及以上版本获取网络类型
  @RequiresApi(api = Build.VERSION_CODES.M)
  private static int getNetworkTypeAboveM(ConnectivityManager connectivityManager) {
    Network network = connectivityManager.getActiveNetwork();
    NetworkCapabilities networkCapabilities = connectivityManager.getNetworkCapabilities(network);

    if (networkCapabilities != null) {
      if (networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI)) {
        return NETWORK_WIFI;
      } else if (networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR)) {
        // 获取手机当前连接的移动网络类型（如 4G、5G）
        getMobileNetworkType();
      } else {
        return NETWORK_UNKONW;
      }
    }
    return NETWORK_UNKONW;
  }

  /**
   * Android M 及以下版本获取网络类型
   *
   * @param networkInfo
   * @return
   */
  private static int getNetworkTypeBelowM(NetworkInfo networkInfo) {
    int networkType = networkInfo.getType();

    if (networkType == ConnectivityManager.TYPE_WIFI) {
      return NETWORK_WIFI;
    } else if (networkType == ConnectivityManager.TYPE_MOBILE) {
      // 获取手机当前连接的移动网络类型（如 4G、5G）
      getMobileNetworkType();
    } else {
      return NETWORK_UNKONW;
    }

    return NETWORK_UNABLE_GET;
  }


  /**
   * 获取手机当前连接的移动网络类型（如 4G、5G）
   *
   * @return
   */
  private static int getMobileNetworkType() {

    int status = NETWORK_UNKONW;

    try {
      TelephonyManager telephonyManager = (TelephonyManager) BaseApplication.getInstance().getSystemService(Context.TELEPHONY_SERVICE);

      if (telephonyManager != null) {
        @SuppressLint("MissingPermission") int networkType = telephonyManager.getNetworkType();

        switch (networkType) {
          case TelephonyManager.NETWORK_TYPE_UNKNOWN:
            status = NETWORK_UNKONW;
            break;
          case TelephonyManager.NETWORK_TYPE_GPRS:
          case TelephonyManager.NETWORK_TYPE_EDGE:
          case TelephonyManager.NETWORK_TYPE_CDMA:
          case TelephonyManager.NETWORK_TYPE_1xRTT:
          case TelephonyManager.NETWORK_TYPE_IDEN:
            status = NETWORK_2G;
            break;
          case TelephonyManager.NETWORK_TYPE_UMTS:
          case TelephonyManager.NETWORK_TYPE_EVDO_0:
          case TelephonyManager.NETWORK_TYPE_EVDO_A:
          case TelephonyManager.NETWORK_TYPE_HSDPA:
          case TelephonyManager.NETWORK_TYPE_HSUPA:
          case TelephonyManager.NETWORK_TYPE_HSPA:
          case TelephonyManager.NETWORK_TYPE_EVDO_B:
          case TelephonyManager.NETWORK_TYPE_EHRPD:
          case TelephonyManager.NETWORK_TYPE_HSPAP:
            status = NETWORK_3G;
            break;
          case TelephonyManager.NETWORK_TYPE_LTE:
            status = NETWORK_4G;
            break;
          case TelephonyManager.NETWORK_TYPE_NR:
            status = NETWORK_5G;
            break;
          default:
            status = NETWORK_UNKONW;
            break;
        }
      }

    }catch (Exception e){
      LogUtils.e("error");
    }

    return status;
  }
}
