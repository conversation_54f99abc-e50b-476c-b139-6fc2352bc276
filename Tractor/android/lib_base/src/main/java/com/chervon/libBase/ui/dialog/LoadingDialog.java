package com.chervon.libBase.ui.dialog;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.os.Bundle;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.databinding.DataBindingUtil;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;

import com.blankj.utilcode.util.FragmentUtils;
import com.bumptech.glide.Glide;
import com.chervon.libBase.R;
import com.chervon.libBase.databinding.DialogConfirmAlertBinding;
import com.chervon.libBase.databinding.DialogLoadingBinding;
import com.chervon.libBase.ui.BaseDialogFragment;

import java.util.concurrent.TimeUnit;

import io.reactivex.Observable;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Consumer;

public class LoadingDialog extends BaseDialogFragment {

  private int TIMEOUT = 60;
  //content text
  public String content;
  public Disposable disposable;
  private LoadingDialog() {
  }

  /**
   * @param fragmentManager 用于显示dialog]
   * @return void
   * @method show
   * @description show dialog and get update info.
   * @date: 2022/6/22 11:36
   * @author: LangMeng
   */
  public static LoadingDialog showDialog(@NonNull FragmentManager fragmentManager) {
    Fragment fragment = FragmentUtils.findFragment(fragmentManager, LoadingDialog.class.getName());
    if (fragment == null) {
      LoadingDialog loadingDialog = new LoadingDialog();
      loadingDialog.show(fragmentManager, LoadingDialog.class.getName());
      return loadingDialog;
    } else {
      return (LoadingDialog) fragment;
    }
  }

  /**
   * @param fragmentManager 用于显示dialog]
   * @return void
   * @method show
   * @description show dialog and get update info.
   * @date: 2022/6/22 11:36
   * @author: LangMeng
   */
  public static LoadingDialog showDialog(@NonNull FragmentManager fragmentManager, @NonNull String content) {
    Fragment fragment = FragmentUtils.findFragment(fragmentManager, LoadingDialog.class.getName());
    if (fragment == null) {
      LoadingDialog loadingDialog = new LoadingDialog();
      loadingDialog.content = content;
      loadingDialog.show(fragmentManager, LoadingDialog.class.getName());
      return loadingDialog;
    } else {
      return (LoadingDialog) fragment;
    }
  }

  @Override
  public void onCreate(@Nullable Bundle savedInstanceState) {
    super.onCreate(savedInstanceState);
    setStyle(DialogFragment.STYLE_NO_FRAME, R.style.NoBackgroundDialog);
  }

  @SuppressLint("NotifyDataSetChanged")
  @Nullable
  @Override
  public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {

    getDialog().requestWindowFeature(Window.FEATURE_NO_TITLE);

    DialogLoadingBinding inflate = DataBindingUtil.inflate(LayoutInflater.from(getContext()),
      R.layout.dialog_loading, container, false);

    if (content == null || content.isEmpty()) {
      inflate.dialogLoadingContent.setVisibility(View.GONE);
//            ConstraintLayout.LayoutParams layoutParams = new ConstraintLayout.LayoutParams(0, 0);
//            inflate.dialogLoadingImg.setLayoutParams();
    }

    inflate.setLoadingdialog(this);

    Glide.with(this).load(R.raw.loading).into(inflate.dialogLoadingImg);

    disposable =   Observable.timer(TIMEOUT, TimeUnit.SECONDS)
      .subscribe(new Consumer<Long>() {
        @Override
        public void accept(Long aLong) throws Exception {
          dismiss();
        }
      });

    return inflate.getRoot();
  }

  @Override
  public void onStart() {
    super.onStart();

    //set dialog window
    Dialog dialog = getDialog();
    assert dialog != null;
    dialog.getWindow().getDecorView().setPadding(0, 0, 0, 0);
    dialog.getWindow().setGravity(Gravity.CENTER);
    WindowManager.LayoutParams attributes = dialog.getWindow().getAttributes();
    attributes.width = WindowManager.LayoutParams.MATCH_PARENT;
    attributes.height = WindowManager.LayoutParams.WRAP_CONTENT;
    dialog.getWindow().setAttributes(attributes);

    getDialog().setCancelable(false);
  }


  @Override
  public void dismiss() {
    if(disposable!=null){
      disposable.dispose();
      disposable=null;
    }
    if (getFragmentManager()==null){
      //   Log.w(TAG, "dismiss: "+this+" not associated with a fragment manager." );
    }else {
      super.dismiss();
    }

  }
}

