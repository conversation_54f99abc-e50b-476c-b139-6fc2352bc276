package com.chervon.libBase.model;

import java.io.Serializable;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleDeviceManage.ui.state
 * @ClassName: DeviceListUistate
 * @Description: 类描述
 * @Author: name
 * @CreateDate: 2022/5/17 下午6:18
 * @UpdateUser: 更新者
 * @UpdateDate: 2022/5/17 下午6:18
 * @UpdateRemark: 更新说明
 * @Version: 1.0
 */
public class DeviceStatus implements Serializable {
  /**
   * 设备状态: 0 停用 1 启用
   */
  private int deviceStatus;
  /**
   * 设备多码状态： 0 废弃 1 启用
   */
  private int deviceCodeStatus;
  /**
   * 设备在线状态： 0 离线 1 在线
   */
  private int isOnline;;

  public int getDeviceStatus() {
    return deviceStatus;
  }

  public void setDeviceStatus(int deviceStatus) {
    this.deviceStatus = deviceStatus;
  }

  public int getDeviceCodeStatus() {
    return deviceCodeStatus;
  }

  public void setDeviceCodeStatus(int deviceCodeStatus) {
    this.deviceCodeStatus = deviceCodeStatus;
  }

  public int getIsOnline() {
    return isOnline;
  }

  public void setIsOnline(int isOnline) {
    this.isOnline = isOnline;
  }
}
