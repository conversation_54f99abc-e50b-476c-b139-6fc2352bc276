package com.chervon.libBase.ui.dialog;

import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libBase.utils.Utils.sendDurationTrace;
import static com.chervon.libBase.utils.Utils.sendExposure;

import android.annotation.SuppressLint;
import android.app.Application;
import android.app.Dialog;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;

import com.blankj.utilcode.util.FragmentUtils;
import com.blankj.utilcode.util.LogUtils;
import com.chervon.libBase.BaseApplication;
import com.chervon.libBase.R;
import com.chervon.libBase.databinding.DialogConfirmAlertBinding;


import io.reactivex.functions.Consumer;

/**
 * @ProjectName: app
 * @Package: com.chervon.libBase.ui.dialog
 * @ClassName: ConfirmAlertDialog
 * @Description: 确认对话框
 * @Author: LangMeng
 * @CreateDate: 2022/6/22 11:00
 * @UpdateUser: LangMeng
 * @UpdateDate: 2022/6/22 11:00
 * @UpdateRemark: new class
 * @Version: 1.0
 */
public class ConfirmAlertDialog extends DialogFragment {
  public static String mouduleId;
  public static String pageId;
  public static String pageResouce;
  public static String stayEleId;
  public static String doButtoneleid;
  public static String cancelButtoneleid;
  public long enterTime;


  //content text
  public String content;
  //left btn text
  public String lText;
  //right btn text
  public String rText;
  public int color = 0;
  //data observer
  private Consumer<Boolean> consumer;

  private ConfirmAlertDialog() {
  }

  /**
   * @param [fragmentManager 用于显示dialog,conten 内容文本, lText 左按钮文本, rText 右按钮文本, callback 回调接口]
   * @return void
   * @method show
   * @description show dialog and get update info.
   * @date: 2022/6/22 11:36
   * @author: LangMeng
   */
  public static void show(@NonNull FragmentManager fragmentManager, @NonNull String content, @NonNull String lText, @NonNull String rText, Consumer<Boolean> callback) {

    Fragment fragment = FragmentUtils.findFragment(fragmentManager, ConfirmAlertDialog.class.getName());
    if (fragment == null) {
      ConfirmAlertDialog confirmAlertDialog = new ConfirmAlertDialog();
      confirmAlertDialog.setData(content, lText, rText, 0, callback);
      confirmAlertDialog.show(fragmentManager, ConfirmAlertDialog.class.getName());
    }
  }

  /**
   * @param [fragmentManager 用于显示dialog,conten 内容文本, lText 左按钮文本, rText 右按钮文本,rbtColor 右按钮颜色 callback 回调接口]
   * @return void
   * @method show
   * @description show dialog and get update info.
   * @date: 2022/6/22 11:36
   * @author: LangMeng
   */
  public static void show(@NonNull FragmentManager fragmentManager, @NonNull String content, @NonNull String lText, @NonNull String rText, @NonNull int rbtColor, Consumer<Boolean> callback) {
    Fragment fragment = FragmentUtils.findFragment(fragmentManager, ConfirmAlertDialog.class.getName());
    if (fragment == null) {
      ConfirmAlertDialog confirmAlertDialog = new ConfirmAlertDialog();
      confirmAlertDialog.setData(content, lText, rText, rbtColor, callback);
      confirmAlertDialog.show(fragmentManager, ConfirmAlertDialog.class.getName());
    }
  }

  public static ConfirmAlertDialog showAndReturn(@NonNull FragmentManager fragmentManager, @NonNull String content, @NonNull String lText, @NonNull String rText, @NonNull int rbtColor, Consumer<Boolean> callback) {
    Fragment fragment = FragmentUtils.findFragment(fragmentManager, ConfirmAlertDialog.class.getName());

    ConfirmAlertDialog confirmAlertDialog = new ConfirmAlertDialog();
    confirmAlertDialog.setData(content, lText, rText, rbtColor, callback);
    confirmAlertDialog.show(fragmentManager, ConfirmAlertDialog.class.getName());
    return confirmAlertDialog;

  }


  @Override
  public void onCreate(@Nullable Bundle savedInstanceState) {
    super.onCreate(savedInstanceState);
    setStyle(DialogFragment.STYLE_NO_FRAME, R.style.NoBackgroundDialog);
  }

  @SuppressLint("NotifyDataSetChanged")
  @Nullable
  @Override
  public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
    getDialog().requestWindowFeature(Window.FEATURE_NO_TITLE);
    DialogConfirmAlertBinding inflate = DataBindingUtil.inflate(LayoutInflater.from(getContext()),
      R.layout.dialog_confirm_alert, container, false);
    inflate.setConfirmAlertDialog(this);
    return inflate.getRoot();
  }

  @Override
  public void onStart() {
    super.onStart();
    //set dialog window
    Dialog dialog = getDialog();
    assert dialog != null;
    dialog.getWindow().getDecorView().setPadding(0, 0, 0, 0);
    dialog.getWindow().setGravity(Gravity.CENTER);
    WindowManager.LayoutParams attributes = dialog.getWindow().getAttributes();
    attributes.width = WindowManager.LayoutParams.MATCH_PARENT;
    attributes.height = WindowManager.LayoutParams.WRAP_CONTENT;
    dialog.getWindow().setAttributes(attributes);

    getDialog().setCancelable(false);
    enterTime = System.currentTimeMillis();
    sendExposure(this.getContext(), mouduleId, pageId, pageResouce);
  }


  /**
   * @param [conten 内容文本, lText 左按钮文本, rText 右按钮文本, callback 回调接口]
   * @return void
   * @method setData
   * @description 设置弹窗数据
   * @date: 2022/6/22 11:35
   * @author: LangMeng
   */
  public void setData(@NonNull String conten, @NonNull String lText, @NonNull String rText, @NonNull int color, Consumer<Boolean> callback) {
    this.content = conten;
    this.lText = lText;
    this.rText = rText;
    this.color = color;
    this.consumer = callback;
  }

  /**
   * @return void
   * @method lCLick
   * @description 左按钮点击事件
   * @date: 2022/6/22 11:39
   * @author: LangMeng
   */
  public void lCLick() {
    if (consumer != null) {
      try {
        consumer.accept(false);
        sendNextBottonClick(cancelButtoneleid);
        getDialog().hide();
        dismiss();
      } catch (Exception e) {
        e.printStackTrace();
      }
    }
  }

  /**
   * @return void
   * @method rCLick
   * @description 右按钮点击事件
   * @date: 2022/6/22 11:39
   * @author: LangMeng
   */
  public void rCLick() {
    if (consumer != null) {
      try {
        consumer.accept(true);
        sendNextBottonClick(doButtoneleid);
        getDialog().hide();
        dismiss();
      } catch (Exception e) {
        e.printStackTrace();
      }
    }
  }

  public static void initTrace(Application application, String stayEleIda, String pageIda, String mouduleIda, String pageResoucea, String cancelButtoneleida, String doButtoneleida) {
    stayEleId = stayEleIda;
    pageId = pageIda;
    mouduleId = mouduleIda;
    pageResouce = pageResoucea;
    cancelButtoneleid = cancelButtoneleida;
    doButtoneleid = doButtoneleida;
    initPageResouce(application);
  }

  public static void initTrace(String stayEleIda, String pageIda, String mouduleIda, String pageResoucea, String cancelButtoneleida, String doButtoneleida) {
    stayEleId = stayEleIda;
    pageId = pageIda;
    mouduleId = mouduleIda;
    pageResouce = pageResoucea;
    cancelButtoneleid = cancelButtoneleida;
    doButtoneleid = doButtoneleida;
  }

  @Override
  public void onPause() {
    super.onPause();
    sendDurationTrace(this.getContext(), mouduleId, pageId, pageResouce, "", stayEleId, System.currentTimeMillis() - enterTime);
  }

  @Override
  public void dismiss() {
    try {
      if (pageId != null) {
        FragmentActivity fragmentActivity = getActivity();
        // 触发背景 activity 重新 onresume
        Intent intent = new Intent(getActivity(), fragmentActivity.getClass());
        intent.setFlags(Intent.FLAG_ACTIVITY_REORDER_TO_FRONT);
        startActivity(intent);
      }
    } catch (NullPointerException e) {
      LogUtils.i("dismiss", "dialog dismiss, resume activity failed, null pointer exception occurs");
    }

    if (getFragmentManager() == null) {
      //   Log.w(TAG, "dismiss: "+this+" not associated with a fragment manager." );
    } else {
      super.dismiss();
    }


  }


  public void sendNextBottonClick(String eleid) {
    sendClickTrace(this.getContext(), mouduleId, pageId, pageResouce, eleid, "1");
  }

  private static void initPageResouce(Application application) {
    if (!TextUtils.isEmpty(pageId)) {
      pageResouce = ((BaseApplication) application).getCurrentPageResouce();
      if (!pageResouce.endsWith(pageId)) {
        pageResouce = pageResouce + "_" + pageId;
        ((BaseApplication) application).setCurrentPageResouce(pageResouce);
      }
    }
    LogUtils.d("PageResouce" + pageResouce);
  }
}
