package com.chervon.libBase;


import android.app.Application;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.database.ContentObserver;
import android.location.Location;
import android.location.LocationManager;
import android.os.Build;
import android.os.Handler;
import android.provider.Settings;
import android.text.TextUtils;
import android.view.Gravity;

import androidx.annotation.NonNull;
import androidx.lifecycle.MutableLiveData;

import com.alibaba.android.arouter.launcher.ARouter;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.chervon.libBase.broadcast.BleStateChangeEvent;
import com.chervon.libBase.broadcast.BluetoothStateBroadcastReceive;
import com.chervon.libBase.model.DeviceRegistStatusModel;
import com.chervon.libBase.model.OTACheckBean;
import com.chervon.libBase.utils.LocationUtils;
import com.chervon.libBase.utils.VideoCacheManager2;
import com.chervon.libRouter.RouterConstants;
import com.clj.fastble.BleManager;
import com.google.android.gms.location.FusedLocationProviderClient;
import com.google.android.gms.location.LocationServices;
import com.google.android.gms.tasks.OnCompleteListener;
import com.google.android.gms.tasks.OnFailureListener;
import com.google.android.gms.tasks.OnSuccessListener;
import com.google.android.gms.tasks.Task;
import com.kunminx.architecture.domain.message.MutableResult;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.HashMap;

import io.reactivex.functions.Consumer;
import io.reactivex.plugins.RxJavaPlugins;
import me.jessyan.autosize.AutoSizeConfig;
import me.jessyan.autosize.unit.Subunits;

/**
 * @ProjectName: app
 * @Package: com.chervon.libBase
 * @ClassName: BaseApplication
 * @Description: the base  Application class of app
 * @Author: wangheng
 * @CreateDate: 2022/4/19 下午7:02
 * @UpdateUser: wuxd
 * @UpdateDate: 2024/8/8
 * @UpdateRemark: 新增当前操作设备deviceId
 * @Version: 1.0
 */
public class BaseApplication extends Application {
    private static final String TAG = "BaseApplication";
    protected android.os.Handler mHandler;
    private static BaseApplication sInstance;
    private OTACheckBean otaCheckBean;
    private long appStartTime;
    private String currentPage;
    private boolean isLoginSuccess;
    public static MutableLiveData<DeviceRegistStatusModel> registLiveData = new MutableLiveData<>();
    private static boolean isShowDialogMessage;

    //蓝牙打开
    public static final int BLE_ON = 1;
    //蓝牙关闭
    public static final int BLE_OFF = 0;

    public BluetoothStateBroadcastReceive mReceiver;

    //记录当前GPS的状态
    private Boolean lastGPSState;
    private LocationManager mLocationManager;
    public static MutableResult<Boolean> gpsResult = new MutableResult<>();
    private String currentDeviceId = "";
    //MainActivity显示Fragment的默认值
    public static int showCurrentFragmentLayoutId = -1;

    public static boolean networkAvailable = false;

    public static VideoCacheManager2 videoCacheManager;
    public  HashMap<String,Long> videoPositionManager = new HashMap<>();

    private   boolean isUpgradeComplete = false;

    private final ContentObserver mGpsMonitor = new ContentObserver(null) {
        @Override
        public void onChange(boolean selfChange) {
            super.onChange(selfChange);
            boolean enabled = mLocationManager.isProviderEnabled(LocationManager.GPS_PROVIDER);
            if (lastGPSState != enabled) {
                gpsResult.postValue(selfChange);
                lastGPSState = enabled;
            }
        }
    };

    private void registerLocationObserver() {
        //设置GPS初始值
        if (LocationUtils.isGpsEnabled()) {
            lastGPSState = true;
        } else {
            lastGPSState = false;
        }
        mLocationManager = (LocationManager) getSystemService(LOCATION_SERVICE);
        getContentResolver().registerContentObserver(Settings.Secure.getUriFor(Settings.System.LOCATION_PROVIDERS_ALLOWED),
                false, mGpsMonitor);
    }

    public static void setShowDialogMessage(boolean b) {
        isShowDialogMessage = b;
    }

    public static boolean isShowDialogMessage() {
        return isShowDialogMessage;
    }

    public void setLoginSuccess(boolean loginSuccess) {
        isLoginSuccess = loginSuccess;
    }


    public static BaseApplication getInstance() {
        return sInstance;
    }
    public void setCurrentDeviceId(String currentDeviceId) {
      this.currentDeviceId = currentDeviceId;
    }

    public boolean isCurrentDevice(String deviceId) {
      return !TextUtils.isEmpty(currentDeviceId) && currentDeviceId.equalsIgnoreCase(deviceId);
    }

    @Override
    public void onCreate() {
        super.onCreate();
        isShowDialogMessage = false;
        initLog();
        initARouter();
        mHandler = new Handler();
        appStartTime = System.currentTimeMillis();
        mHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                new Thread(new Runnable() {
                    @Override
                    public void run() {
                        initToast();
                        initFastBle();
                    }
                }).start();
            }
        }, 700);
        initAutoSize();
        sInstance = this;
        initRxJava();
        registerLocationObserver();
        registerBluetoothReceiver(getInstance());
        initMap();

        videoCacheManager = new VideoCacheManager2(this);
        videoPositionManager.clear();
//        videoCacheManager = new VideoCacheManager2(this);
//        videoPositionManager = new VideoPositionManager(this);
//        videoPositionManager.clearAllPositions();
    }


    public void destroyVideoManager(){
        try {
            videoPositionManager.clear();
        }catch (Exception e){
            LogUtils.e(TAG,"destroyVideoManager is error +"+e.getMessage());
        }

    }

    private void initRxJava() {
        RxJavaPlugins.setErrorHandler(new Consumer<Throwable>() {
            @Override
            public void accept(Throwable throwable) throws Exception {
                throwable.printStackTrace();//这里处理所有的Rxjava异常
            }
        });
    }


    private void initFastBle() {
        final int MTU_MAX = 247;
        BleManager.getInstance().init(this);
        BleManager.getInstance().enableLog(BuildConfig.DEBUG ? true : false)
                .setReConnectCount(3, 5000)
                .setSplitWriteNum(MTU_MAX)
                .setConnectOverTime(15000)
                .setOperateTimeout(5000);
    }


    private void initAutoSize() {
        AutoSizeConfig.getInstance().setUseDeviceSize(false);
        //Support fragment custom parameters
        AutoSizeConfig.getInstance().setCustomFragment(true);
        AutoSizeConfig.getInstance().setBaseOnWidth(true);
        AutoSizeConfig.getInstance().getUnitsManager().setSupportDP(false);
        AutoSizeConfig.getInstance().getUnitsManager().setSupportSP(false);
        //Use mm sub unit adaptation
        AutoSizeConfig.getInstance().getUnitsManager().setSupportSubunits(Subunits.MM);
    }


    private void initToast() {
        ToastUtils.getDefaultMaker().setGravity(Gravity.CENTER, 0, 0);
        ToastUtils.getDefaultMaker().setBgColor(getResources().getColor(R.color.black));
        ToastUtils.getDefaultMaker().setTextColor(getResources().getColor(R.color.white));
    }

    private void initARouter() {
        ARouter.init(this);
    }

    private void initLog() {
        LogUtils.getConfig()
                .setLogSwitch(BuildConfig.DEBUG)
                .setConsoleSwitch(BuildConfig.DEBUG)
                .setGlobalTag(null)
                .setLogHeadSwitch(true)
                .setLog2FileSwitch(false)
                .setDir("")
                .setFilePrefix("")
                .setBorderSwitch(true)
                .setStackDeep(1);

    }


    @Override
    public void onTerminate() {
        super.onTerminate();
        ARouter.getInstance().destroy();
    }


    public void setOTACheckBean(OTACheckBean otaCheckBean) {
        this.otaCheckBean = otaCheckBean;
    }

    public OTACheckBean getOtaCheckBean() {
        return otaCheckBean;
    }

    public long getAppStartTime() {
        return appStartTime;
    }

    public String getCurrentPageResouce() {
        return currentPage;
    }

    public void setCurrentPageResouce(String str) {
        currentPage = str;
    }


    public boolean getIsLoginSuccess() {
        return isLoginSuccess;
    }


    public static MutableResult<Integer> bleResult = new MutableResult<>();

    public void registerBluetoothReceiver(Context context) {


        if (mReceiver == null) {
            mReceiver = new BluetoothStateBroadcastReceive();
            EventBus.getDefault().register(this);
        }
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(BluetoothAdapter.ACTION_STATE_CHANGED);
        intentFilter.addAction(BluetoothDevice.ACTION_ACL_CONNECTED);
        intentFilter.addAction(BluetoothDevice.ACTION_ACL_DISCONNECTED);
        intentFilter.addAction("android.bluetooth.BluetoothAdapter.STATE_OFF");
        intentFilter.addAction("android.bluetooth.BluetoothAdapter.STATE_ON");
        //适配Android14
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            context.registerReceiver(mReceiver, intentFilter, RECEIVER_NOT_EXPORTED);
        } else {
            context.registerReceiver(mReceiver, intentFilter);
        }
    }


    public void unregisterBluetoothReceiver(Context context) {
        if (mReceiver != null) {
            context.unregisterReceiver(mReceiver);
            mReceiver = null;
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    public void BleStateChange(BleStateChangeEvent event) {
        bleResult.postValue(event.state);
    }




    private FusedLocationProviderClient fusedLocationClient;
    public boolean locationPermission = false;
    public String latitude = "";
    public String longitude = "";
    public long startMapTime = -1;
    private final long ONE_MIN = 60 * 1000;

    public void initMap() {
        fusedLocationClient = LocationServices.getFusedLocationProviderClient(this);
        boolean permission = (PackageManager.PERMISSION_GRANTED == getPackageManager().checkPermission(android.Manifest.permission.ACCESS_FINE_LOCATION, getPackageName()));
        if (permission) {
            locationPermission = true;
        } else {
            locationPermission = false;
            return;
        }

        if (System.currentTimeMillis() - startMapTime < ONE_MIN && startMapTime != -1) {
            return;
        }
        startMapTime = System.currentTimeMillis();
        // 获取位置
        fusedLocationClient.getLastLocation().addOnCompleteListener(new OnCompleteListener<Location>() {
            @Override
            public void onComplete(@NonNull Task<Location> task) {
                LogUtils.i(TAG, "fusedLocationClient-->onComplete");
            }
        }).addOnSuccessListener(new OnSuccessListener<Location>() {
            @Override
            public void onSuccess(Location location) {
                LogUtils.i(TAG, "fusedLocationClient-->onSuccess");
                if (null != location) {
                    LogUtils.i(TAG, "fusedLocationClient-->getLatitude--" + location.getLatitude() + "");
                    LogUtils.i(TAG, "fusedLocationClient-->getLongitude--" + location.getLongitude() + "");
                    latitude = location.getLatitude() + "";
                    longitude = location.getLongitude() + "";
                } else {
                    LogUtils.i(TAG, "fusedLocationClient-->location is null");
                }
            }
        }).addOnFailureListener(new OnFailureListener() {
            @Override
            public void onFailure(@NonNull Exception e) {
                LogUtils.i(TAG, "fusedLocationClient-->onFailure");
            }
        });
    }


    /**
     * OTA升级相关状态处理
     * @return
     */
    public boolean isUpgradeComplete() {
        return isUpgradeComplete;
    }

    public void setUpgradeComplete(boolean upgradeComplete) {
        isUpgradeComplete = upgradeComplete;
    }
    public void resetUpgradeComplete(){
        isUpgradeComplete = false;
    }
}
