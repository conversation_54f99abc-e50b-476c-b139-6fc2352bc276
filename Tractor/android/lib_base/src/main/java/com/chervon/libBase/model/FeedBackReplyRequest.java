package com.chervon.libBase.model;

import com.fasterxml.jackson.annotation.JsonIgnore;

/**
 * @ProjectName: app
 * @Package: com.chervon.libNetwork.http.model.request
 * @ClassName: EditInfoRequest
 * @Description: 修改用户信息接口请求模型
 * @Author: LangMeng
 * @CreateDate: 2022/6/23 15:33
 * @UpdateUser: LangMeng
 * @UpdateDate: 2022/6/23 15:33
 * @UpdateRemark: new class
 * @Version: 1.0
 */
public class FeedBackReplyRequest {
  private String feedbackId	;
  private String  replyContent;
  private String  replyId ;
  private String[]  replyPictures;
  @JsonIgnore
  private boolean originFeedBack;


  public String getFeedbackId() {
    return feedbackId;
  }

  public void setFeedbackId(String feedbackId) {
    this.feedbackId = feedbackId;
  }

  public String getReplyContent() {
    return replyContent;
  }

  public void setReplyContent(String replyContent) {
    this.replyContent = replyContent;
  }

  public String getReplyId() {
    return replyId;
  }

  public void setReplyId(String replyId) {
    this.replyId = replyId;
  }

  public String[] getReplyPictures() {
    return replyPictures;
  }

  public void setReplyPictures(String[] replyPictures) {
    this.replyPictures = replyPictures;
  }

  public boolean isOriginFeedBack() {
    return originFeedBack;
  }

  public void setOriginFeedBack(boolean originFeedBack) {
    this.originFeedBack = originFeedBack;
  }
}
