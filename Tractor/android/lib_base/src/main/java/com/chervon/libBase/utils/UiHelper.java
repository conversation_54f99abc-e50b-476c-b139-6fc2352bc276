package com.chervon.libBase.utils;
import android.graphics.Color;
import android.os.Build;
import android.text.InputType;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.EditText;
import android.widget.ImageButton;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleOobe.utils
 * @ClassName: UiHelper
 * @Description: 类描述
 * @Author: name
 * @CreateDate: 2022/4/25 下午7:25
 * @UpdateUser: 更新者
 * @UpdateDate: 2022/4/25 下午7:25
 * @UpdateRemark: 更新说明
 * @Version: 1.0
 */
public class UiHelper {
    public final static int PASSWORD_HIDE = 0;
    public final static int PASSWORD_SHOW = 1;
    public static void switchPasswordVisibility(ImageButton ibPassword , EditText etPassword, View view) {
        Integer isHide = (Integer) view.getTag();
        if (isHide == null || isHide == 0) {
            ibPassword.setImageResource(com.chervon.libBase.R.drawable.ic_password_show);
            view.setTag(PASSWORD_SHOW);
            etPassword.setInputType(InputType.TYPE_TEXT_VARIATION_VISIBLE_PASSWORD);

        } else {
            ibPassword.setImageResource(com.chervon.libBase.R.drawable.ic_password_hide);
            view.setTag(PASSWORD_HIDE);
            etPassword.setInputType(InputType.TYPE_CLASS_TEXT | InputType.TYPE_TEXT_VARIATION_PASSWORD);
        }

        if (null!=etPassword){
          if (etPassword.getText().toString().length()>0){
            etPassword.setSelection(etPassword.getText().length());
          }
        }



    }


    public static void hideSystemUI(Window window) {
        // Enables regular immersive mode.
        // For "lean back" mode, remove SYSTEM_UI_FLAG_IMMERSIVE.
        // Or for "sticky immersive," replace it with SYSTEM_UI_FLAG_IMMERSIVE_STICKY
        View decorView = window.getDecorView();
        decorView.setSystemUiVisibility(
                View.SYSTEM_UI_FLAG_IMMERSIVE
                        // Set the content to appear under the system bars so that the
                        // content doesn't resize when the system bars hide and show.
                        | View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                        // | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                        | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                // Hide the nav bar and status bar
                //   | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                //     | View.SYSTEM_UI_FLAG_FULLSCREEN
        );
       //给状态栏设置颜色。我设置透明。
        window.setStatusBarColor(Color.TRANSPARENT);
        window.setNavigationBarColor(Color.TRANSPARENT);

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            //实现状态栏图标和文字颜色为暗色
            window.getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN | View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR);
        }

    }




    // Shows the system bars by removing all the flags
    // except for the ones that make the content appear under the system bars.
    public static void showSystemUI(Window window) {
//        View decorView = window.getDecorView();
//        decorView.setSystemUiVisibility(
//                View.SYSTEM_UI_FLAG_LAYOUT_STABLE
//                        | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
//                        | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN);



        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {



            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS | WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION);

            window.getDecorView().setSystemUiVisibility( View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION | View.SYSTEM_UI_FLAG_LAYOUT_STABLE);

            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
//给状态栏设置颜色。我设置透明。
            int statusBarColor;
            window.setStatusBarColor(Color.TRANSPARENT);
            window.setNavigationBarColor(Color.TRANSPARENT);
            window.getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR);
        }
    }
  public static void setSystemUIColor(Window window) {

    View decorView = window.getDecorView();
    decorView.setSystemUiVisibility(
      View.SYSTEM_UI_FLAG_IMMERSIVE
        | View.SYSTEM_UI_FLAG_LAYOUT_STABLE);
    //给状态栏设置颜色。我设置透明。
    window.setStatusBarColor(Color.parseColor("#FFF7F7F7"));
   // window.setNavigationBarColor(Color.TRANSPARENT);

    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
        //实现状态栏图标和文字颜色为暗色
      window.getDecorView().setSystemUiVisibility( View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR);
    }

  }
}
