package com.chervon.libBase.model;

import com.chervon.libDB.entities.ProductInfo;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleConfigNet.ui.state
 * @ClassName: ProductEntry
 * @Description: 类描述
 * @Author: name
 * @CreateDate: 2022/5/26 上午11:13
 * @UpdateUser: 更新者
 * @UpdateDate: 2022/5/26 上午11:13
 * @UpdateRemark: 更新说明
 * @Version: 1.0
 */
public class ProductEntry {

    ProductInfo[]  list;

    int  pageNum;

    int pageSize;

    int  pages;

    int total;



    public ProductInfo[] getList() {
        return list;
    }

    public void setList(ProductInfo[] list) {
        this.list = list;
    }

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getPages() {
        return pages;
    }

    public void setPages(int pages) {
        this.pages = pages;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }
}
