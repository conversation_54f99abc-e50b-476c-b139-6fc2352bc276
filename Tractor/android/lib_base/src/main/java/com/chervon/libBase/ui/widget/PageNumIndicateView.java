package com.chervon.libBase.ui.widget;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.TypedArray;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;

import androidx.annotation.Nullable;
import androidx.databinding.BindingAdapter;

import com.chervon.libBase.R;


/**
 * @ProjectName: app
 * @Package: com.chervon.moudleOobe.ui.widget.supertooltip
 * @ClassName: PageNumIndicateView
 * @Description: 类描述
 * @Author: name
 * @CreateDate: 2022/6/17 下午5:05
 * @UpdateUser: 更新者
 * @UpdateDate: 2022/6/17 下午5:05
 * @UpdateRemark: 更新说明
 * @Version: 1.0
 */
public class PageNumIndicateView extends LinearLayout {
    private int leftMargin;
    private int  itemWidth;
    private int  itemHeight;
    private float  itemTextSize;

    public PageNumIndicateView(Context context) {
        this(context, null);
    }

    public PageNumIndicateView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    @SuppressLint("ResourceAsColor")
    public PageNumIndicateView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        TypedArray obtainStyledAttributes =
                getContext().obtainStyledAttributes(attrs, com.chervon.libBase.R.styleable.pageNumIndicateView);
        leftMargin = obtainStyledAttributes.getDimensionPixelSize(com.chervon.libBase.R.styleable.pageNumIndicateView_itemLeftMargin,15);
        itemWidth = obtainStyledAttributes.getDimensionPixelSize(com.chervon.libBase.R.styleable.pageNumIndicateView_itemWidth,15);
        itemHeight = obtainStyledAttributes.getDimensionPixelSize(com.chervon.libBase.R.styleable.pageNumIndicateView_itemHeight,15);
        itemTextSize=obtainStyledAttributes.getDimension(com.chervon.libBase.R.styleable.pageNumIndicateView_itemTextSize,28);
        String text= obtainStyledAttributes.getString(com.chervon.libBase.R.styleable.pageNumIndicateView_text_num);
        setText(text);

    }
    @BindingAdapter(value = "text_num", requireAll = false)
    public static void setPageIndicateText(PageNumIndicateView pageNumIndicateView, String str) {
        pageNumIndicateView.setText(str);
    }

    public void setText(String pageStr){
        if (!TextUtils.isEmpty(pageStr)) {
            this.removeAllViews();
            String[] pageNums = pageStr.split("/");
            int  currentPage=Integer.parseInt(pageNums[0]);
            int  totalPage=Integer.parseInt(pageNums[1]);
            for(int i=0;i<totalPage;i++){
                View pageItemView=  LayoutInflater.from(this.getContext()).inflate(R.layout.moudle_oobe_page_indicate_item,null);
                if(i<currentPage){
                    pageItemView.setBackgroundResource(R.color.colorButtonNormal);
                }else{
                    pageItemView.setBackgroundResource(R.color.colorGreenGray);
                }
                LayoutParams layoutParams= new  LayoutParams(itemWidth,itemHeight);
                layoutParams.leftMargin=leftMargin;
                layoutParams.gravity= Gravity.CENTER_VERTICAL;

                 addView(pageItemView,layoutParams);

            }
            PageNumView pageNumView=new PageNumView(this.getContext());
            pageNumView.setLeft(24);
            pageNumView.setText(pageStr);
            pageNumView.setTextSize(TypedValue.COMPLEX_UNIT_PX,itemTextSize);
            pageNumView.setGravity(Gravity.CENTER_VERTICAL);
            LayoutParams pageNumLayoutParams= new  LayoutParams(LayoutParams.WRAP_CONTENT,LayoutParams.MATCH_PARENT);
            pageNumLayoutParams.leftMargin=leftMargin;
            addView(pageNumView,pageNumLayoutParams);
        }
    }
}
