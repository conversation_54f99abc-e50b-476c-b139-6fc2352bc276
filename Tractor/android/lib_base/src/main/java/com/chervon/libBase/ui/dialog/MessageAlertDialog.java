package com.chervon.libBase.ui.dialog;

import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libBase.utils.Utils.sendDurationTrace;
import static com.chervon.libBase.utils.Utils.sendExposure;

import android.annotation.SuppressLint;
import android.app.Dialog;
import android.content.Intent;
import android.os.Bundle;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;

import com.blankj.utilcode.util.FragmentUtils;
import com.blankj.utilcode.util.LogUtils;
import com.chervon.libBase.R;
import com.chervon.libBase.databinding.DialogConfirmAlertBinding;
import com.chervon.libBase.databinding.DialogMessageAlertBinding;

import io.reactivex.functions.Consumer;

/**
 * @ProjectName: app
 * @Package: com.chervon.libBase.ui.dialog
 * @ClassName: MessageAlertDialog
 * @Description: 消息确认弹窗
 * @Author: langmeng
 * @CreateDate: 2022/9/5 17:07
 * @UpdateUser: langmeng
 * @UpdateDate: 2022/9/5 17:07
 * @UpdateRemark: new class
 * @Version: 1.0
 */
public class MessageAlertDialog extends DialogFragment {
  public static String mouduleId;
  public static String pageId;
  public static String pageResouce;
  public static String stayEleId;
  public static String doButtoneleid;
  public static String cancelButtoneleid;
  public long enterTime;

  //content text
  public String content;
  //title text
  public String title;
  //bt text
  public String btLeftText;
  //bt text
  public String btRightText;
  //data observer
  private Consumer<Boolean> consumer;
  public static MessageAlertDialog messageAlertDialog;

  private MessageAlertDialog() {
  }

  /**
   * @param fragmentManager 用于显示dialog,conten 内容文本, title 标题文本, btText 按钮文本, callback 回调接口]
   * @return void
   * @method show
   * @description show dialog.
   * @date: 2022/6/22 11:36
   * @author: LangMeng
   */
  public static void show(@NonNull FragmentManager fragmentManager, @NonNull String title, @NonNull String content,
                          @NonNull String leftText,
                          @NonNull String rightText, Consumer<Boolean> callback) {

//        Fragment fragment = FragmentUtils.findFragment(fragmentManager, ConfirmAlertDialog.class.getName());
//        if (fragment == null) {
    if (messageAlertDialog == null) {
      messageAlertDialog = new MessageAlertDialog();
    } else {
      messageAlertDialog.dismiss();
      messageAlertDialog = null;
      messageAlertDialog = new MessageAlertDialog();
    }

    messageAlertDialog.setData(content, title,leftText, rightText, callback);
    messageAlertDialog.show(fragmentManager, ConfirmAlertDialog.class.getName());
//        }
  }

  @Override
  public void onCreate(@Nullable Bundle savedInstanceState) {
    super.onCreate(savedInstanceState);
    setStyle(DialogFragment.STYLE_NO_FRAME, R.style.NoBackgroundDialog);
  }

  @SuppressLint("NotifyDataSetChanged")
  @Nullable
  @Override
  public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {

    getDialog().requestWindowFeature(Window.FEATURE_NO_TITLE);

    DialogMessageAlertBinding inflate = DataBindingUtil.inflate(LayoutInflater.from(getContext()),
      R.layout.dialog_message_alert, container, false);

    inflate.setMessageAlertDialog(this);

    return inflate.getRoot();
  }

  @Override
  public void onStart() {
    super.onStart();
    //set dialog window
    Dialog dialog = getDialog();
    assert dialog != null;
    dialog.getWindow().getDecorView().setPadding(0, 0, 0, 0);
    dialog.getWindow().setGravity(Gravity.CENTER);
    WindowManager.LayoutParams attributes = dialog.getWindow().getAttributes();
    attributes.width = WindowManager.LayoutParams.MATCH_PARENT;
    attributes.height = WindowManager.LayoutParams.WRAP_CONTENT;
    dialog.getWindow().setAttributes(attributes);

    getDialog().setCancelable(false);
    sendExposure(this.getContext(), mouduleId, pageId, pageResouce);
  }


  /**
   * @param content 内容文本, title 标题文本, btText 按钮文本, callback 回调接口]
   * @return void
   * @method setData
   * @description 设置弹窗数据
   * @date: 2022/6/22 11:35
   * @author: LangMeng
   */
  public void setData(@NonNull String content, @NonNull String title,@NonNull String leftText, @NonNull String rightText, Consumer<Boolean> callback) {
    this.content = content;
    this.title = title;
    this.btLeftText = leftText;
    this.btRightText = rightText;
    this.consumer = callback;
  }

  /**
   * @return void
   * @method close
   * @description 关闭dialog
   * @date: 2022/9/5 17:32
   * @author: langmeng
   */
  public void close() {
    if (consumer != null) {
      try {
        sendNextBottonClick(cancelButtoneleid);
        consumer.accept(false);
        dismiss();
      } catch (Exception e) {
        e.printStackTrace();
      }
    }
  }

  /**
   * @return void
   * @method next
   * @description 点击按钮
   * @date: 2022/9/5 17:32
   * @author: langmeng
   */
  public void next() {
    if (consumer != null) {
      try {
        sendNextBottonClick(doButtoneleid);
        consumer.accept(true);
        dismiss();
      } catch (Exception e) {
        e.printStackTrace();
      }
    }
  }

  @Override
  public void onPause() {
    super.onPause();
    sendDurationTrace(this.getContext(), mouduleId, pageId, pageResouce, "", stayEleId, System.currentTimeMillis() - enterTime);
  }


  @Override
  public void dismiss() {
    if (getFragmentManager() == null) {
      //   Log.w(TAG, "dismiss: "+this+" not associated with a fragment manager." );
    } else {
      super.dismiss();
    }
    messageAlertDialog = null;
  }


  public void sendNextBottonClick(String eleid) {
    sendClickTrace(this.getContext(), mouduleId, pageId, pageResouce, eleid, "1");
  }

  public static void initTrace(String stayEleIda, String pageIda, String mouduleIda, String pageResoucea, String cancelButtoneleida, String doButtoneleida) {
    stayEleId = stayEleIda;
    pageId = pageIda;
    mouduleId = mouduleIda;
    pageResouce = pageResoucea;
    cancelButtoneleid = cancelButtoneleida;
    doButtoneleid = doButtoneleida;
  }
}
