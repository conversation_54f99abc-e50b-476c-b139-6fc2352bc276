package com.chervon.libBase.ui.view;

import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewpager.widget.ViewPager;
/**
 * @ProjectName: app
 * @Package: com.chervon.libBase.ui.view
 * @ClassName: NoSlideViewPager
 * @Description:  the ViewPager can't slide
 * @Author: LangMeng
 * @CreateDate: 2022/4/22 19:34
 * @UpdateUser: LangMeng
 * @UpdateDate: 2022/4/22 19:34
 * @UpdateRemark: new class
 * @Version: 1.0
 */
public class NoSlideViewPager extends ViewPager {
    public NoSlideViewPager(@NonNull Context context) {
        super(context);
    }

    public NoSlideViewPager(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent ev) {
        return false;
    }

    @Override
    public boolean onTouchEvent(MotionEvent ev) {
        return false;
    }
}
