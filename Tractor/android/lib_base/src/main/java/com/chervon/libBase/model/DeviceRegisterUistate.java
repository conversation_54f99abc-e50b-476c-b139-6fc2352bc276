package com.chervon.libBase.model;

import androidx.databinding.BaseObservable;
import androidx.databinding.Bindable;

import com.chervon.libBase.BR;
import com.chervon.libDB.entities.DeviceInfo;


import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleDeviceManage.ui.state
 * @ClassName: HomeUiState
 * @Description: UiSate of Home page
 * @Author: wangheng
 * @CreateDate: 2022/4/21 下午7:02
 * @UpdateUser: wuxd
 * @UpdateDate: 2024/6/18
 * @UpdateRemark: 添加欧洲字段
 * @Version: 1.1
 */
public class DeviceRegisterUistate extends BaseObservable implements Serializable {
  int ifCheckedWarranty;
  private String deviceid;
  private String receipt;
  private int receiptType;
  private String address;
  private String date;
  private String purpose;
  private int purposeType;
  private String addressType;
  private boolean isRegisteSuccessed;
  private String sn;
  private String showSN;
  //是否进行了SN校验
  private boolean checkSnHasRegistered;
  //是否显示SN已经被注册Tips
  private boolean showSNRegisteredTips;

  public String registeredWarnTips;
  @Bindable
  private String name;
  private String modelNo;
  @Bindable
  private String showModelNo;
  private String photoUrl;
  private String message;
  private int from;
  private QuestionPageData questionPageData;
  private int state;
  private List<String> url = new ArrayList<>();
  private boolean hasQuestionPage;
  //用于检查是否查询过问卷调查---注册更多使用
  private boolean hasQuestionChecked;
  private boolean isRegistMore;
  private String purchasePlaceOther;
  private boolean purchasePlaceOtherShow;

  //是否显示这个文案 “You will need to provide a valid receipt to enjoy the warranty extension from 3 to 5 years.”
  private boolean showlostReceipt;
  private boolean showQuestionPage;
  private boolean showGiftResidential;

  private boolean cbAnswer;
  private boolean showEnterSN;
  private DeviceInfo deviceInfo;
  private DeviceRegisterRes deviceRegisterRes;
  /**
   * 欧洲CRM添加字段
   * snValidateStatus
   */
  private DeviceSnStatus snValidateStatus = new DeviceSnStatus();
  /**
   * 欧洲CRM添加字段
   * hasKit: 是否kit 默认为true
   */
  @Bindable
  private boolean hasKit = true;
  /**
   * 欧洲CRM添加字段
   * kitSN: kit SN 最多3个
   */
  @Bindable
  private DeviceSnStatus[] kitSN = new DeviceSnStatus[]{new DeviceSnStatus(),new DeviceSnStatus(),new DeviceSnStatus()};


  public DeviceInfo getDeviceInfo() {
    return deviceInfo;
  }

  public boolean isPurchasePlaceOtherShow() {
    return purchasePlaceOtherShow;
  }

  public void setPurchasePlaceOtherShow(boolean purchasePlaceOtherShow) {
    this.purchasePlaceOtherShow = purchasePlaceOtherShow;
  }

  public void setDeviceInfo(DeviceInfo deviceInfo) {
    this.deviceInfo = deviceInfo;
  }

  public String getShowSN() {
    return showSN;
  }

  public void setShowSN(String showSN) {
    this.showSN = showSN;
  }

  public String getShowModelNo() {
    return showModelNo;
  }

  public void setShowModelNo(String showModelNo) {
    this.showModelNo = showModelNo;
    notifyPropertyChanged(BR.showModelNo);
  }

  public String getPurchasePlaceOther() {
    return purchasePlaceOther;
  }

  public void setPurchasePlaceOther(String purchasePlaceOther) {
    this.purchasePlaceOther = purchasePlaceOther;
  }

  public boolean isHasQuestionPage() {
    return hasQuestionPage;
  }

  public void setHasQuestionPage(boolean hasQuestionPage) {
    this.hasQuestionPage = hasQuestionPage;
  }



  private List<String> recipKey;

  public int getFrom() {
    return from;
  }

  public void setFrom(int from) {
    this.from = from;
  }

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public String getModelNo() {
    return modelNo;
  }

  public void setModelNo(String modelNo) {
    this.modelNo = modelNo;
  }

  public String getReceipt() {
    return receipt;
  }

  public void setReceipt(String receipt) {
    this.receipt = receipt;
  }

  public String getAddress() {
    return address;
  }

  public void setAddress(String address) {
    this.address = address;
  }

  public String getDate() {
    return date;
  }

  public void setDate(String date) {
    this.date = date;
  }

  public String getPurpose() {
    return purpose;
  }

  public void setPurpose(String purpose) {
    this.purpose = purpose;
  }

  public int getReceiptType() {
    return receiptType;
  }

  public void setReceiptType(int receiptType) {
    this.receiptType = receiptType;
  }

  public int getPurposeType() {
    return purposeType;
  }

  public void setPurposeType(int purposeType) {
    this.purposeType = purposeType;
  }

  public String getAddressType() {
    return addressType;
  }

  public void setAddressType(String addressType) {
    this.addressType = addressType;
  }

  public boolean isRegistdedSuccessed() {
    return isRegisteSuccessed;
  }

  public void setRegistedSuccessed(boolean registedSuccessed) {
    isRegisteSuccessed = registedSuccessed;
  }

  public String getSn() {
    return sn;
  }

  public void setSn(String sn) {
    this.sn = sn;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
    notifyPropertyChanged(BR.name);
  }

  public String getPhotoUrl() {
    return photoUrl;
  }

  public void setPhotoUrl(String photoUrl) {
    this.photoUrl = photoUrl;
  }

  public String getDeviceid() {
    return deviceid;
  }

  public void setDeviceid(String deviceid) {
    this.deviceid = deviceid;
  }

  public QuestionPageData getQuestionPageData() {
    return questionPageData;
  }

  public void setQuestionPageData(QuestionPageData questionPageData) {
    this.questionPageData = questionPageData;
  }

  public int getState() {
    return state;
  }

  public void setState(int state) {
    this.state = state;
  }


  public boolean isRegistMore() {
    return isRegistMore;
  }

  public void setRegistMore(boolean registMore) {
    isRegistMore = registMore;
  }

  public boolean checkSnHasRegistered() {
    return checkSnHasRegistered;
  }

  public void setCheckSnHasRegistered(boolean checkSnHasRegistered) {
    this.checkSnHasRegistered = checkSnHasRegistered;
  }

  public List<String> getRecipKey() {
    return recipKey;
  }

  public void setRecipKey(List<String> recipKey) {
    this.recipKey = recipKey;
  }

  public int getIfCheckedWarranty() {
    return ifCheckedWarranty;
  }

  public void setIfCheckedWarranty(int ifCheckedWarranty) {
    this.ifCheckedWarranty = ifCheckedWarranty;
  }

  public List<String> getUrl() {
    return url;
  }

  public void setUrl(List<String> url) {
    this.url = url;
  }

  public boolean isShowlostReceipt() {
    return showlostReceipt;
  }

  public void setShowlostReceipt(boolean showlostReceipt) {
    this.showlostReceipt = showlostReceipt;
  }

  public boolean isShowQuestionPage() {
    return showQuestionPage;
  }

  public void setShowQuestionPage(boolean showQuestionPage) {
    this.showQuestionPage = showQuestionPage;
  }

  public boolean isCbAnswer() {
    return cbAnswer;
  }

  public void setCbAnswer(boolean cbAnswer) {
    this.cbAnswer = cbAnswer;
  }

  public boolean isShowEnterSN() {
    return showEnterSN;
  }

  public void setShowEnterSN(boolean showEnterSN) {
    this.showEnterSN = showEnterSN;
  }

  public boolean isHasQuestionChecked() {
    return hasQuestionChecked;
  }

  public void setHasQuestionChecked(boolean hasQuestionChecked) {
    this.hasQuestionChecked = hasQuestionChecked;
  }

  public boolean showSNRegisteredTips() {
    return showSNRegisteredTips;
  }

  public void setShowSNRegisteredTips(boolean showSNRegisteredTips) {
    this.showSNRegisteredTips = showSNRegisteredTips;
  }

  public boolean isShowGiftResidential() {
    return showGiftResidential;
  }

  public void setShowGiftResidential(boolean showGiftResidential) {
    this.showGiftResidential = showGiftResidential;
  }

  public void setSnValidateStatus(DeviceSnStatus snValidateStatus) {
    this.snValidateStatus = snValidateStatus;
  }

  public DeviceSnStatus getSnValidateStatus() {
    return snValidateStatus;
  }

  public boolean isHasKit() {
    return hasKit;
  }

  public void setHasKit(boolean hasKit) {
    this.hasKit = hasKit;
    notifyPropertyChanged(BR.hasKit);
  }


  public String getRegisteredWarnTips() {
    return registeredWarnTips;
  }

  public void setRegisteredWarnTips(String warn) {
    this.registeredWarnTips = warn;
  }

  public List<String> getKitSnAsList() {
    ArrayList< String> arrayList = new ArrayList<>();
    for (DeviceSnStatus status : kitSN) {
      if(status.getSn() != null && !status.getSn().isEmpty()) {
        //2024/09/24需求sn转换成大写
        arrayList.add(status.getSn().toUpperCase());
      }
    }
    return arrayList;
  }

  public DeviceSnStatus[] getKitSN() {
    return kitSN;
  }

  public String getKitSN(int index) {
    if(index<kitSN.length && index>=0) {
      return kitSN[index].getSn();
    } else {
      return "";
    }
  }

  public String getKitSNMessage(int index) {
    if(index<kitSN.length && index>=0) {
      return kitSN[index].getMessage();
    } else {
      return "";
    }
  }
  public boolean setKitSN(String sn, int index) {
    if(index<kitSN.length && index>=0) {
      this.kitSN[index].setSn(sn);
      notifyPropertyChanged(BR.kitSN);
      return true;
    } else {
      return false;
    }
  }

  public boolean setKitMessage(String message, int index) {
    if(index<kitSN.length && index>=0) {
      this.kitSN[index].setMessage(message);
      notifyPropertyChanged(BR.kitSN);
      return true;
    } else {
      return false;
    }
  }

  public DeviceRegisterRes getDeviceRegisterRes() {
    return deviceRegisterRes;
  }

  public void setDeviceRegisterRes(DeviceRegisterRes deviceRegisterRes) {
    this.deviceRegisterRes = deviceRegisterRes;
  }
}
