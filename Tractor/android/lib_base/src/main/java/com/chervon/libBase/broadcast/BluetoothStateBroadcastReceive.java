package com.chervon.libBase.broadcast;

import android.bluetooth.BluetoothAdapter;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.blankj.utilcode.util.LogUtils;

import org.greenrobot.eventbus.EventBus;

/**
 * @Author: 184862
 * @desc 监听蓝牙开关广播--基于Android14适配
 * @CreateDate: 2024/8/8
 * @UpdateDate: 2024/8/8
 */
public class BluetoothStateBroadcastReceive extends BroadcastReceiver {
    private final String TAG = "BluetoothStateBroadcastReceive";
    //蓝牙打开
    public static final int BLE_ON = 1;
    //蓝牙关闭
    public static final int BLE_OFF = 0;

    public BluetoothStateBroadcastReceive() {
    }


    @Override
    public void onReceive(Context context, Intent intent) {
        String action = intent.getAction();
        if (BluetoothAdapter.ACTION_STATE_CHANGED.equals(action)) {
            int blueState = intent.getIntExtra(BluetoothAdapter.EXTRA_STATE, 0);
            switch (blueState) {
                case BluetoothAdapter.STATE_OFF:

                    EventBus.getDefault().postSticky(new BleStateChangeEvent(BLE_OFF));
                    LogUtils.i(TAG,"BluetoothAdapter.STATE_OFF");
                    break;
                case BluetoothAdapter.STATE_ON:
                    LogUtils.i(TAG,"BluetoothAdapter.STATE_ON");
                    EventBus.getDefault().postSticky(new BleStateChangeEvent(BLE_ON));
                    break;
                default:
                    break;
            }
        }
    }

}