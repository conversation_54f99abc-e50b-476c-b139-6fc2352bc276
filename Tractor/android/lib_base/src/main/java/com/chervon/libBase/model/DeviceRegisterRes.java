package com.chervon.libBase.model;

import java.io.Serializable;

public class DeviceRegisterRes implements Serializable {
    public String partId;
    public String partModel;
    public String partName;
    public String imageUrl;
    public String shopUrl;

    public DeviceRegisterRes() {
    }

    public DeviceRegisterRes(String partId, String partModel, String partName, String imageUrl, String shopUrl) {
        this.partId = partId;
        this.partModel = partModel;
        this.partName = partName;
        this.imageUrl = imageUrl;
        this.shopUrl = shopUrl;
    }

    public String getPartId() {
        return partId;
    }

    public void setPartId(String partId) {
        this.partId = partId;
    }

    public String getPartModel() {
        return partModel;
    }

    public void setPartModel(String partModel) {
        this.partModel = partModel;
    }

    public String getPartName() {
        return partName;
    }

    public void setPartName(String partName) {
        this.partName = partName;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getShopUrl() {
        return shopUrl;
    }

    public void setShopUrl(String shopUrl) {
        this.shopUrl = shopUrl;
    }
}
