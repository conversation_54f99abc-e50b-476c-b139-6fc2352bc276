package com.chervon.libBase.utils.mlang;

import android.annotation.SuppressLint;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.blankj.utilcode.util.GsonUtils;
import com.blankj.utilcode.util.LogUtils;
import com.chervon.libBase.BaseApplication;
import com.chervon.libDB.SoftRoomDatabase;
import com.chervon.libDB.entities.LanguageInfo;
import com.chervon.libDB.entities.LanguagePackage;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.timecat.component.locale.LangAction;
import com.timecat.component.locale.model.LangPackDifference;
import com.timecat.component.locale.model.LangPackLanguage;
import com.timecat.component.locale.model.LangPackString;

import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.reactivex.Observable;
import io.reactivex.Observer;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Function;
import io.reactivex.schedulers.Schedulers;

/**
 * @ProjectName: app
 * @Package: com.chervon.libBase.utils.mlang
 * @ClassName: MyLangAction
 * @Description: 语言动作实现 如果有需要的话，可以在这里注入 Context 也可以注入其他用于线程控制的对象
 * @Author: LangMeng
 * @CreateDate: 2022/4/21 19:19
 * @UpdateUser: LangMeng
 * @UpdateDate: 2022/4/21 19:19
 * @UpdateRemark: new class
 * @Version: 1.0
 */
public class MyLangAction implements LangAction {

    @Override
    public void saveLanguageKeyInLocal(String language) {
        MyLang.saveLanguageKeyInLocal(language);
    }

    @Nullable
    @Override
    public String loadLanguageKeyInLocal() {
        return MyLang.loadLanguageKeyInLocal();
    }

    @Override
    public void langpack_getDifference(String lang_pack, String lang_code, int from_version, @NonNull final LangAction.GetDifferenceCallback callback) {
        Observable.just(1)
                .map(new Function<Integer, LangPackDifference>() {
                    @Override
                    public LangPackDifference apply(Integer integer) throws Exception {

                        LanguagePackage languagePackage = SoftRoomDatabase
                                .getDatabase(BaseApplication.getInstance().getApplicationContext())
                                .languagePackageDao().getLanguageByCode(lang_code);

                        Map<String, String> app = languagePackage.getApp();
                        ArrayList<LangPackString> langPackStrings = new ArrayList<>();

                        for (String key : app.keySet()) {
                            langPackStrings.add(new LangPackString(key, app.get(key)));
                        }

                        LangPackDifference difference = new LangPackDifference();
                        difference.from_version = from_version;
                        difference.lang_code = lang_code;
                        difference.version = 0;
                        difference.strings = langPackStrings;
                        return difference;
                    }
                })
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Observer<LangPackDifference>() {
                    @Override
                    public void onSubscribe(Disposable d) {

                    }

                    @Override
                    public void onNext(LangPackDifference langPackDifference) {
                        callback.onLoad(langPackDifference);
                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onComplete() {

                    }
                });

    }

    @Override
    public void langpack_getLanguages(@NonNull final LangAction.GetLanguagesCallback callback) {

        Observable.just(1)
                .map(new Function<Integer, List<LangPackLanguage>>() {
                    @Override
                    public List<LangPackLanguage> apply(Integer integer) throws Exception {

                        List<LanguageInfo> allLanguageInfo = SoftRoomDatabase
                                .getDatabase(BaseApplication.getInstance().getApplicationContext())
                                .languageInfoDao().getAllLanguageInfo();


                        //数据转换
                        ArrayList<LangPackLanguage> langPackLanguages = new ArrayList<>();
                        if (allLanguageInfo != null) {
                            for (LanguageInfo languageInfo : allLanguageInfo) {
                                LangPackLanguage langPackLanguage = new LangPackLanguage();
                                langPackLanguage.name = languageInfo.getContent();
                                langPackLanguage.native_name = languageInfo.getContent();
                                langPackLanguage.lang_code = languageInfo.getType();
                                langPackLanguage.base_lang_code = languageInfo.getType();
                                langPackLanguages.add(langPackLanguage);
                            }
                        }
                        return langPackLanguages;
                    }
                })
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Observer<List<LangPackLanguage>>() {
                    @Override
                    public void onSubscribe(Disposable d) {

                    }

                    @Override
                    public void onNext(List<LangPackLanguage> langPackLanguages) {
                        callback.onLoad(langPackLanguages);
                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onComplete() {

                    }
                });
    }

    @Override
    public void langpack_getLangPack(String lang_code, @NonNull final LangAction.GetLangPackCallback callback) {
        Observable.just(1)
                .map(new Function<Integer, LangPackDifference>() {
                    @Override
                    public LangPackDifference apply(Integer integer) throws Exception {

                        LanguagePackage languagePackage = SoftRoomDatabase
                                .getDatabase(BaseApplication.getInstance().getApplicationContext())
                                .languagePackageDao().getLanguageByCode(lang_code);

                        Map<String, String> app = languagePackage.getApp();
                        ArrayList<LangPackString> langPackStrings = new ArrayList<>();

                        for (String key : app.keySet()) {
                            //对特殊字符进行转义，否则xml解析器无法解析
                            langPackStrings.add(new LangPackString(key, app.get(key)
                                    .replace("&","&amp;")
                                    .replace("<","&lt;")
                                    .replace(">","&gt;")
                                    .replace("'","&apos;")
                                    .replace("\"","&quot;")));
                        }

                        LangPackDifference difference = new LangPackDifference();
                        difference.from_version = 0;
                        difference.lang_code = lang_code;
                        difference.version = 0;
                        difference.strings = langPackStrings;
                        return difference;
                    }
                })
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Observer<LangPackDifference>() {
                    @Override
                    public void onSubscribe(Disposable d) {

                    }

                    @Override
                    public void onNext(LangPackDifference langPackDifference) {
                        callback.onLoad(langPackDifference);
                    }

                    @Override
                    public void onError(Throwable e) {

                    }

                    @Override
                    public void onComplete() {

                    }
                });
    }
}

