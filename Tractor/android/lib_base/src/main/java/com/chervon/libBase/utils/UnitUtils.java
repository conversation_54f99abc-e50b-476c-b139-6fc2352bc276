package com.chervon.libBase.utils;

import android.text.TextUtils;

/**
 * @Author: 184862
 * @CreateDate: 2024/8/20
 * @UpdateDate: 2024/8/20
 * @desc 英制和公制转换
 */
public class UnitUtils {

    /**
     * 将米转换成KM
     * @param value 米
     * @return
     */
    public static Double getKmWithMeters(String value) {
        try {
            return Double.parseDouble(value)/1000;
        }catch (Exception e){
            return 0.0;
        }
    }

    /**
     * 将KM转换成米
     * @param value -km
     * @return
     */
    public static Double getMetersWithKm(String value) {

        try {
            return Double.parseDouble(value)*1000;
        }catch (Exception e){
            return 0.0;
        }
    }

    /**
     * 将米转换成英里mi
     * @param value 米
     * @return
     */
    public static Double getMiWithMeters(String value) {
        try {
            return Double.parseDouble(value) / 1609;
        }catch (Exception e){
            return 0.0;
        }
    }

    /**
     * 将英里mi转换成米
     * @param value 米
     * @return
     */
    public static Double getMetersWithMi(String value) {

        try {
            return Double.parseDouble(value) * 1609;
        }catch (Exception e){
            return 0.0;
        }
    }
}
