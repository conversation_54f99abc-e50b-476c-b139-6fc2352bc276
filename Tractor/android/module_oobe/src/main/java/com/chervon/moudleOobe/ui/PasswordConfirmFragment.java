package com.chervon.moudleOobe.ui;

import static com.chervon.libBase.utils.Utils.sendClickTrace;
import static com.chervon.libRouter.RouterConstants.KEY_EMAIL;
import static com.chervon.libRouter.RouterConstants.KEY_PREV_DATA;
import static com.chervon.moudleOobe.ui.ForgetPasswordFragment.INIT_STATE;
import static com.chervon.moudleOobe.ui.LoginFragment.LOGIN_PASSWORD_ERROR;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;

import com.blankj.utilcode.util.ToastUtils;

import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.Observer;
import androidx.navigation.NavController;
import androidx.navigation.Navigation;

import com.alibaba.android.arouter.launcher.ARouter;
import com.chervon.libBase.model.UserInfo;
import com.chervon.libBase.ui.BaseEnhanceFragment;
import com.chervon.libBase.ui.BaseUistate;
import com.chervon.libBase.ui.widget.InlineVerificationHelper2;
import com.chervon.libBase.ui.widget.ProgressHelper;
import com.chervon.libBase.utils.mlang.LanguageStrings;
import com.chervon.libRouter.RouterConstants;
import com.chervon.moudleOobe.R;
import com.chervon.moudleOobe.databinding.MoudleOobeFragmentPasswordConfirmBinding;
import com.chervon.moudleOobe.ui.state.ForgetPasswordUiSate;
import com.chervon.moudleOobe.ui.viewmodel.PasswordConfirmViewModel;
import com.chervon.libBase.ui.widget.InlineTipEditText2;

/**
 * @ProjectName: app
 * @Package: com.chervon.moudleOobe.ui
 * @ClassName: PasswordConfirmFragment
 * @Description: Password modification interface for forgetting password
 * @Author: wangheng
 * @CreateDate: 2022/4/21 下午7:00
 * @UpdateUser: wangheng
 * @UpdateDate: 2022/4/21 下午7:00
 * @UpdateRemark:
 * @Version: 1.0
 */
public class PasswordConfirmFragment extends BaseEnhanceFragment<PasswordConfirmViewModel, MoudleOobeFragmentPasswordConfirmBinding> implements BaseEnhanceFragment.OnkeyBackListener {
  public static final String TAG = "PasswordConfirmFragment";
  public static final int PASSWORD_NOT_MATCH = 2;
  public static final int RESPONSE_SUCCESS = 1;
  public static final int RESPONSE_FAIL = 3;
  public static final String FORGET_PASSWORD_CONFIRM = "ForgetPasswordConfirm";
  private InlineVerificationHelper2 mInlineVerificationHelper;
  private String eMail = null;

  @Override
  protected void initDatas(Bundle savedInstanceState) {
    Bundle bundle = getArguments();

    if (null==bundle){
      return;
    }

    ForgetPasswordUiSate uiSate = (ForgetPasswordUiSate) bundle.getSerializable(KEY_PREV_DATA);

    if (null != uiSate) {
      if (!TextUtils.isEmpty(uiSate.getAccount())) {
        eMail = TextUtils.isEmpty(uiSate.getEmail())?uiSate.getAccount():uiSate.getEmail();
      }
    }

    mInlineVerificationHelper = new InlineVerificationHelper2(mViewDataBinding.btnNext);
    mInlineVerificationHelper.setEmail(eMail)
      .buildPasswordChecker(mViewDataBinding.ietPassword)
      .buildPasswordCheckerWithSameChecker2(mViewDataBinding.ietConfirmPassword,
        mViewDataBinding.ietPassword, mViewDataBinding.ietConfirmPassword)
      .buildPasswordSameChecker(mViewDataBinding.ietPassword,
        mViewDataBinding.ietConfirmPassword);

    mViewDataBinding.ietPassword.setmTextChangedListenner(new InlineTipEditText2.TextChangedListenner() {
      @Override
      public void textChanged(String s) {
        ForgetPasswordUiSate uiState = mViewDataBinding.getUiState();
        uiState.setPassword(s);
        mInlineVerificationHelper.checkInline();
      }
    });


    mViewDataBinding.ietConfirmPassword.setmTextChangedListenner(new InlineTipEditText2.TextChangedListenner() {
      @Override
      public void textChanged(String s) {
        ForgetPasswordUiSate uiState = mViewDataBinding.getUiState();
        uiState.setConfirmPassword(s);
        mInlineVerificationHelper.checkInline();
      }
    });

    mViewDataBinding.setUiState(uiSate);
    mViewDataBinding.setPresenter(this);
    observeLivedata();
    if (uiSate.getFrom() == 0) {
      NavController controller = Navigation.findNavController(getActivity(), R.id.nav_host_fragment_content_main);
      controller.getCurrentDestination().setLabel(FORGET_PASSWORD_CONFIRM);

      initForgetPageTrace();
    } else if (uiSate.getFrom() == R.id.changepassword) {
      initChangPasswordPageTrace();
    } else {
      initChangePasswordPageTrace();
    }




  }

  private void observeLivedata() {
    mViewModel.mPasswordConfirmLiveData.observe(this, new Observer<ForgetPasswordUiSate>() {
      @Override
      public void onChanged(ForgetPasswordUiSate forgetPasswordUiSate) {
        ProgressHelper.hideProgressView(PasswordConfirmFragment.this.getContext());
        if (forgetPasswordUiSate.getState() == RESPONSE_SUCCESS) {
          if (forgetPasswordUiSate.getFrom() == R.id.changepassword) {
            UserInfo.clear();
            ARouter.getInstance().build(RouterConstants.ACTIVITY_URL_LOGIN).withFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP).navigation();
            getActivity().finish();
          } else {
            NavController controller = Navigation.findNavController(getActivity(), R.id.nav_host_fragment_content_main);
            Bundle bundle = new Bundle();
            bundle.putString(KEY_EMAIL, forgetPasswordUiSate.getAccount());
            controller.navigate(R.id.action_passwordConfirmFragment_to_forgetResetSuccessFragment, bundle);
          }
        } else if (forgetPasswordUiSate.getState() == LOGIN_PASSWORD_ERROR) {
         // ToastUtils.showLong(getString(com.chervon.libBase.R.string.password_range));
        } else if (forgetPasswordUiSate.getState() == PASSWORD_NOT_MATCH) {
          ToastUtils.showLong(LanguageStrings.app_password_comfirm_mismatch_text());
        } else if (forgetPasswordUiSate.getState() != INIT_STATE) {
          ToastUtils.showLong(forgetPasswordUiSate.getRespondMessage());
        }

        forgetPasswordUiSate.setState(INIT_STATE);
      }
    });
  }

  private void initChangePasswordPageTrace() {
    stayEleId = "4";
    pageId = "8";
    mouduleId = "4";
    pageResouce = "1_7_8";
    nextButtoneleid = "2";
  }

  private void initForgetPageTrace() {
    stayEleId = "3";
    pageId = "5";
    mouduleId = "3";
    pageResouce = "1_2_3_4_5";
    nextButtoneleid = "2";
  }

  @Override
  protected int getLayoutId() {
    return R.layout.moudle_oobe_fragment_password_confirm;
  }

  @Override
  protected void initViews(ViewDataBinding viewDataBinding) {

  }


  @Override
  protected void onUnRegister() {

  }

  @Override
  protected void onRegister() {

  }

  @Override
  protected void onLowMemoryProcessed() {

  }

  @Override
  protected Class<? extends PasswordConfirmViewModel> getViewModelClass() {
    return PasswordConfirmViewModel.class;
  }

  public void onClickEvent(ForgetPasswordUiSate uiSate) {
    sendButtonClick();
    if (isAdded()) {
      ProgressHelper.showProgressView(this.getActivity(), 0);
    }
    mViewModel.onClickEvent(uiSate);
  }

  @Override
  protected void onUiLiveDataChanged(BaseUistate uiState) {

  }

  @Override
  protected LifecycleOwner getLifecycleOwner() {
    return null;
  }

  private void sendButtonClick() {
    sendClickTrace(this.getContext(), mouduleId, pageId, pageResouce, nextButtoneleid, "1");
  }

  @Override
  public boolean OnkeyBack() {
    sendClickTrace(this.getContext(), mouduleId, pageId, pageResouce, "1", "1");
    return false;
  }


  private void initChangPasswordPageTrace() {
    stayEleId = "3";
    pageId = "158";
    mouduleId = "11";
    pageResouce = "1_9_88_155_157_158";
    nextButtoneleid = "2";
  }

  @Override
  public void onStop() {
    super.onStop();
    mViewDataBinding.ietConfirmPassword.setmTextChangedListenner(null);
    mViewDataBinding.ietConfirmPassword.setEditTextOnFocusChangeListener(null);

    mViewDataBinding.ietPassword.setmTextChangedListenner(null);
    mViewDataBinding.ietPassword.setEditTextOnFocusChangeListener(null);
  }

  @Override
  public void onDestroyView() {
    if (null!=mInlineVerificationHelper){
      mInlineVerificationHelper.clear();
    }
    mInlineVerificationHelper = null;
    super.onDestroyView();
  }
}
